# 澳门葡京新彩 - 部署指南

## 🚀 快速部署

### 1. 环境准备
- [x] Node.js 16+ 已安装
- [x] npm 8+ 已安装
- [x] Git 已安装
- [ ] Docker Desktop (可选)

### 2. 项目部署

#### 方式一：本地开发部署 (推荐)

```bash
# 1. 克隆项目
git clone <repository-url>
cd lottery-website

# 2. 安装后端依赖
cd html/backend
npm install
cd ../..

# 3. 安装前端依赖
cd html/frontend
npm install
cd ../..

# 4. 启动应用
.\start-app.ps1  # Windows
# 或
./start-app.sh   # Linux/Mac
```

#### 方式二：Docker部署

```bash
# 1. 启动所有服务
docker-compose up --build

# 2. 仅启动开发服务 (不包含Nginx)
docker-compose up backend frontend

# 3. 生产环境 (包含Nginx)
docker-compose --profile production up
```

## 📋 部署检查清单

### 基础环境
- [ ] Node.js 版本 >= 16.0.0
- [ ] npm 版本 >= 8.0.0
- [ ] 端口 3000 和 3001 可用
- [ ] 防火墙允许相关端口

### 后端服务
- [ ] 依赖安装完成 (`npm install`)
- [ ] SQLite 数据库文件创建
- [ ] 环境变量配置正确
- [ ] API 接口响应正常
- [ ] Socket.IO 连接正常

### 前端服务
- [ ] 依赖安装完成 (`npm install`)
- [ ] 构建过程无错误
- [ ] 页面加载正常
- [ ] API 调用成功
- [ ] 实时功能正常

### Docker部署 (可选)
- [ ] Docker 和 Docker Compose 已安装
- [ ] 镜像构建成功
- [ ] 容器启动正常
- [ ] 服务间通信正常
- [ ] 数据持久化配置

## 🔧 配置说明

### 环境变量

#### 后端 (.env)
```env
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=lottery_db
DB_USER=root
DB_PASSWORD=

# 服务器配置
PORT=3001
NODE_ENV=development

# JWT密钥
JWT_SECRET=lottery_secret_key_2024

# CORS配置
CORS_ORIGIN=http://localhost:3000
```

#### 前端
```env
REACT_APP_API_URL=http://localhost:3001
```

### 数据库配置

项目默认使用 SQLite 数据库，无需额外配置。数据库文件位于：
- 开发环境: `html/backend/data/lottery.db`
- 生产环境: `/app/data/lottery.db`

如需使用 MySQL，请修改 `html/backend/src/models/index.js` 中的数据库配置。

## 🌐 网络配置

### 端口说明
- **3000**: 前端开发服务器
- **3001**: 后端API服务器
- **80**: Nginx反向代理 (生产环境)
- **443**: HTTPS (生产环境)

### 防火墙设置
确保以下端口在防火墙中开放：
```bash
# Windows
netsh advfirewall firewall add rule name="Lottery App" dir=in action=allow protocol=TCP localport=3000,3001

# Linux (ufw)
sudo ufw allow 3000
sudo ufw allow 3001
```

## 🔍 故障排除

### 常见问题

#### 1. 端口被占用
```bash
# 查找占用端口的进程
netstat -ano | findstr :3000
netstat -ano | findstr :3001

# 终止进程
taskkill /PID <进程ID> /F
```

#### 2. 依赖安装失败
```bash
# 清除npm缓存
npm cache clean --force

# 删除node_modules重新安装
rm -rf node_modules package-lock.json
npm install
```

#### 3. 数据库连接失败
- 检查SQLite文件权限
- 确认数据目录存在
- 查看错误日志

#### 4. 前端无法连接后端
- 检查后端服务是否启动
- 确认API地址配置正确
- 检查CORS设置

### 日志查看

#### 后端日志
```bash
cd html/backend
npm start
# 或查看Docker日志
docker logs lottery_backend
```

#### 前端日志
```bash
cd html/frontend
npm start
# 或查看浏览器控制台
```

## 📊 性能优化

### 生产环境优化

1. **前端优化**
   ```bash
   cd html/frontend
   npm run build
   ```

2. **启用Gzip压缩**
   - Nginx配置已包含Gzip设置

3. **静态资源缓存**
   - 配置CDN加速
   - 设置浏览器缓存

4. **数据库优化**
   - 定期清理历史数据
   - 添加必要索引
   - 配置数据库备份

### 监控配置

1. **健康检查**
   ```bash
   # 后端健康检查
   curl http://localhost:3001/health
   
   # 前端健康检查
   curl http://localhost:3000
   ```

2. **性能监控**
   - 配置应用性能监控 (APM)
   - 设置错误日志收集
   - 监控服务器资源使用

## 🔒 安全配置

### 生产环境安全

1. **HTTPS配置**
   ```nginx
   server {
       listen 443 ssl;
       ssl_certificate /path/to/cert.pem;
       ssl_certificate_key /path/to/key.pem;
   }
   ```

2. **环境变量安全**
   - 使用强密码
   - 定期更换JWT密钥
   - 限制数据库访问权限

3. **网络安全**
   - 配置防火墙规则
   - 启用DDoS防护
   - 设置访问频率限制

## 📞 技术支持

如遇到部署问题，请：
1. 查看本文档的故障排除部分
2. 检查项目日志文件
3. 联系技术支持团队

---

**部署完成后，请访问 http://localhost:3000 验证应用是否正常运行。**
