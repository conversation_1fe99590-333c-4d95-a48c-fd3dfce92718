# 澳门葡京新彩 - 彩票预测网站

基于 React + Node.js + SQLite 构建的现代化彩票预测平台，提供实时开奖结果、智能预测分析和历史数据查询功能。

## 🚀 功能特性

- **实时开奖**: Socket.IO 实时推送最新开奖结果
- **智能预测**: 18种不同类型的彩票预测算法
- **历史查询**: 完整的历史开奖数据和统计分析
- **响应式设计**: 支持桌面端和移动端访问
- **现代化UI**: 基于 Ant Design 的美观界面

## 🛠️ 技术栈

### 前端
- **React 18** - 现代化前端框架
- **TypeScript** - 类型安全的JavaScript
- **Ant Design** - 企业级UI组件库
- **Socket.IO Client** - 实时通信
- **Styled Components** - CSS-in-JS样式方案
- **React Router** - 单页应用路由

### 后端
- **Node.js** - JavaScript运行时
- **Express.js** - Web应用框架
- **Sequelize** - ORM数据库操作
- **SQLite** - 轻量级数据库
- **Socket.IO** - 实时双向通信
- **JWT** - 身份验证

### 部署
- **Docker** - 容器化部署
- **Docker Compose** - 多容器编排
- **Nginx** - 反向代理和静态文件服务

## 📦 快速开始

### 方式一：直接运行 (推荐)

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd lottery-website
   ```

2. **安装依赖**
   ```bash
   # 安装后端依赖
   cd html/backend
   npm install
   cd ../..
   
   # 安装前端依赖
   cd html/frontend
   npm install
   cd ../..
   ```

3. **启动应用**
   ```bash
   # Windows PowerShell
   .\start-app.ps1
   
   # 或手动启动
   # 后端 (新终端)
   cd html/backend && npm start
   
   # 前端 (新终端)
   cd html/frontend && npm start
   ```

4. **访问应用**
   - 前端: http://localhost:3000
   - 后端API: http://localhost:3001

### 方式二：Docker部署

1. **确保Docker已安装并运行**

2. **构建并启动服务**
   ```bash
   docker-compose up --build
   ```

3. **访问应用**
   - 应用: http://localhost:3000
   - API: http://localhost:3001

## 📁 项目结构

```
lottery-website/
├── html/
│   ├── backend/                 # 后端服务
│   │   ├── src/
│   │   │   ├── controllers/     # 控制器
│   │   │   ├── models/          # 数据模型
│   │   │   ├── routes/          # 路由定义
│   │   │   ├── services/        # 业务逻辑
│   │   │   └── app.js          # 应用入口
│   │   ├── data/               # SQLite数据库文件
│   │   ├── Dockerfile
│   │   └── package.json
│   │
│   └── frontend/               # 前端应用
│       ├── public/             # 静态资源
│       ├── src/
│       │   ├── components/     # React组件
│       │   ├── pages/          # 页面组件
│       │   ├── services/       # API服务
│       │   ├── types/          # TypeScript类型
│       │   └── App.tsx         # 应用根组件
│       ├── Dockerfile
│       └── package.json
│
├── docker-compose.yml          # Docker编排配置
├── start-app.ps1              # 启动脚本
├── stop-app.ps1               # 停止脚本
├── project-progress.md        # 项目进度
└── README.md                  # 项目文档
```

## 🔧 开发指南

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0
- Docker (可选)

### 开发模式
```bash
# 后端开发模式 (自动重启)
cd html/backend
npm run dev

# 前端开发模式 (热重载)
cd html/frontend
npm start
```

### 构建生产版本
```bash
# 前端构建
cd html/frontend
npm run build

# Docker构建
docker-compose build
```

## 📊 API文档

### 彩票相关
- `GET /api/lottery/current` - 获取当前期数信息
- `GET /api/lottery/history` - 获取历史开奖记录
- `GET /api/lottery/results/:id` - 获取指定期数结果

### 预测相关
- `GET /api/prediction/current` - 获取当前预测数据
- `GET /api/prediction/types` - 获取预测类型列表

### 配置相关
- `GET /api/config/zodiac` - 获取生肖配置
- `GET /api/config/colors` - 获取波色配置
- `GET /api/config/elements` - 获取五行配置

## 🔄 实时功能

应用使用 Socket.IO 提供以下实时功能：
- 开奖倒计时更新
- 新开奖结果推送
- 预测数据更新
- 在线用户统计
- 系统消息通知

## 🛡️ 安全特性

- JWT身份验证
- CORS跨域保护
- 请求频率限制
- 输入数据验证
- SQL注入防护

## 📱 移动端支持

- 响应式设计，完美适配移动设备
- PWA支持，可添加到主屏幕
- 触摸友好的交互设计

## 🚀 部署说明

### 生产环境部署
1. 修改环境变量配置
2. 构建前端生产版本
3. 配置Nginx反向代理
4. 启用HTTPS证书
5. 设置数据库备份

### 性能优化
- 启用Gzip压缩
- 配置静态资源缓存
- 数据库查询优化
- CDN加速

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 联系方式

如有问题，请联系开发团队。
