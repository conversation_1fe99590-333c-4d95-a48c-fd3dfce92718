# 澳门葡京新彩应用状态检查脚本
Write-Host "=== 澳门葡京新彩应用状态检查 ===" -ForegroundColor Green

# 检查后端状态
Write-Host "`n检查后端服务..." -ForegroundColor Cyan
try {
    $backendHealth = Invoke-RestMethod -Uri "http://localhost:3001/health" -Method GET -TimeoutSec 5
    Write-Host "✓ 后端服务正常运行" -ForegroundColor Green
    Write-Host "  - 状态: $($backendHealth.status)" -ForegroundColor Gray
    Write-Host "  - 运行时间: $([math]::Round($backendHealth.uptime, 2)) 秒" -ForegroundColor Gray
    
    # 测试API接口
    try {
        $apiResponse = Invoke-RestMethod -Uri "http://localhost:3001/api/lottery/current" -Method GET -TimeoutSec 5
        Write-Host "✓ API接口响应正常" -ForegroundColor Green
    } catch {
        if ($_.Exception.Response.StatusCode -eq 404 -or $_.ErrorDetails.Message -like "*当前没有待开奖期数*") {
            Write-Host "✓ API接口正常 (无待开奖期数)" -ForegroundColor Green
        } else {
            Write-Host "✗ API接口异常: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
} catch {
    Write-Host "X 后端服务未运行或无响应" -ForegroundColor Red
    Write-Host "  错误: $($_.Exception.Message)" -ForegroundColor Gray
}

# 检查前端状态
Write-Host "`n检查前端服务..." -ForegroundColor Cyan
try {
    $frontendResponse = Invoke-WebRequest -Uri "http://localhost:3000" -Method GET -TimeoutSec 5
    if ($frontendResponse.StatusCode -eq 200) {
        Write-Host "✓ 前端服务正常运行" -ForegroundColor Green
        Write-Host "  - 状态码: $($frontendResponse.StatusCode)" -ForegroundColor Gray
        Write-Host "  - 内容长度: $($frontendResponse.RawContentLength) 字节" -ForegroundColor Gray
        
        # 检查是否包含React应用标识
        if ($frontendResponse.Content -like "*react*" -or $frontendResponse.Content -like "*澳门葡京新彩*") {
            Write-Host "✓ React应用加载正常" -ForegroundColor Green
        }
    }
} catch {
    Write-Host "X 前端服务未运行或无响应" -ForegroundColor Red
    Write-Host "  错误: $($_.Exception.Message)" -ForegroundColor Gray
}

# 检查进程状态
Write-Host "`n检查进程状态..." -ForegroundColor Cyan
$nodeProcesses = Get-Process -Name "node" -ErrorAction SilentlyContinue
if ($nodeProcesses) {
    Write-Host "✓ 发现 $($nodeProcesses.Count) 个Node.js进程" -ForegroundColor Green
    foreach ($process in $nodeProcesses) {
        $cpuUsage = [math]::Round($process.CPU, 2)
        $memoryMB = [math]::Round($process.WorkingSet64 / 1MB, 2)
        Write-Host "  - PID: $($process.Id), CPU: ${cpuUsage}s, 内存: ${memoryMB}MB" -ForegroundColor Gray
    }
} else {
    Write-Host "X 未发现Node.js进程" -ForegroundColor Red
}

# 检查端口占用
Write-Host "`n检查端口占用..." -ForegroundColor Cyan
$port3000 = netstat -ano | findstr ":3000"
$port3001 = netstat -ano | findstr ":3001"

if ($port3000) {
    Write-Host "✓ 端口 3000 已被占用 (前端)" -ForegroundColor Green
} else {
    Write-Host "X 端口 3000 未被占用" -ForegroundColor Red
}

if ($port3001) {
    Write-Host "✓ 端口 3001 已被占用 (后端)" -ForegroundColor Green
} else {
    Write-Host "X 端口 3001 未被占用" -ForegroundColor Red
}

# 总结
Write-Host "`n=== 状态检查完成 ===" -ForegroundColor Green
Write-Host "如果服务正常运行，请访问:" -ForegroundColor Yellow
Write-Host "  前端应用: http://localhost:3000" -ForegroundColor Cyan
Write-Host "  后端API: http://localhost:3001" -ForegroundColor Cyan
Write-Host "  健康检查: http://localhost:3001/health" -ForegroundColor Cyan

Write-Host "`n如果服务未运行，请执行:" -ForegroundColor Yellow
Write-Host "  .\start-app.ps1" -ForegroundColor Cyan
