version: '3.8'

services:
  # MySQL数据库服务
  mysql:
    image: mysql:8.0
    container_name: lottery_mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: root123456
      MYSQL_DATABASE: lottery_db
      MYSQL_USER: lottery_user
      MYSQL_PASSWORD: lottery_pass
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./html/database:/docker-entrypoint-initdb.d
    networks:
      - lottery_network

  # 后端API服务
  backend:
    build:
      context: ./html/backend
      dockerfile: Dockerfile
    container_name: lottery_backend
    restart: always
    environment:
      NODE_ENV: production
      DB_HOST: mysql
      DB_PORT: 3306
      DB_NAME: lottery_db
      DB_USER: lottery_user
      DB_PASSWORD: lottery_pass
    ports:
      - "3001:3001"
    depends_on:
      - mysql
    volumes:
      - ./html/backend:/app
      - /app/node_modules
    networks:
      - lottery_network

  # 前端服务
  frontend:
    build:
      context: ./html/frontend
      dockerfile: Dockerfile
    container_name: lottery_frontend
    restart: always
    ports:
      - "3000:3000"
    environment:
      REACT_APP_API_URL: http://localhost:3001
    volumes:
      - ./html/frontend:/app
      - /app/node_modules
    networks:
      - lottery_network

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: lottery_nginx
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./html/frontend/build:/usr/share/nginx/html
    depends_on:
      - frontend
      - backend
    networks:
      - lottery_network

volumes:
  mysql_data:

networks:
  lottery_network:
    driver: bridge
