{"version": 3, "names": ["helpers", "data", "require", "_generator", "_template", "_t", "arrayExpression", "assignmentExpression", "binaryExpression", "blockStatement", "callExpression", "cloneNode", "conditionalExpression", "exportNamedDeclaration", "exportSpecifier", "expressionStatement", "functionExpression", "identifier", "memberExpression", "objectExpression", "program", "stringLiteral", "unaryExpression", "variableDeclaration", "variableDeclarator", "buildUmdWrapper", "replacements", "template", "statement", "buildGlobal", "allowlist", "namespace", "body", "container", "tree", "push", "buildHelpers", "buildModule", "refs", "unshift", "Object", "keys", "map", "name", "buildUmd", "FACTORY_PARAMETERS", "BROWSER_ARGUMENTS", "COMMON_ARGUMENTS", "AMD_ARGUMENTS", "FACTORY_BODY", "UMD_ROOT", "buildVar", "getHelperReference", "list", "for<PERSON>ach", "includes", "ref", "nodes", "get", "ast", "exportName", "mapExportBindingAssignments", "node", "_default", "outputType", "build", "global", "module", "umd", "var", "Error", "generator", "code"], "sources": ["../../src/tools/build-external-helpers.ts"], "sourcesContent": ["import * as helpers from \"@babel/helpers\";\nimport generator from \"@babel/generator\";\nimport template from \"@babel/template\";\nimport {\n  arrayExpression,\n  assignmentExpression,\n  binaryExpression,\n  blockStatement,\n  callExpression,\n  cloneNode,\n  conditionalExpression,\n  exportNamedDeclaration,\n  exportSpecifier,\n  expressionStatement,\n  functionExpression,\n  identifier,\n  memberExpression,\n  objectExpression,\n  program,\n  stringLiteral,\n  unaryExpression,\n  variableDeclaration,\n  variableDeclarator,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport type { Replacements } from \"@babel/template\";\n\n// Wrapped to avoid wasting time parsing this when almost no-one uses\n// build-external-helpers.\nconst buildUmdWrapper = (replacements: Replacements) =>\n  template.statement`\n    (function (root, factory) {\n      if (typeof define === \"function\" && define.amd) {\n        define(AMD_ARGUMENTS, factory);\n      } else if (typeof exports === \"object\") {\n        factory(COMMON_ARGUMENTS);\n      } else {\n        factory(BROWSER_ARGUMENTS);\n      }\n    })(UMD_ROOT, function (FACTORY_PARAMETERS) {\n      FACTORY_BODY\n    });\n  `(replacements);\n\nfunction buildGlobal(allowlist?: Array<string>) {\n  const namespace = identifier(\"babelHelpers\");\n\n  const body: t.Statement[] = [];\n  const container = functionExpression(\n    null,\n    [identifier(\"global\")],\n    blockStatement(body),\n  );\n  const tree = program([\n    expressionStatement(\n      callExpression(container, [\n        // typeof global === \"undefined\" ? self : global\n        conditionalExpression(\n          binaryExpression(\n            \"===\",\n            unaryExpression(\"typeof\", identifier(\"global\")),\n            stringLiteral(\"undefined\"),\n          ),\n          identifier(\"self\"),\n          identifier(\"global\"),\n        ),\n      ]),\n    ),\n  ]);\n\n  body.push(\n    variableDeclaration(\"var\", [\n      variableDeclarator(\n        namespace,\n        assignmentExpression(\n          \"=\",\n          memberExpression(identifier(\"global\"), namespace),\n          objectExpression([]),\n        ),\n      ),\n    ]),\n  );\n\n  buildHelpers(body, namespace, allowlist);\n\n  return tree;\n}\n\nfunction buildModule(allowlist?: Array<string>) {\n  const body: t.Statement[] = [];\n  const refs = buildHelpers(body, null, allowlist);\n\n  body.unshift(\n    exportNamedDeclaration(\n      null,\n      Object.keys(refs).map(name => {\n        return exportSpecifier(cloneNode(refs[name]), identifier(name));\n      }),\n    ),\n  );\n\n  return program(body, [], \"module\");\n}\n\nfunction buildUmd(allowlist?: Array<string>) {\n  const namespace = identifier(\"babelHelpers\");\n\n  const body: t.Statement[] = [];\n  body.push(\n    variableDeclaration(\"var\", [\n      variableDeclarator(namespace, identifier(\"global\")),\n    ]),\n  );\n\n  buildHelpers(body, namespace, allowlist);\n\n  return program([\n    buildUmdWrapper({\n      FACTORY_PARAMETERS: identifier(\"global\"),\n      BROWSER_ARGUMENTS: assignmentExpression(\n        \"=\",\n        memberExpression(identifier(\"root\"), namespace),\n        objectExpression([]),\n      ),\n      COMMON_ARGUMENTS: identifier(\"exports\"),\n      AMD_ARGUMENTS: arrayExpression([stringLiteral(\"exports\")]),\n      FACTORY_BODY: body,\n      UMD_ROOT: identifier(\"this\"),\n    }),\n  ]);\n}\n\nfunction buildVar(allowlist?: Array<string>) {\n  const namespace = identifier(\"babelHelpers\");\n\n  const body: t.Statement[] = [];\n  body.push(\n    variableDeclaration(\"var\", [\n      variableDeclarator(namespace, objectExpression([])),\n    ]),\n  );\n  const tree = program(body);\n  buildHelpers(body, namespace, allowlist);\n  body.push(expressionStatement(namespace));\n  return tree;\n}\n\nfunction buildHelpers(\n  body: t.Statement[],\n  namespace: t.Expression,\n  allowlist?: Array<string>,\n): Record<string, t.MemberExpression>;\nfunction buildHelpers(\n  body: t.Statement[],\n  namespace: null,\n  allowlist?: Array<string>,\n): Record<string, t.Identifier>;\n\nfunction buildHelpers(\n  body: t.Statement[],\n  namespace: t.Expression | null,\n  allowlist?: Array<string>,\n) {\n  const getHelperReference = (name: string) => {\n    return namespace\n      ? memberExpression(namespace, identifier(name))\n      : identifier(`_${name}`);\n  };\n\n  const refs: { [key: string]: t.Identifier | t.MemberExpression } = {};\n  helpers.list.forEach(function (name) {\n    if (allowlist && !allowlist.includes(name)) return;\n\n    const ref = (refs[name] = getHelperReference(name));\n\n    const { nodes } = helpers.get(\n      name,\n      getHelperReference,\n      namespace ? null : `_${name}`,\n      [],\n      namespace\n        ? (ast, exportName, mapExportBindingAssignments) => {\n            mapExportBindingAssignments(node =>\n              assignmentExpression(\"=\", ref, node),\n            );\n            ast.body.push(\n              expressionStatement(\n                assignmentExpression(\"=\", ref, identifier(exportName)),\n              ),\n            );\n          }\n        : null,\n    );\n\n    body.push(...nodes);\n  });\n  return refs;\n}\nexport default function (\n  allowlist?: Array<string>,\n  outputType: \"global\" | \"module\" | \"umd\" | \"var\" = \"global\",\n) {\n  let tree: t.Program;\n\n  const build = {\n    global: buildGlobal,\n    module: buildModule,\n    umd: buildUmd,\n    var: buildVar,\n  }[outputType];\n\n  if (build) {\n    tree = build(allowlist);\n  } else {\n    throw new Error(`Unsupported output type ${outputType}`);\n  }\n\n  return generator(tree).code;\n}\n"], "mappings": ";;;;;;AAAA,SAAAA,QAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,OAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,WAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,UAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,UAAA;EAAA,MAAAH,IAAA,GAAAC,OAAA;EAAAE,SAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,GAAA;EAAA,MAAAJ,IAAA,GAAAC,OAAA;EAAAG,EAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAoBsB;EAnBpBK,eAAe;EACfC,oBAAoB;EACpBC,gBAAgB;EAChBC,cAAc;EACdC,cAAc;EACdC,SAAS;EACTC,qBAAqB;EACrBC,sBAAsB;EACtBC,eAAe;EACfC,mBAAmB;EACnBC,kBAAkB;EAClBC,UAAU;EACVC,gBAAgB;EAChBC,gBAAgB;EAChBC,OAAO;EACPC,aAAa;EACbC,eAAe;EACfC,mBAAmB;EACnBC;AAAkB,IAAAnB,EAAA;AAOpB,MAAMoB,eAAe,GAAIC,YAA0B,IACjDC,mBAAQ,CAACC,SAAS;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,CAACF,YAAY,CAAC;AAEjB,SAASG,WAAWA,CAACC,SAAyB,EAAE;EAC9C,MAAMC,SAAS,GAAGd,UAAU,CAAC,cAAc,CAAC;EAE5C,MAAMe,IAAmB,GAAG,EAAE;EAC9B,MAAMC,SAAS,GAAGjB,kBAAkB,CAClC,IAAI,EACJ,CAACC,UAAU,CAAC,QAAQ,CAAC,CAAC,EACtBR,cAAc,CAACuB,IAAI,CACrB,CAAC;EACD,MAAME,IAAI,GAAGd,OAAO,CAAC,CACnBL,mBAAmB,CACjBL,cAAc,CAACuB,SAAS,EAAE,CAExBrB,qBAAqB,CACnBJ,gBAAgB,CACd,KAAK,EACLc,eAAe,CAAC,QAAQ,EAAEL,UAAU,CAAC,QAAQ,CAAC,CAAC,EAC/CI,aAAa,CAAC,WAAW,CAC3B,CAAC,EACDJ,UAAU,CAAC,MAAM,CAAC,EAClBA,UAAU,CAAC,QAAQ,CACrB,CAAC,CACF,CACH,CAAC,CACF,CAAC;EAEFe,IAAI,CAACG,IAAI,CACPZ,mBAAmB,CAAC,KAAK,EAAE,CACzBC,kBAAkB,CAChBO,SAAS,EACTxB,oBAAoB,CAClB,GAAG,EACHW,gBAAgB,CAACD,UAAU,CAAC,QAAQ,CAAC,EAAEc,SAAS,CAAC,EACjDZ,gBAAgB,CAAC,EAAE,CACrB,CACF,CAAC,CACF,CACH,CAAC;EAEDiB,YAAY,CAACJ,IAAI,EAAED,SAAS,EAAED,SAAS,CAAC;EAExC,OAAOI,IAAI;AACb;AAEA,SAASG,WAAWA,CAACP,SAAyB,EAAE;EAC9C,MAAME,IAAmB,GAAG,EAAE;EAC9B,MAAMM,IAAI,GAAGF,YAAY,CAACJ,IAAI,EAAE,IAAI,EAAEF,SAAS,CAAC;EAEhDE,IAAI,CAACO,OAAO,CACV1B,sBAAsB,CACpB,IAAI,EACJ2B,MAAM,CAACC,IAAI,CAACH,IAAI,CAAC,CAACI,GAAG,CAACC,IAAI,IAAI;IAC5B,OAAO7B,eAAe,CAACH,SAAS,CAAC2B,IAAI,CAACK,IAAI,CAAC,CAAC,EAAE1B,UAAU,CAAC0B,IAAI,CAAC,CAAC;EACjE,CAAC,CACH,CACF,CAAC;EAED,OAAOvB,OAAO,CAACY,IAAI,EAAE,EAAE,EAAE,QAAQ,CAAC;AACpC;AAEA,SAASY,QAAQA,CAACd,SAAyB,EAAE;EAC3C,MAAMC,SAAS,GAAGd,UAAU,CAAC,cAAc,CAAC;EAE5C,MAAMe,IAAmB,GAAG,EAAE;EAC9BA,IAAI,CAACG,IAAI,CACPZ,mBAAmB,CAAC,KAAK,EAAE,CACzBC,kBAAkB,CAACO,SAAS,EAAEd,UAAU,CAAC,QAAQ,CAAC,CAAC,CACpD,CACH,CAAC;EAEDmB,YAAY,CAACJ,IAAI,EAAED,SAAS,EAAED,SAAS,CAAC;EAExC,OAAOV,OAAO,CAAC,CACbK,eAAe,CAAC;IACdoB,kBAAkB,EAAE5B,UAAU,CAAC,QAAQ,CAAC;IACxC6B,iBAAiB,EAAEvC,oBAAoB,CACrC,GAAG,EACHW,gBAAgB,CAACD,UAAU,CAAC,MAAM,CAAC,EAAEc,SAAS,CAAC,EAC/CZ,gBAAgB,CAAC,EAAE,CACrB,CAAC;IACD4B,gBAAgB,EAAE9B,UAAU,CAAC,SAAS,CAAC;IACvC+B,aAAa,EAAE1C,eAAe,CAAC,CAACe,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC;IAC1D4B,YAAY,EAAEjB,IAAI;IAClBkB,QAAQ,EAAEjC,UAAU,CAAC,MAAM;EAC7B,CAAC,CAAC,CACH,CAAC;AACJ;AAEA,SAASkC,QAAQA,CAACrB,SAAyB,EAAE;EAC3C,MAAMC,SAAS,GAAGd,UAAU,CAAC,cAAc,CAAC;EAE5C,MAAMe,IAAmB,GAAG,EAAE;EAC9BA,IAAI,CAACG,IAAI,CACPZ,mBAAmB,CAAC,KAAK,EAAE,CACzBC,kBAAkB,CAACO,SAAS,EAAEZ,gBAAgB,CAAC,EAAE,CAAC,CAAC,CACpD,CACH,CAAC;EACD,MAAMe,IAAI,GAAGd,OAAO,CAACY,IAAI,CAAC;EAC1BI,YAAY,CAACJ,IAAI,EAAED,SAAS,EAAED,SAAS,CAAC;EACxCE,IAAI,CAACG,IAAI,CAACpB,mBAAmB,CAACgB,SAAS,CAAC,CAAC;EACzC,OAAOG,IAAI;AACb;AAaA,SAASE,YAAYA,CACnBJ,IAAmB,EACnBD,SAA8B,EAC9BD,SAAyB,EACzB;EACA,MAAMsB,kBAAkB,GAAIT,IAAY,IAAK;IAC3C,OAAOZ,SAAS,GACZb,gBAAgB,CAACa,SAAS,EAAEd,UAAU,CAAC0B,IAAI,CAAC,CAAC,GAC7C1B,UAAU,CAAC,IAAI0B,IAAI,EAAE,CAAC;EAC5B,CAAC;EAED,MAAML,IAA0D,GAAG,CAAC,CAAC;EACrEtC,OAAO,CAAD,CAAC,CAACqD,IAAI,CAACC,OAAO,CAAC,UAAUX,IAAI,EAAE;IACnC,IAAIb,SAAS,IAAI,CAACA,SAAS,CAACyB,QAAQ,CAACZ,IAAI,CAAC,EAAE;IAE5C,MAAMa,GAAG,GAAIlB,IAAI,CAACK,IAAI,CAAC,GAAGS,kBAAkB,CAACT,IAAI,CAAE;IAEnD,MAAM;MAAEc;IAAM,CAAC,GAAGzD,OAAO,CAAD,CAAC,CAAC0D,GAAG,CAC3Bf,IAAI,EACJS,kBAAkB,EAClBrB,SAAS,GAAG,IAAI,GAAG,IAAIY,IAAI,EAAE,EAC7B,EAAE,EACFZ,SAAS,GACL,CAAC4B,GAAG,EAAEC,UAAU,EAAEC,2BAA2B,KAAK;MAChDA,2BAA2B,CAACC,IAAI,IAC9BvD,oBAAoB,CAAC,GAAG,EAAEiD,GAAG,EAAEM,IAAI,CACrC,CAAC;MACDH,GAAG,CAAC3B,IAAI,CAACG,IAAI,CACXpB,mBAAmB,CACjBR,oBAAoB,CAAC,GAAG,EAAEiD,GAAG,EAAEvC,UAAU,CAAC2C,UAAU,CAAC,CACvD,CACF,CAAC;IACH,CAAC,GACD,IACN,CAAC;IAED5B,IAAI,CAACG,IAAI,CAAC,GAAGsB,KAAK,CAAC;EACrB,CAAC,CAAC;EACF,OAAOnB,IAAI;AACb;AACe,SAAAyB,SACbjC,SAAyB,EACzBkC,UAA+C,GAAG,QAAQ,EAC1D;EACA,IAAI9B,IAAe;EAEnB,MAAM+B,KAAK,GAAG;IACZC,MAAM,EAAErC,WAAW;IACnBsC,MAAM,EAAE9B,WAAW;IACnB+B,GAAG,EAAExB,QAAQ;IACbyB,GAAG,EAAElB;EACP,CAAC,CAACa,UAAU,CAAC;EAEb,IAAIC,KAAK,EAAE;IACT/B,IAAI,GAAG+B,KAAK,CAACnC,SAAS,CAAC;EACzB,CAAC,MAAM;IACL,MAAM,IAAIwC,KAAK,CAAC,2BAA2BN,UAAU,EAAE,CAAC;EAC1D;EAEA,OAAO,IAAAO,oBAAS,EAACrC,IAAI,CAAC,CAACsC,IAAI;AAC7B;AAAC", "ignoreList": []}