{"name": "lottery-backend", "version": "1.0.0", "description": "彩票网站后端API服务", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "keywords": ["lottery", "api", "nodejs", "express", "mysql"], "author": "Lottery Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.0", "sequelize": "^6.32.1", "socket.io": "^4.7.2", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-rate-limit": "^6.8.1", "express-validator": "^7.0.1", "moment": "^2.29.4", "node-cron": "^3.0.2", "compression": "^1.7.4"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3", "eslint": "^8.45.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-n": "^16.0.1", "eslint-plugin-promise": "^6.1.1"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}