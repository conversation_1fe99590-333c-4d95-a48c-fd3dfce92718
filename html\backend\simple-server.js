const express = require('express');
const http = require('http');
const cors = require('cors');

const app = express();
const server = http.createServer(app);
const PORT = 3001;

// 中间件配置
app.use(cors({
  origin: "http://localhost:3000",
  credentials: true
}));

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 简单的API路由
app.get('/', (req, res) => {
  res.json({
    message: '澳门葡京新彩 API',
    version: '1.0.0',
    timestamp: new Date().toISOString()
  });
});

app.get('/api', (req, res) => {
  res.json({
    message: '澳门葡京新彩 API',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
    endpoints: {
      lottery: '/api/lottery',
      prediction: '/api/prediction',
      config: '/api/config'
    }
  });
});

// 彩票相关API
app.get('/api/lottery/current', (req, res) => {
  res.json({
    success: true,
    data: {
      currentPeriod: {
        id: 1,
        period_number: 188,
        draw_time: new Date(Date.now() + 5 * 60 * 1000).toISOString(),
        status: 'pending'
      },
      timeUntilDraw: 300000, // 5分钟
      latestResult: {
        period_number: 187,
        numbers: [26, 41, 7, 27, 25, 5],
        special_number: 6,
        zodiacs: ['龙', '牛', '猪', '兔', '蛇', '牛'],
        special_zodiac: '鼠',
        colors: ['蓝', '蓝', '红', '绿', '蓝', '绿'],
        special_color: '绿'
      }
    }
  });
});

app.get('/api/lottery/latest/:count', (req, res) => {
  const count = parseInt(req.params.count) || 5;
  const results = [];
  
  for (let i = 0; i < count; i++) {
    results.push({
      period_number: 187 - i,
      numbers: [26, 41, 7, 27, 25, 5],
      special_number: 6,
      zodiacs: ['龙', '牛', '猪', '兔', '蛇', '牛'],
      special_zodiac: '鼠',
      colors: ['蓝', '蓝', '红', '绿', '蓝', '绿'],
      special_color: '绿',
      draw_time: new Date(Date.now() - (i + 1) * 24 * 60 * 60 * 1000).toISOString()
    });
  }
  
  res.json({
    success: true,
    data: results
  });
});

// 预测相关API
app.get('/api/prediction/current', (req, res) => {
  res.json({
    success: true,
    data: {
      period_number: 188,
      predictions: {
        '一肖一码': {
          seven_zodiac: ['狗', '猴', '虎', '鸡', '兔', '羊', '马'],
          five_zodiac: ['狗', '猴', '虎', '鸡', '兔'],
          three_zodiac: ['狗', '猴', '虎'],
          two_zodiac: ['狗', '猴'],
          one_zodiac: ['狗'],
          eight_numbers: ['08', '32', '46', '04', '45', '27', '35', '12']
        },
        '输尽光三肖': ['猴', '马', '蛇'],
        '惊喜24码': [
          '31', '43', '06', '30', '10', '34', '03', '15', '02', '38', '21', '33',
          '20', '44', '16', '40', '11', '23', '24', '48', '05', '41', '13', '37'
        ],
        '波色中特': ['绿波', '红波'],
        '三头中特': ['1', '2', '4'],
        '单双中特': '单数'
      }
    }
  });
});

// 配置相关API
app.get('/api/config/zodiac', (req, res) => {
  res.json({
    success: true,
    data: {
      zodiac_numbers: {
        '蛇': [1, 13, 25, 37, 49],
        '龙': [2, 14, 26, 38],
        '兔': [3, 15, 27, 39],
        '虎': [4, 16, 28, 40],
        '牛': [5, 17, 29, 41],
        '鼠': [6, 18, 30, 42],
        '猪': [7, 19, 31, 43],
        '狗': [8, 20, 32, 44],
        '鸡': [9, 21, 33, 45],
        '猴': [10, 22, 34, 46],
        '羊': [11, 23, 35, 47],
        '马': [12, 24, 36, 48]
      },
      color_numbers: {
        '红波': [1, 2, 7, 8, 12, 13, 18, 19, 23, 24, 29, 30, 34, 35, 40, 45, 46],
        '蓝波': [3, 4, 9, 10, 14, 15, 20, 25, 26, 31, 36, 37, 41, 42, 47, 48],
        '绿波': [5, 6, 11, 16, 17, 21, 22, 27, 28, 32, 33, 38, 39, 43, 44, 49]
      }
    }
  });
});

// 启动服务器
server.listen(PORT, () => {
  console.log(`简化服务器运行在端口 ${PORT}`);
  console.log(`访问地址: http://localhost:${PORT}`);
});
