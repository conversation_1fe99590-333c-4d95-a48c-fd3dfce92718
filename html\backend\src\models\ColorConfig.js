const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const ColorConfig = sequelize.define('ColorConfig', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    color_name: {
      type: DataTypes.STRING(20),
      allowNull: false,
      unique: true,
      comment: '波色名称'
    },
    numbers: {
      type: DataTypes.JSON,
      allowNull: false,
      comment: '对应号码'
    }
  }, {
    tableName: 'color_config',
    comment: '波色配置表'
  });

  // 实例方法
  ColorConfig.prototype.getNumbersArray = function() {
    return Array.isArray(this.numbers) ? this.numbers : JSON.parse(this.numbers);
  };

  ColorConfig.prototype.hasNumber = function(number) {
    const numbers = this.getNumbersArray();
    return numbers.includes(number);
  };

  // 类方法
  ColorConfig.getByNumber = async function(number) {
    const configs = await this.findAll();
    return configs.find(config => config.hasNumber(number));
  };

  ColorConfig.numberToColor = async function(number) {
    const config = await this.getByNumber(number);
    return config ? config.color_name : null;
  };

  return ColorConfig;
};
