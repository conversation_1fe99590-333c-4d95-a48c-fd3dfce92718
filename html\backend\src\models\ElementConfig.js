const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const ElementConfig = sequelize.define('ElementConfig', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    element_name: {
      type: DataTypes.STRING(10),
      allowNull: false,
      unique: true,
      comment: '五行名称'
    },
    numbers: {
      type: DataTypes.JSON,
      allowNull: false,
      comment: '对应号码'
    }
  }, {
    tableName: 'element_config',
    comment: '五行配置表'
  });

  // 实例方法
  ElementConfig.prototype.getNumbersArray = function() {
    return Array.isArray(this.numbers) ? this.numbers : JSON.parse(this.numbers);
  };

  ElementConfig.prototype.hasNumber = function(number) {
    const numbers = this.getNumbersArray();
    return numbers.includes(number);
  };

  // 类方法
  ElementConfig.getByNumber = async function(number) {
    const configs = await this.findAll();
    return configs.find(config => config.hasNumber(number));
  };

  ElementConfig.numberToElement = async function(number) {
    const config = await this.getByNumber(number);
    return config ? config.element_name : null;
  };

  return ElementConfig;
};
