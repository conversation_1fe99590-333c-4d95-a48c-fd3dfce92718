const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const LotteryPeriod = sequelize.define('LotteryPeriod', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    period_number: {
      type: DataTypes.INTEGER,
      allowNull: false,
      unique: true,
      comment: '期数'
    },
    draw_time: {
      type: DataTypes.DATE,
      allowNull: false,
      comment: '开奖时间'
    },
    status: {
      type: DataTypes.ENUM('pending', 'drawn', 'cancelled'),
      defaultValue: 'pending',
      comment: '状态'
    }
  }, {
    tableName: 'lottery_periods',
    comment: '开奖期数表',
    indexes: [
      {
        fields: ['period_number']
      },
      {
        fields: ['draw_time']
      },
      {
        fields: ['status']
      }
    ]
  });

  // 实例方法
  LotteryPeriod.prototype.isDrawn = function() {
    return this.status === 'drawn';
  };

  LotteryPeriod.prototype.isPending = function() {
    return this.status === 'pending';
  };

  LotteryPeriod.prototype.getTimeUntilDraw = function() {
    const now = new Date();
    const drawTime = new Date(this.draw_time);
    return Math.max(0, drawTime.getTime() - now.getTime());
  };

  // 类方法
  LotteryPeriod.getCurrentPeriod = async function() {
    return await this.findOne({
      where: {
        status: 'pending'
      },
      order: [['period_number', 'ASC']]
    });
  };

  LotteryPeriod.getLatestResult = async function() {
    return await this.findOne({
      where: {
        status: 'drawn'
      },
      order: [['period_number', 'DESC']],
      include: ['result']
    });
  };

  LotteryPeriod.getNextPeriod = async function() {
    const current = await this.getCurrentPeriod();
    if (!current) return null;
    
    return await this.findOne({
      where: {
        period_number: current.period_number + 1
      }
    });
  };

  return LotteryPeriod;
};
