const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const LotteryResult = sequelize.define('LotteryResult', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    period_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '期数ID'
    },
    number_1: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: { min: 1, max: 49 },
      comment: '第1个号码'
    },
    number_2: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: { min: 1, max: 49 },
      comment: '第2个号码'
    },
    number_3: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: { min: 1, max: 49 },
      comment: '第3个号码'
    },
    number_4: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: { min: 1, max: 49 },
      comment: '第4个号码'
    },
    number_5: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: { min: 1, max: 49 },
      comment: '第5个号码'
    },
    number_6: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: { min: 1, max: 49 },
      comment: '第6个号码'
    },
    special_number: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: { min: 1, max: 49 },
      comment: '特别号码'
    },
    zodiac_1: {
      type: DataTypes.STRING(10),
      allowNull: false,
      comment: '第1个生肖'
    },
    zodiac_2: {
      type: DataTypes.STRING(10),
      allowNull: false,
      comment: '第2个生肖'
    },
    zodiac_3: {
      type: DataTypes.STRING(10),
      allowNull: false,
      comment: '第3个生肖'
    },
    zodiac_4: {
      type: DataTypes.STRING(10),
      allowNull: false,
      comment: '第4个生肖'
    },
    zodiac_5: {
      type: DataTypes.STRING(10),
      allowNull: false,
      comment: '第5个生肖'
    },
    zodiac_6: {
      type: DataTypes.STRING(10),
      allowNull: false,
      comment: '第6个生肖'
    },
    special_zodiac: {
      type: DataTypes.STRING(10),
      allowNull: false,
      comment: '特别生肖'
    }
  }, {
    tableName: 'lottery_results',
    comment: '开奖结果表',
    indexes: [
      {
        fields: ['period_id']
      }
    ]
  });

  // 实例方法
  LotteryResult.prototype.getAllNumbers = function() {
    return [
      this.number_1,
      this.number_2,
      this.number_3,
      this.number_4,
      this.number_5,
      this.number_6,
      this.special_number
    ];
  };

  LotteryResult.prototype.getRegularNumbers = function() {
    return [
      this.number_1,
      this.number_2,
      this.number_3,
      this.number_4,
      this.number_5,
      this.number_6
    ];
  };

  LotteryResult.prototype.getAllZodiacs = function() {
    return [
      this.zodiac_1,
      this.zodiac_2,
      this.zodiac_3,
      this.zodiac_4,
      this.zodiac_5,
      this.zodiac_6,
      this.special_zodiac
    ];
  };

  LotteryResult.prototype.getColors = function() {
    const { ZodiacConfig } = require('./index');
    const numbers = this.getAllNumbers();
    const colors = [];
    
    // 根据号码计算波色
    numbers.forEach(num => {
      if ([1, 2, 7, 8, 12, 13, 18, 19, 23, 24, 29, 30, 34, 35, 40, 45, 46].includes(num)) {
        colors.push('红');
      } else if ([3, 4, 9, 10, 14, 15, 20, 25, 26, 31, 36, 37, 41, 42, 47, 48].includes(num)) {
        colors.push('蓝');
      } else {
        colors.push('绿');
      }
    });
    
    return colors;
  };

  LotteryResult.prototype.getElements = function() {
    const numbers = this.getAllNumbers();
    const elements = [];
    
    // 根据号码计算五行
    numbers.forEach(num => {
      if ([3, 4, 11, 12, 25, 26, 33, 34, 41, 42].includes(num)) {
        elements.push('金');
      } else if ([7, 8, 15, 16, 23, 24, 37, 38, 45, 46].includes(num)) {
        elements.push('木');
      } else if ([13, 14, 21, 22, 29, 30, 43, 44].includes(num)) {
        elements.push('水');
      } else if ([1, 2, 9, 10, 17, 18, 31, 32, 39, 40, 47, 48].includes(num)) {
        elements.push('火');
      } else {
        elements.push('土');
      }
    });
    
    return elements;
  };

  // 类方法
  LotteryResult.getHistoryResults = async function(limit = 10) {
    return await this.findAll({
      include: ['period'],
      order: [['period', 'period_number', 'DESC']],
      limit
    });
  };

  return LotteryResult;
};
