const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Prediction = sequelize.define('Prediction', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    period_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '期数ID'
    },
    prediction_type: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '预测类型'
    },
    prediction_data: {
      type: DataTypes.JSON,
      allowNull: false,
      comment: '预测数据'
    },
    accuracy_rate: {
      type: DataTypes.DECIMAL(5, 2),
      defaultValue: 0.00,
      comment: '准确率'
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      comment: '是否启用'
    }
  }, {
    tableName: 'predictions',
    comment: '预测数据表',
    indexes: [
      {
        fields: ['period_id']
      },
      {
        fields: ['prediction_type']
      },
      {
        fields: ['is_active']
      }
    ]
  });

  // 预测类型常量
  Prediction.TYPES = {
    ONE_ZODIAC_ONE_CODE: 'one_zodiac_one_code',      // 一肖一码
    LOSE_ALL_THREE: 'lose_all_three',                // 输尽光三肖
    SURPRISE_24_CODES: 'surprise_24_codes',          // 惊喜24码
    COLOR_SPECIAL: 'color_special',                  // 波色中特
    THREE_HEAD_SPECIAL: 'three_head_special',        // 三头中特
    SINGLE_DOUBLE: 'single_double',                  // 单双中特
    KILL_TWO_ZODIAC_ONE_TAIL: 'kill_two_zodiac_one_tail', // 杀二肖一尾
    SEVEN_TAIL_SPECIAL: 'seven_tail_special',        // 七尾中特
    HOME_WILD_SPECIAL: 'home_wild_special',          // 家野中特
    BIG_SMALL_SPECIAL: 'big_small_special',          // 大小中特
    FLAT_ONE_TAIL: 'flat_one_tail',                  // 平特一尾
    IDIOM_FLAT_SPECIAL: 'idiom_flat_special',        // 成语出平特
    FOUR_ZODIAC_FOUR_CODE: 'four_zodiac_four_code',  // 四肖四码
    FIVE_ELEMENT_SPECIAL: 'five_element_special',    // 五行中特
    KILL_ONE_DOOR: 'kill_one_door',                  // 杀一门
    MALE_FEMALE_SPECIAL: 'male_female_special',      // 男女特肖
    HOME_WILD_VS: 'home_wild_vs',                    // 家禽VS野兽
    BLACK_WHITE_SPECIAL: 'black_white_special'       // 黑白肖中特
  };

  // 实例方法
  Prediction.prototype.updateAccuracy = function(isCorrect) {
    // 简单的准确率计算，可以根据需要改进
    const currentRate = parseFloat(this.accuracy_rate) || 0;
    const newRate = isCorrect ? Math.min(100, currentRate + 10) : Math.max(0, currentRate - 5);
    this.accuracy_rate = newRate;
    return this.save();
  };

  Prediction.prototype.isExpired = function() {
    // 检查预测是否过期（基于关联的期数开奖时间）
    if (!this.period) return false;
    return new Date() > new Date(this.period.draw_time);
  };

  // 类方法
  Prediction.getByType = async function(type, periodId = null) {
    const where = { 
      prediction_type: type,
      is_active: true
    };
    
    if (periodId) {
      where.period_id = periodId;
    }

    return await this.findAll({
      where,
      include: ['period'],
      order: [['period', 'period_number', 'DESC']]
    });
  };

  Prediction.getCurrentPredictions = async function() {
    const { LotteryPeriod } = require('./index');
    const currentPeriod = await LotteryPeriod.getCurrentPeriod();
    
    if (!currentPeriod) return [];

    return await this.findAll({
      where: {
        period_id: currentPeriod.id,
        is_active: true
      },
      include: ['period']
    });
  };

  Prediction.getHistoryByType = async function(type, limit = 10) {
    return await this.findAll({
      where: {
        prediction_type: type,
        is_active: true
      },
      include: ['period'],
      order: [['period', 'period_number', 'DESC']],
      limit
    });
  };

  Prediction.createPrediction = async function(periodId, type, data) {
    return await this.create({
      period_id: periodId,
      prediction_type: type,
      prediction_data: data,
      is_active: true
    });
  };

  return Prediction;
};
