const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const SystemConfig = sequelize.define('SystemConfig', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    config_key: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true,
      comment: '配置键'
    },
    config_value: {
      type: DataTypes.TEXT,
      allowNull: false,
      comment: '配置值'
    },
    description: {
      type: DataTypes.STRING(255),
      comment: '描述'
    }
  }, {
    tableName: 'system_config',
    comment: '系统配置表',
    indexes: [
      {
        fields: ['config_key']
      }
    ]
  });

  // 类方法
  SystemConfig.getValue = async function(key, defaultValue = null) {
    const config = await this.findOne({
      where: { config_key: key }
    });
    return config ? config.config_value : defaultValue;
  };

  SystemConfig.setValue = async function(key, value, description = null) {
    const [config, created] = await this.findOrCreate({
      where: { config_key: key },
      defaults: {
        config_value: value,
        description
      }
    });

    if (!created) {
      config.config_value = value;
      if (description) {
        config.description = description;
      }
      await config.save();
    }

    return config;
  };

  SystemConfig.getAll = async function() {
    const configs = await this.findAll();
    const result = {};
    configs.forEach(config => {
      result[config.config_key] = config.config_value;
    });
    return result;
  };

  return SystemConfig;
};
