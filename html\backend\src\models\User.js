const { DataTypes } = require('sequelize');
const bcrypt = require('bcryptjs');

module.exports = (sequelize) => {
  const User = sequelize.define('User', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    username: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      comment: '用户名'
    },
    email: {
      type: DataTypes.STRING(100),
      unique: true,
      validate: {
        isEmail: true
      },
      comment: '邮箱'
    },
    password_hash: {
      type: DataTypes.STRING(255),
      allowNull: false,
      comment: '密码哈希'
    },
    role: {
      type: DataTypes.ENUM('admin', 'user'),
      defaultValue: 'user',
      comment: '角色'
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      comment: '是否激活'
    },
    last_login: {
      type: DataTypes.DATE,
      comment: '最后登录时间'
    }
  }, {
    tableName: 'users',
    comment: '用户表',
    indexes: [
      {
        fields: ['username']
      },
      {
        fields: ['email']
      },
      {
        fields: ['role']
      }
    ]
  });

  // 钩子：密码加密
  User.beforeCreate(async (user) => {
    if (user.password_hash) {
      user.password_hash = await bcrypt.hash(user.password_hash, 10);
    }
  });

  User.beforeUpdate(async (user) => {
    if (user.changed('password_hash')) {
      user.password_hash = await bcrypt.hash(user.password_hash, 10);
    }
  });

  // 实例方法
  User.prototype.validatePassword = async function(password) {
    return await bcrypt.compare(password, this.password_hash);
  };

  User.prototype.isAdmin = function() {
    return this.role === 'admin';
  };

  User.prototype.updateLastLogin = function() {
    this.last_login = new Date();
    return this.save();
  };

  // 类方法
  User.findByUsername = async function(username) {
    return await this.findOne({
      where: { username, is_active: true }
    });
  };

  User.findByEmail = async function(email) {
    return await this.findOne({
      where: { email, is_active: true }
    });
  };

  return User;
};
