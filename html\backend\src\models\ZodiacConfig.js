const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const ZodiacConfig = sequelize.define('ZodiacConfig', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    zodiac_name: {
      type: DataTypes.STRING(10),
      allowNull: false,
      unique: true,
      comment: '生肖名称'
    },
    numbers: {
      type: DataTypes.JSON,
      allowNull: false,
      comment: '对应号码'
    },
    color: {
      type: DataTypes.STRING(20),
      allowNull: false,
      comment: '波色'
    },
    element: {
      type: DataTypes.STRING(10),
      allowNull: false,
      comment: '五行'
    },
    gender: {
      type: DataTypes.ENUM('male', 'female'),
      allowNull: false,
      comment: '性别'
    },
    type: {
      type: DataTypes.ENUM('domestic', 'wild'),
      allowNull: false,
      comment: '家禽/野兽'
    }
  }, {
    tableName: 'zodiac_config',
    comment: '生肖配置表'
  });

  // 生肖常量
  ZodiacConfig.ZODIACS = [
    '鼠', '牛', '虎', '兔', '龙', '蛇', 
    '马', '羊', '猴', '鸡', '狗', '猪'
  ];

  ZodiacConfig.COLORS = ['红', '蓝', '绿'];
  ZodiacConfig.ELEMENTS = ['金', '木', '水', '火', '土'];
  ZodiacConfig.GENDERS = ['male', 'female'];
  ZodiacConfig.TYPES = ['domestic', 'wild'];

  // 实例方法
  ZodiacConfig.prototype.getNumbersArray = function() {
    return Array.isArray(this.numbers) ? this.numbers : JSON.parse(this.numbers);
  };

  ZodiacConfig.prototype.hasNumber = function(number) {
    const numbers = this.getNumbersArray();
    return numbers.includes(number);
  };

  ZodiacConfig.prototype.isMale = function() {
    return this.gender === 'male';
  };

  ZodiacConfig.prototype.isFemale = function() {
    return this.gender === 'female';
  };

  ZodiacConfig.prototype.isDomestic = function() {
    return this.type === 'domestic';
  };

  ZodiacConfig.prototype.isWild = function() {
    return this.type === 'wild';
  };

  // 类方法
  ZodiacConfig.getByNumber = async function(number) {
    const configs = await this.findAll();
    return configs.find(config => config.hasNumber(number));
  };

  ZodiacConfig.getByColor = async function(color) {
    return await this.findAll({
      where: { color }
    });
  };

  ZodiacConfig.getByElement = async function(element) {
    return await this.findAll({
      where: { element }
    });
  };

  ZodiacConfig.getByGender = async function(gender) {
    return await this.findAll({
      where: { gender }
    });
  };

  ZodiacConfig.getByType = async function(type) {
    return await this.findAll({
      where: { type }
    });
  };

  ZodiacConfig.getMaleZodiacs = async function() {
    return await this.getByGender('male');
  };

  ZodiacConfig.getFemaleZodiacs = async function() {
    return await this.getByGender('female');
  };

  ZodiacConfig.getDomesticZodiacs = async function() {
    return await this.getByType('domestic');
  };

  ZodiacConfig.getWildZodiacs = async function() {
    return await this.getByType('wild');
  };

  ZodiacConfig.numberToZodiac = async function(number) {
    const config = await this.getByNumber(number);
    return config ? config.zodiac_name : null;
  };

  ZodiacConfig.getZodiacNumbers = async function(zodiacName) {
    const config = await this.findOne({
      where: { zodiac_name: zodiacName }
    });
    return config ? config.getNumbersArray() : [];
  };

  ZodiacConfig.getAllZodiacData = async function() {
    const configs = await this.findAll({
      order: [['id', 'ASC']]
    });
    
    return configs.map(config => ({
      name: config.zodiac_name,
      numbers: config.getNumbersArray(),
      color: config.color,
      element: config.element,
      gender: config.gender,
      type: config.type
    }));
  };

  return ZodiacConfig;
};
