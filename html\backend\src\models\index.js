const { Sequelize } = require('sequelize');

// 数据库配置
const sequelize = new Sequelize({
  dialect: 'mysql',
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  database: process.env.DB_NAME || 'lottery_db',
  username: process.env.DB_USER || 'lottery_user',
  password: process.env.DB_PASSWORD || 'lottery_pass',
  logging: process.env.NODE_ENV === 'development' ? console.log : false,
  pool: {
    max: 10,
    min: 0,
    acquire: 30000,
    idle: 10000
  },
  define: {
    timestamps: true,
    underscored: true,
    freezeTableName: true
  },
  timezone: '+08:00'
});

// 导入模型
const LotteryPeriod = require('./LotteryPeriod')(sequelize);
const LotteryResult = require('./LotteryResult')(sequelize);
const Prediction = require('./Prediction')(sequelize);
const ZodiacConfig = require('./ZodiacConfig')(sequelize);
const ColorConfig = require('./ColorConfig')(sequelize);
const ElementConfig = require('./ElementConfig')(sequelize);
const User = require('./User')(sequelize);
const SystemConfig = require('./SystemConfig')(sequelize);

// 定义关联关系
LotteryPeriod.hasOne(LotteryResult, {
  foreignKey: 'period_id',
  as: 'result'
});

LotteryResult.belongsTo(LotteryPeriod, {
  foreignKey: 'period_id',
  as: 'period'
});

LotteryPeriod.hasMany(Prediction, {
  foreignKey: 'period_id',
  as: 'predictions'
});

Prediction.belongsTo(LotteryPeriod, {
  foreignKey: 'period_id',
  as: 'period'
});

// 导出模型和连接
module.exports = {
  sequelize,
  LotteryPeriod,
  LotteryResult,
  Prediction,
  ZodiacConfig,
  ColorConfig,
  ElementConfig,
  User,
  SystemConfig
};
