const express = require('express');
const router = express.Router();
const { ZodiacConfig, ColorConfig, ElementConfig, SystemConfig } = require('../models');

// 获取生肖配置
router.get('/zodiac', async (req, res) => {
  try {
    const zodiacs = await ZodiacConfig.getAllZodiacData();

    res.json({
      success: true,
      data: zodiacs
    });
  } catch (error) {
    console.error('获取生肖配置失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 根据号码获取生肖
router.get('/zodiac/number/:number', async (req, res) => {
  try {
    const { number } = req.params;
    const zodiac = await ZodiacConfig.numberToZodiac(parseInt(number));

    if (!zodiac) {
      return res.status(404).json({
        success: false,
        message: '号码对应的生肖不存在'
      });
    }

    res.json({
      success: true,
      data: { number: parseInt(number), zodiac }
    });
  } catch (error) {
    console.error('获取号码生肖失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 根据生肖获取号码
router.get('/zodiac/:zodiacName/numbers', async (req, res) => {
  try {
    const { zodiacName } = req.params;
    const numbers = await ZodiacConfig.getZodiacNumbers(zodiacName);

    res.json({
      success: true,
      data: { zodiac: zodiacName, numbers }
    });
  } catch (error) {
    console.error('获取生肖号码失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 获取波色配置
router.get('/color', async (req, res) => {
  try {
    const colors = await ColorConfig.findAll({
      order: [['id', 'ASC']]
    });

    const colorData = colors.map(color => ({
      name: color.color_name,
      numbers: color.getNumbersArray()
    }));

    res.json({
      success: true,
      data: colorData
    });
  } catch (error) {
    console.error('获取波色配置失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 根据号码获取波色
router.get('/color/number/:number', async (req, res) => {
  try {
    const { number } = req.params;
    const color = await ColorConfig.numberToColor(parseInt(number));

    if (!color) {
      return res.status(404).json({
        success: false,
        message: '号码对应的波色不存在'
      });
    }

    res.json({
      success: true,
      data: { number: parseInt(number), color }
    });
  } catch (error) {
    console.error('获取号码波色失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 获取五行配置
router.get('/element', async (req, res) => {
  try {
    const elements = await ElementConfig.findAll({
      order: [['id', 'ASC']]
    });

    const elementData = elements.map(element => ({
      name: element.element_name,
      numbers: element.getNumbersArray()
    }));

    res.json({
      success: true,
      data: elementData
    });
  } catch (error) {
    console.error('获取五行配置失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 根据号码获取五行
router.get('/element/number/:number', async (req, res) => {
  try {
    const { number } = req.params;
    const element = await ElementConfig.numberToElement(parseInt(number));

    if (!element) {
      return res.status(404).json({
        success: false,
        message: '号码对应的五行不存在'
      });
    }

    res.json({
      success: true,
      data: { number: parseInt(number), element }
    });
  } catch (error) {
    console.error('获取号码五行失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 获取系统配置
router.get('/system', async (req, res) => {
  try {
    const configs = await SystemConfig.getAll();

    res.json({
      success: true,
      data: configs
    });
  } catch (error) {
    console.error('获取系统配置失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 获取指定系统配置
router.get('/system/:key', async (req, res) => {
  try {
    const { key } = req.params;
    const value = await SystemConfig.getValue(key);

    if (value === null) {
      return res.status(404).json({
        success: false,
        message: '配置项不存在'
      });
    }

    res.json({
      success: true,
      data: { key, value }
    });
  } catch (error) {
    console.error('获取系统配置失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 设置系统配置（管理员功能）
router.post('/system/:key', async (req, res) => {
  try {
    const { key } = req.params;
    const { value, description } = req.body;

    if (!value) {
      return res.status(400).json({
        success: false,
        message: '配置值不能为空'
      });
    }

    await SystemConfig.setValue(key, value, description);

    res.json({
      success: true,
      message: '配置设置成功'
    });
  } catch (error) {
    console.error('设置系统配置失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 获取所有配置数据（用于前端初始化）
router.get('/all', async (req, res) => {
  try {
    const [zodiacs, colors, elements, systemConfigs] = await Promise.all([
      ZodiacConfig.getAllZodiacData(),
      ColorConfig.findAll({ order: [['id', 'ASC']] }),
      ElementConfig.findAll({ order: [['id', 'ASC']] }),
      SystemConfig.getAll()
    ]);

    const colorData = colors.map(color => ({
      name: color.color_name,
      numbers: color.getNumbersArray()
    }));

    const elementData = elements.map(element => ({
      name: element.element_name,
      numbers: element.getNumbersArray()
    }));

    res.json({
      success: true,
      data: {
        zodiacs,
        colors: colorData,
        elements: elementData,
        system: systemConfigs
      }
    });
  } catch (error) {
    console.error('获取所有配置失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

module.exports = router;
