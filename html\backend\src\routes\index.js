const express = require('express');
const router = express.Router();

// 导入各个路由模块
const lotteryRoutes = require('./lottery');
const predictionRoutes = require('./prediction');
const configRoutes = require('./config');
const authRoutes = require('./auth');

// API版本信息
router.get('/', (req, res) => {
  res.json({
    message: '澳门葡京新彩 API',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
    endpoints: {
      lottery: '/api/lottery',
      prediction: '/api/prediction',
      config: '/api/config',
      auth: '/api/auth'
    }
  });
});

// 健康检查
router.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// 注册路由
router.use('/lottery', lotteryRoutes);
router.use('/prediction', predictionRoutes);
router.use('/config', configRoutes);
router.use('/auth', authRoutes);

module.exports = router;
