const express = require('express');
const router = express.Router();
const { LotteryPeriod, LotteryResult } = require('../models');
const { manualDraw } = require('../services/cronService');

// 获取当前期数信息
router.get('/current', async (req, res) => {
  try {
    const currentPeriod = await LotteryPeriod.getCurrentPeriod();
    const latestResult = await LotteryPeriod.getLatestResult();
    
    if (!currentPeriod) {
      return res.status(404).json({
        success: false,
        message: '当前没有待开奖期数'
      });
    }

    const timeUntilDraw = currentPeriod.getTimeUntilDraw();

    res.json({
      success: true,
      data: {
        currentPeriod,
        timeUntilDraw,
        latestResult
      }
    });
  } catch (error) {
    console.error('获取当前期数失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 获取历史开奖结果
router.get('/results', async (req, res) => {
  try {
    const { page = 1, limit = 10 } = req.query;
    const offset = (page - 1) * limit;

    const { count, rows } = await LotteryResult.findAndCountAll({
      include: ['period'],
      order: [['period', 'period_number', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      success: true,
      data: {
        results: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(count / limit)
        }
      }
    });
  } catch (error) {
    console.error('获取历史结果失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 获取指定期数的开奖结果
router.get('/results/:periodNumber', async (req, res) => {
  try {
    const { periodNumber } = req.params;

    const period = await LotteryPeriod.findOne({
      where: { period_number: periodNumber },
      include: ['result']
    });

    if (!period) {
      return res.status(404).json({
        success: false,
        message: '期数不存在'
      });
    }

    res.json({
      success: true,
      data: period
    });
  } catch (error) {
    console.error('获取期数结果失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 获取最新几期的开奖结果
router.get('/latest/:count', async (req, res) => {
  try {
    const { count = 5 } = req.params;
    const limit = Math.min(parseInt(count), 20); // 最多20期

    const results = await LotteryResult.getHistoryResults(limit);

    res.json({
      success: true,
      data: results
    });
  } catch (error) {
    console.error('获取最新结果失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 获取期数列表
router.get('/periods', async (req, res) => {
  try {
    const { status, page = 1, limit = 20 } = req.query;
    const offset = (page - 1) * limit;

    const where = {};
    if (status) {
      where.status = status;
    }

    const { count, rows } = await LotteryPeriod.findAndCountAll({
      where,
      order: [['period_number', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      success: true,
      data: {
        periods: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(count / limit)
        }
      }
    });
  } catch (error) {
    console.error('获取期数列表失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 手动开奖（管理员功能）
router.post('/draw/:periodNumber', async (req, res) => {
  try {
    const { periodNumber } = req.params;
    
    // 这里应该添加管理员权限验证
    // if (!req.user || !req.user.isAdmin()) {
    //   return res.status(403).json({
    //     success: false,
    //     message: '权限不足'
    //   });
    // }

    const result = await manualDraw(parseInt(periodNumber));

    res.json({
      success: true,
      message: '开奖成功',
      data: result
    });
  } catch (error) {
    console.error('手动开奖失败:', error);
    res.status(500).json({
      success: false,
      message: error.message || '开奖失败'
    });
  }
});

// 获取开奖统计信息
router.get('/stats', async (req, res) => {
  try {
    const totalPeriods = await LotteryPeriod.count();
    const drawnPeriods = await LotteryPeriod.count({
      where: { status: 'drawn' }
    });
    const pendingPeriods = await LotteryPeriod.count({
      where: { status: 'pending' }
    });

    // 获取最近10期的号码统计
    const recentResults = await LotteryResult.findAll({
      include: ['period'],
      order: [['period', 'period_number', 'DESC']],
      limit: 10
    });

    const numberStats = {};
    const zodiacStats = {};

    recentResults.forEach(result => {
      const numbers = result.getAllNumbers();
      const zodiacs = result.getAllZodiacs();

      numbers.forEach(num => {
        numberStats[num] = (numberStats[num] || 0) + 1;
      });

      zodiacs.forEach(zodiac => {
        zodiacStats[zodiac] = (zodiacStats[zodiac] || 0) + 1;
      });
    });

    res.json({
      success: true,
      data: {
        totalPeriods,
        drawnPeriods,
        pendingPeriods,
        recentStats: {
          numbers: numberStats,
          zodiacs: zodiacStats
        }
      }
    });
  } catch (error) {
    console.error('获取统计信息失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

module.exports = router;
