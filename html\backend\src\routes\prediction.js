const express = require('express');
const router = express.Router();
const { Prediction, LotteryPeriod } = require('../models');
const { generateAllPredictions } = require('../services/predictionService');

// 获取当前期数的所有预测
router.get('/current', async (req, res) => {
  try {
    const predictions = await Prediction.getCurrentPredictions();

    res.json({
      success: true,
      data: predictions
    });
  } catch (error) {
    console.error('获取当前预测失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 根据类型获取预测
router.get('/type/:type', async (req, res) => {
  try {
    const { type } = req.params;
    const { periodId, limit = 10 } = req.query;

    let predictions;
    if (periodId) {
      predictions = await Prediction.getByType(type, parseInt(periodId));
    } else {
      predictions = await Prediction.getHistoryByType(type, parseInt(limit));
    }

    res.json({
      success: true,
      data: predictions
    });
  } catch (error) {
    console.error('获取预测数据失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 获取指定期数的预测
router.get('/period/:periodNumber', async (req, res) => {
  try {
    const { periodNumber } = req.params;

    const period = await LotteryPeriod.findOne({
      where: { period_number: periodNumber }
    });

    if (!period) {
      return res.status(404).json({
        success: false,
        message: '期数不存在'
      });
    }

    const predictions = await Prediction.findAll({
      where: {
        period_id: period.id,
        is_active: true
      },
      include: ['period']
    });

    res.json({
      success: true,
      data: predictions
    });
  } catch (error) {
    console.error('获取期数预测失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 获取预测类型列表
router.get('/types', (req, res) => {
  const types = Object.entries(Prediction.TYPES).map(([key, value]) => ({
    key,
    value,
    name: getTypeName(value)
  }));

  res.json({
    success: true,
    data: types
  });
});

// 获取一肖一码预测
router.get('/one-zodiac-one-code', async (req, res) => {
  try {
    const predictions = await Prediction.getByType(Prediction.TYPES.ONE_ZODIAC_ONE_CODE);
    
    res.json({
      success: true,
      data: predictions
    });
  } catch (error) {
    console.error('获取一肖一码预测失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 获取输尽光三肖预测
router.get('/lose-all-three', async (req, res) => {
  try {
    const predictions = await Prediction.getByType(Prediction.TYPES.LOSE_ALL_THREE);
    
    res.json({
      success: true,
      data: predictions
    });
  } catch (error) {
    console.error('获取输尽光三肖预测失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 获取惊喜24码预测
router.get('/surprise-24-codes', async (req, res) => {
  try {
    const predictions = await Prediction.getByType(Prediction.TYPES.SURPRISE_24_CODES);
    
    res.json({
      success: true,
      data: predictions
    });
  } catch (error) {
    console.error('获取惊喜24码预测失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 重新生成预测数据（管理员功能）
router.post('/regenerate/:periodNumber', async (req, res) => {
  try {
    const { periodNumber } = req.params;

    const period = await LotteryPeriod.findOne({
      where: { 
        period_number: periodNumber,
        status: 'pending'
      }
    });

    if (!period) {
      return res.status(404).json({
        success: false,
        message: '期数不存在或已开奖'
      });
    }

    await generateAllPredictions(period.id);

    res.json({
      success: true,
      message: '预测数据重新生成成功'
    });
  } catch (error) {
    console.error('重新生成预测数据失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 获取预测准确率统计
router.get('/accuracy', async (req, res) => {
  try {
    const { type } = req.query;

    const where = { is_active: true };
    if (type) {
      where.prediction_type = type;
    }

    const predictions = await Prediction.findAll({
      where,
      attributes: ['prediction_type', 'accuracy_rate'],
      include: ['period']
    });

    const stats = {};
    predictions.forEach(prediction => {
      const type = prediction.prediction_type;
      if (!stats[type]) {
        stats[type] = {
          total: 0,
          totalAccuracy: 0,
          averageAccuracy: 0
        };
      }
      stats[type].total++;
      stats[type].totalAccuracy += parseFloat(prediction.accuracy_rate);
    });

    // 计算平均准确率
    Object.keys(stats).forEach(type => {
      stats[type].averageAccuracy = stats[type].totalAccuracy / stats[type].total;
    });

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('获取准确率统计失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 预测类型名称映射
function getTypeName(type) {
  const names = {
    'one_zodiac_one_code': '一肖一码',
    'lose_all_three': '输尽光三肖',
    'surprise_24_codes': '惊喜24码',
    'color_special': '波色中特',
    'three_head_special': '三头中特',
    'single_double': '单双中特',
    'kill_two_zodiac_one_tail': '杀二肖一尾',
    'seven_tail_special': '七尾中特',
    'home_wild_special': '家野中特',
    'big_small_special': '大小中特',
    'flat_one_tail': '平特一尾',
    'idiom_flat_special': '成语出平特',
    'four_zodiac_four_code': '四肖四码',
    'five_element_special': '五行中特',
    'kill_one_door': '杀一门',
    'male_female_special': '男女特肖',
    'home_wild_vs': '家禽VS野兽',
    'black_white_special': '黑白肖中特'
  };
  return names[type] || type;
}

module.exports = router;
