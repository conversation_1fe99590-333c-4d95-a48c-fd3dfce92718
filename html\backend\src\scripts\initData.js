const { LotteryPeriod, LotteryResult, Prediction } = require('../models');
const { generateAllPredictions } = require('../services/predictionService');
const moment = require('moment');

// 初始化数据
const initializeData = async () => {
  try {
    console.log('开始初始化数据...');

    // 检查是否已有数据
    const existingPeriods = await LotteryPeriod.count();
    if (existingPeriods > 0) {
      console.log('数据已存在，跳过初始化');
      return;
    }

    // 创建历史期数（185-187期，已开奖）
    const historyPeriods = [
      {
        period_number: 185,
        draw_time: moment().subtract(3, 'days').hour(22).minute(39).second(0).toDate(),
        status: 'drawn'
      },
      {
        period_number: 186,
        draw_time: moment().subtract(2, 'days').hour(22).minute(39).second(0).toDate(),
        status: 'drawn'
      },
      {
        period_number: 187,
        draw_time: moment().subtract(1, 'days').hour(22).minute(39).second(0).toDate(),
        status: 'drawn'
      }
    ];

    // 创建当前期数（188期，待开奖）
    const currentPeriod = {
      period_number: 188,
      draw_time: moment().add(5, 'minutes').toDate(), // 5分钟后开奖
      status: 'pending'
    };

    // 插入期数数据
    const periods = await LotteryPeriod.bulkCreate([...historyPeriods, currentPeriod]);
    console.log('期数数据创建完成');

    // 为历史期数创建开奖结果
    const historyResults = [
      {
        period_id: periods[0].id, // 185期
        number_1: 7, number_2: 19, number_3: 31, number_4: 43, number_5: 10, number_6: 22,
        special_number: 7,
        zodiac_1: '猪', zodiac_2: '猪', zodiac_3: '猪', zodiac_4: '猪', zodiac_5: '猴', zodiac_6: '猴',
        special_zodiac: '猪',
        color_1: '红', color_2: '红', color_3: '红', color_4: '红', color_5: '蓝', color_6: '蓝',
        special_color: '红'
      },
      {
        period_id: periods[1].id, // 186期
        number_1: 4, number_2: 16, number_3: 28, number_4: 40, number_5: 8, number_6: 20,
        special_number: 4,
        zodiac_1: '虎', zodiac_2: '虎', zodiac_3: '虎', zodiac_4: '虎', zodiac_5: '狗', zodiac_6: '狗',
        special_zodiac: '虎',
        color_1: '蓝', color_2: '蓝', color_3: '绿', color_4: '蓝', color_5: '红', color_6: '蓝',
        special_color: '蓝'
      },
      {
        period_id: periods[2].id, // 187期
        number_1: 26, number_2: 41, number_3: 7, number_4: 27, number_5: 25, number_6: 5,
        special_number: 6,
        zodiac_1: '龙', zodiac_2: '牛', zodiac_3: '猪', zodiac_4: '兔', zodiac_5: '蛇', zodiac_6: '牛',
        special_zodiac: '鼠',
        color_1: '蓝', color_2: '蓝', color_3: '红', color_4: '绿', color_5: '蓝', color_6: '绿',
        special_color: '绿'
      }
    ];

    await LotteryResult.bulkCreate(historyResults);
    console.log('历史开奖结果创建完成');

    // 为当前期数生成预测数据
    await generateAllPredictions(periods[3].id); // 188期
    console.log('当前期预测数据生成完成');

    console.log('数据初始化完成！');
    console.log('- 历史期数: 185-187期（已开奖）');
    console.log('- 当前期数: 188期（待开奖）');
    console.log('- 预测数据已生成');

  } catch (error) {
    console.error('初始化数据失败:', error);
    throw error;
  }
};

// 如果直接运行此脚本
if (require.main === module) {
  const { sequelize } = require('../models');
  
  sequelize.authenticate()
    .then(() => {
      console.log('数据库连接成功');
      return initializeData();
    })
    .then(() => {
      console.log('初始化完成，退出程序');
      process.exit(0);
    })
    .catch((error) => {
      console.error('初始化失败:', error);
      process.exit(1);
    });
}

module.exports = { initializeData };
