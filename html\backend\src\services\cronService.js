const cron = require('node-cron');
const moment = require('moment');
const { LotteryPeriod, LotteryResult, SystemConfig } = require('../models');
const { broadcastCountdownUpdate, broadcastNewResult } = require('./socketService');
const predictionService = require('./predictionService');

let io = null;

const startCronJobs = (socketIo) => {
  io = socketIo;
  
  // 每秒更新倒计时
  cron.schedule('* * * * * *', async () => {
    await broadcastCountdownUpdate();
  });
  
  // 每分钟检查是否需要开奖
  cron.schedule('* * * * *', async () => {
    await checkAndDrawLottery();
  });
  
  // 每天22:30创建下一期
  cron.schedule('30 22 * * *', async () => {
    await createNextPeriod();
  });
  
  // 每小时更新预测数据
  cron.schedule('0 * * * *', async () => {
    await updatePredictions();
  });
  
  console.log('定时任务已启动');
};

// 检查并执行开奖
const checkAndDrawLottery = async () => {
  try {
    const currentPeriod = await LotteryPeriod.getCurrentPeriod();
    if (!currentPeriod) return;
    
    const now = new Date();
    const drawTime = new Date(currentPeriod.draw_time);
    
    // 如果到了开奖时间
    if (now >= drawTime && currentPeriod.status === 'pending') {
      console.log(`开始开奖 - 第${currentPeriod.period_number}期`);
      
      // 生成开奖结果
      const result = await generateLotteryResult(currentPeriod.id);
      
      // 更新期数状态
      currentPeriod.status = 'drawn';
      await currentPeriod.save();
      
      // 广播开奖结果
      await broadcastNewResult(result);
      
      console.log(`开奖完成 - 第${currentPeriod.period_number}期`);
    }
  } catch (error) {
    console.error('开奖检查失败:', error);
  }
};

// 生成开奖结果
const generateLotteryResult = async (periodId) => {
  // 生成6个不重复的号码 + 1个特别号码
  const numbers = [];
  while (numbers.length < 6) {
    const num = Math.floor(Math.random() * 49) + 1;
    if (!numbers.includes(num)) {
      numbers.push(num);
    }
  }
  
  // 特别号码
  let specialNumber;
  do {
    specialNumber = Math.floor(Math.random() * 49) + 1;
  } while (numbers.includes(specialNumber));
  
  // 排序前6个号码
  numbers.sort((a, b) => a - b);
  
  // 转换为生肖
  const { ZodiacConfig } = require('../models');
  const allNumbers = [...numbers, specialNumber];
  const zodiacs = [];
  
  for (const num of allNumbers) {
    const zodiac = await ZodiacConfig.numberToZodiac(num);
    zodiacs.push(zodiac || '鼠'); // 默认生肖
  }
  
  // 创建开奖结果
  const result = await LotteryResult.create({
    period_id: periodId,
    number_1: numbers[0],
    number_2: numbers[1],
    number_3: numbers[2],
    number_4: numbers[3],
    number_5: numbers[4],
    number_6: numbers[5],
    special_number: specialNumber,
    zodiac_1: zodiacs[0],
    zodiac_2: zodiacs[1],
    zodiac_3: zodiacs[2],
    zodiac_4: zodiacs[3],
    zodiac_5: zodiacs[4],
    zodiac_6: zodiacs[5],
    special_zodiac: zodiacs[6]
  });
  
  return result;
};

// 创建下一期
const createNextPeriod = async () => {
  try {
    const latestPeriod = await LotteryPeriod.findOne({
      order: [['period_number', 'DESC']]
    });
    
    if (!latestPeriod) return;
    
    const nextPeriodNumber = latestPeriod.period_number + 1;
    const nextDrawTime = moment().add(1, 'day').hour(22).minute(39).second(0);
    
    // 检查是否已存在下一期
    const existingPeriod = await LotteryPeriod.findOne({
      where: { period_number: nextPeriodNumber }
    });
    
    if (!existingPeriod) {
      await LotteryPeriod.create({
        period_number: nextPeriodNumber,
        draw_time: nextDrawTime.toDate(),
        status: 'pending'
      });
      
      console.log(`创建下一期: 第${nextPeriodNumber}期`);
    }
  } catch (error) {
    console.error('创建下一期失败:', error);
  }
};

// 更新预测数据
const updatePredictions = async () => {
  try {
    const currentPeriod = await LotteryPeriod.getCurrentPeriod();
    if (!currentPeriod) return;
    
    // 生成各种预测数据
    await predictionService.generateAllPredictions(currentPeriod.id);
    
    console.log(`预测数据已更新 - 第${currentPeriod.period_number}期`);
  } catch (error) {
    console.error('更新预测数据失败:', error);
  }
};

// 手动触发开奖（用于测试）
const manualDraw = async (periodNumber) => {
  try {
    const period = await LotteryPeriod.findOne({
      where: { 
        period_number: periodNumber,
        status: 'pending'
      }
    });
    
    if (!period) {
      throw new Error('期数不存在或已开奖');
    }
    
    const result = await generateLotteryResult(period.id);
    period.status = 'drawn';
    await period.save();
    
    await broadcastNewResult(result);
    
    return result;
  } catch (error) {
    console.error('手动开奖失败:', error);
    throw error;
  }
};

module.exports = {
  startCronJobs,
  manualDraw,
  generateLotteryResult,
  createNextPeriod
};
