const { Prediction, ZodiacConfig, ColorConfig, ElementConfig } = require('../models');

// 生成所有预测数据
const generateAllPredictions = async (periodId) => {
  try {
    // 清除该期的旧预测数据
    await Prediction.destroy({
      where: { period_id: periodId }
    });

    // 生成各种预测
    await Promise.all([
      generateOneZodiacOneCode(periodId),
      generateLoseAllThree(periodId),
      generateSurprise24Codes(periodId),
      generateColorSpecial(periodId),
      generateThreeHeadSpecial(periodId),
      generateSingleDouble(periodId),
      generateKillTwoZodiacOneTail(periodId),
      generateSevenTailSpecial(periodId),
      generateHomeWildSpecial(periodId),
      generateBigSmallSpecial(periodId),
      generateFlatOneTail(periodId),
      generateIdiomFlatSpecial(periodId),
      generateFourZodiacFourCode(periodId),
      generateFiveElementSpecial(periodId),
      generateKillOneDoor(periodId),
      generateMaleFemaleSpecial(periodId),
      generateHomeWildVS(periodId),
      generateBlackWhiteSpecial(periodId)
    ]);

    console.log(`所有预测数据生成完成 - 期数: ${periodId}`);
  } catch (error) {
    console.error('生成预测数据失败:', error);
    throw error;
  }
};

// 一肖一码
const generateOneZodiacOneCode = async (periodId) => {
  const zodiacs = ['狗', '猴', '虎', '鸡', '兔'];
  const codes = ['08', '32', '46', '04', '45', '27', '35', '12'];
  
  const data = {
    seven_zodiacs: '狗猴虎鸡兔羊马',
    five_zodiacs: '狗猴虎鸡兔',
    three_zodiacs: '狗猴虎',
    two_zodiacs: '狗猴',
    one_zodiac: '狗',
    eight_codes: codes
  };

  return await Prediction.create({
    period_id: periodId,
    prediction_type: Prediction.TYPES.ONE_ZODIAC_ONE_CODE,
    prediction_data: data
  });
};

// 输尽光三肖
const generateLoseAllThree = async (periodId) => {
  const loseZodiacs = ['猴', '马', '蛇'];
  
  const data = {
    lose_zodiacs: loseZodiacs,
    description: `今期买${loseZodiacs.join('')}输尽光`
  };

  return await Prediction.create({
    period_id: periodId,
    prediction_type: Prediction.TYPES.LOSE_ALL_THREE,
    prediction_data: data
  });
};

// 惊喜24码
const generateSurprise24Codes = async (periodId) => {
  const codes = [];
  while (codes.length < 24) {
    const code = Math.floor(Math.random() * 49) + 1;
    if (!codes.includes(code)) {
      codes.push(code.toString().padStart(2, '0'));
    }
  }
  
  const data = {
    codes: codes.sort((a, b) => parseInt(a) - parseInt(b))
  };

  return await Prediction.create({
    period_id: periodId,
    prediction_type: Prediction.TYPES.SURPRISE_24_CODES,
    prediction_data: data
  });
};

// 波色中特
const generateColorSpecial = async (periodId) => {
  const colors = ['绿波', '红波'];
  
  const data = {
    colors: colors
  };

  return await Prediction.create({
    period_id: periodId,
    prediction_type: Prediction.TYPES.COLOR_SPECIAL,
    prediction_data: data
  });
};

// 三头中特
const generateThreeHeadSpecial = async (periodId) => {
  const heads = ['1', '2', '4'];
  
  const data = {
    heads: heads
  };

  return await Prediction.create({
    period_id: periodId,
    prediction_type: Prediction.TYPES.THREE_HEAD_SPECIAL,
    prediction_data: data
  });
};

// 单双中特
const generateSingleDouble = async (periodId) => {
  const type = Math.random() > 0.5 ? '单数' : '双数';
  
  const data = {
    type: type
  };

  return await Prediction.create({
    period_id: periodId,
    prediction_type: Prediction.TYPES.SINGLE_DOUBLE,
    prediction_data: data
  });
};

// 杀二肖一尾
const generateKillTwoZodiacOneTail = async (periodId) => {
  const killZodiacs = ['狗', '蛇'];
  const killTail = '8';
  
  const data = {
    kill_zodiacs: killZodiacs,
    kill_tail: killTail
  };

  return await Prediction.create({
    period_id: periodId,
    prediction_type: Prediction.TYPES.KILL_TWO_ZODIAC_ONE_TAIL,
    prediction_data: data
  });
};

// 七尾中特
const generateSevenTailSpecial = async (periodId) => {
  const tails = ['1', '2', '3', '5', '6', '7', '8'];
  
  const data = {
    tails: tails
  };

  return await Prediction.create({
    period_id: periodId,
    prediction_type: Prediction.TYPES.SEVEN_TAIL_SPECIAL,
    prediction_data: data
  });
};

// 家野中特
const generateHomeWildSpecial = async (periodId) => {
  const type = '家禽';
  
  const data = {
    type: type
  };

  return await Prediction.create({
    period_id: periodId,
    prediction_type: Prediction.TYPES.HOME_WILD_SPECIAL,
    prediction_data: data
  });
};

// 大小中特
const generateBigSmallSpecial = async (periodId) => {
  const type = '大大大大';
  
  const data = {
    type: type
  };

  return await Prediction.create({
    period_id: periodId,
    prediction_type: Prediction.TYPES.BIG_SMALL_SPECIAL,
    prediction_data: data
  });
};

// 平特一尾
const generateFlatOneTail = async (periodId) => {
  const tail = '1';
  
  const data = {
    tail: tail.repeat(5) // 11111
  };

  return await Prediction.create({
    period_id: periodId,
    prediction_type: Prediction.TYPES.FLAT_ONE_TAIL,
    prediction_data: data
  });
};

// 成语出平特
const generateIdiomFlatSpecial = async (periodId) => {
  const idioms = ['鸡犬不宁', '千军万马', '鸡飞蛋打', '引蛇出洞'];
  const idiom = idioms[Math.floor(Math.random() * idioms.length)];
  
  const data = {
    idiom: idiom
  };

  return await Prediction.create({
    period_id: periodId,
    prediction_type: Prediction.TYPES.IDIOM_FLAT_SPECIAL,
    prediction_data: data
  });
};

// 四肖四码
const generateFourZodiacFourCode = async (periodId) => {
  const zodiacs = ['鼠', '龙', '马', '鸡'];
  const codes = ['11', '23', '03', '27'];
  
  const data = {
    zodiacs: zodiacs,
    codes: codes
  };

  return await Prediction.create({
    period_id: periodId,
    prediction_type: Prediction.TYPES.FOUR_ZODIAC_FOUR_CODE,
    prediction_data: data
  });
};

// 五行中特
const generateFiveElementSpecial = async (periodId) => {
  const elements = ['土', '金', '水'];
  
  const data = {
    elements: elements
  };

  return await Prediction.create({
    period_id: periodId,
    prediction_type: Prediction.TYPES.FIVE_ELEMENT_SPECIAL,
    prediction_data: data
  });
};

// 杀一门
const generateKillOneDoor = async (periodId) => {
  const door = '3门';
  
  const data = {
    kill_door: door
  };

  return await Prediction.create({
    period_id: periodId,
    prediction_type: Prediction.TYPES.KILL_ONE_DOOR,
    prediction_data: data
  });
};

// 男女特肖
const generateMaleFemaleSpecial = async (periodId) => {
  const gender = '男肖';
  
  const data = {
    gender: gender
  };

  return await Prediction.create({
    period_id: periodId,
    prediction_type: Prediction.TYPES.MALE_FEMALE_SPECIAL,
    prediction_data: data
  });
};

// 家禽VS野兽
const generateHomeWildVS = async (periodId) => {
  const prediction = '家禽+兔鼠';
  
  const data = {
    prediction: prediction
  };

  return await Prediction.create({
    period_id: periodId,
    prediction_type: Prediction.TYPES.HOME_WILD_VS,
    prediction_data: data
  });
};

// 黑白肖中特
const generateBlackWhiteSpecial = async (periodId) => {
  const type = '白肖';
  
  const data = {
    type: type
  };

  return await Prediction.create({
    period_id: periodId,
    prediction_type: Prediction.TYPES.BLACK_WHITE_SPECIAL,
    prediction_data: data
  });
};

module.exports = {
  generateAllPredictions
};
