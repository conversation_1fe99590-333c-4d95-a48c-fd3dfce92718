const { LotteryPeriod, LotteryResult } = require('../models');

let io = null;

const initializeSocket = (socketIo) => {
  io = socketIo;
  
  io.on('connection', (socket) => {
    console.log('用户连接:', socket.id);
    
    // 发送当前期数和倒计时信息
    sendCurrentPeriodInfo(socket);
    
    // 处理客户端请求当前期数信息
    socket.on('request_current_period', () => {
      sendCurrentPeriodInfo(socket);
    });
    
    // 处理客户端请求历史开奖结果
    socket.on('request_history_results', async (data) => {
      try {
        const limit = data?.limit || 10;
        const results = await LotteryResult.getHistoryResults(limit);
        socket.emit('history_results', results);
      } catch (error) {
        console.error('获取历史结果失败:', error);
        socket.emit('error', { message: '获取历史结果失败' });
      }
    });
    
    // 处理断开连接
    socket.on('disconnect', () => {
      console.log('用户断开连接:', socket.id);
    });
  });
};

// 发送当前期数信息
const sendCurrentPeriodInfo = async (socket) => {
  try {
    const currentPeriod = await LotteryPeriod.getCurrentPeriod();
    const latestResult = await LotteryPeriod.getLatestResult();
    
    if (currentPeriod) {
      const timeUntilDraw = currentPeriod.getTimeUntilDraw();
      
      socket.emit('current_period', {
        period: currentPeriod,
        timeUntilDraw,
        latestResult
      });
    }
  } catch (error) {
    console.error('发送当前期数信息失败:', error);
  }
};

// 广播新的开奖结果
const broadcastNewResult = async (result) => {
  if (!io) return;
  
  try {
    const resultWithPeriod = await LotteryResult.findByPk(result.id, {
      include: ['period']
    });
    
    io.emit('new_result', resultWithPeriod);
    console.log('广播新开奖结果:', resultWithPeriod.period.period_number);
  } catch (error) {
    console.error('广播开奖结果失败:', error);
  }
};

// 广播倒计时更新
const broadcastCountdownUpdate = async () => {
  if (!io) return;
  
  try {
    const currentPeriod = await LotteryPeriod.getCurrentPeriod();
    if (currentPeriod) {
      const timeUntilDraw = currentPeriod.getTimeUntilDraw();
      
      io.emit('countdown_update', {
        periodNumber: currentPeriod.period_number,
        timeUntilDraw
      });
    }
  } catch (error) {
    console.error('广播倒计时更新失败:', error);
  }
};

// 广播预测数据更新
const broadcastPredictionUpdate = (prediction) => {
  if (!io) return;
  
  io.emit('prediction_update', prediction);
  console.log('广播预测数据更新:', prediction.prediction_type);
};

// 广播系统消息
const broadcastSystemMessage = (message) => {
  if (!io) return;
  
  io.emit('system_message', {
    message,
    timestamp: new Date()
  });
};

// 获取在线用户数
const getOnlineUsersCount = () => {
  return io ? io.engine.clientsCount : 0;
};

module.exports = {
  initializeSocket,
  broadcastNewResult,
  broadcastCountdownUpdate,
  broadcastPredictionUpdate,
  broadcastSystemMessage,
  getOnlineUsersCount
};
