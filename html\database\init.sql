-- 彩票网站数据库初始化脚本
-- 创建数据库
CREATE DATABASE IF NOT EXISTS lottery_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE lottery_db;

-- 开奖期数表
CREATE TABLE IF NOT EXISTS lottery_periods (
    id INT AUTO_INCREMENT PRIMARY KEY,
    period_number INT NOT NULL UNIQUE COMMENT '期数',
    draw_time DATETIME NOT NULL COMMENT '开奖时间',
    status ENUM('pending', 'drawn', 'cancelled') DEFAULT 'pending' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_period_number (period_number),
    INDEX idx_draw_time (draw_time),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='开奖期数表';

-- 开奖结果表
CREATE TABLE IF NOT EXISTS lottery_results (
    id INT AUTO_INCREMENT PRIMARY KEY,
    period_id INT NOT NULL COMMENT '期数ID',
    number_1 INT NOT NULL COMMENT '第1个号码',
    number_2 INT NOT NULL COMMENT '第2个号码',
    number_3 INT NOT NULL COMMENT '第3个号码',
    number_4 INT NOT NULL COMMENT '第4个号码',
    number_5 INT NOT NULL COMMENT '第5个号码',
    number_6 INT NOT NULL COMMENT '第6个号码',
    special_number INT NOT NULL COMMENT '特别号码',
    zodiac_1 VARCHAR(10) NOT NULL COMMENT '第1个生肖',
    zodiac_2 VARCHAR(10) NOT NULL COMMENT '第2个生肖',
    zodiac_3 VARCHAR(10) NOT NULL COMMENT '第3个生肖',
    zodiac_4 VARCHAR(10) NOT NULL COMMENT '第4个生肖',
    zodiac_5 VARCHAR(10) NOT NULL COMMENT '第5个生肖',
    zodiac_6 VARCHAR(10) NOT NULL COMMENT '第6个生肖',
    special_zodiac VARCHAR(10) NOT NULL COMMENT '特别生肖',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (period_id) REFERENCES lottery_periods(id) ON DELETE CASCADE,
    INDEX idx_period_id (period_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='开奖结果表';

-- 预测数据表
CREATE TABLE IF NOT EXISTS predictions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    period_id INT NOT NULL COMMENT '期数ID',
    prediction_type VARCHAR(50) NOT NULL COMMENT '预测类型',
    prediction_data JSON NOT NULL COMMENT '预测数据',
    accuracy_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '准确率',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (period_id) REFERENCES lottery_periods(id) ON DELETE CASCADE,
    INDEX idx_period_id (period_id),
    INDEX idx_prediction_type (prediction_type),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='预测数据表';

-- 生肖配置表
CREATE TABLE IF NOT EXISTS zodiac_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    zodiac_name VARCHAR(10) NOT NULL UNIQUE COMMENT '生肖名称',
    numbers JSON NOT NULL COMMENT '对应号码',
    color VARCHAR(20) NOT NULL COMMENT '波色',
    element VARCHAR(10) NOT NULL COMMENT '五行',
    gender ENUM('male', 'female') NOT NULL COMMENT '性别',
    type ENUM('domestic', 'wild') NOT NULL COMMENT '家禽/野兽',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='生肖配置表';

-- 波色配置表
CREATE TABLE IF NOT EXISTS color_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    color_name VARCHAR(20) NOT NULL UNIQUE COMMENT '波色名称',
    numbers JSON NOT NULL COMMENT '对应号码',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='波色配置表';

-- 五行配置表
CREATE TABLE IF NOT EXISTS element_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    element_name VARCHAR(10) NOT NULL UNIQUE COMMENT '五行名称',
    numbers JSON NOT NULL COMMENT '对应号码',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='五行配置表';

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    email VARCHAR(100) UNIQUE COMMENT '邮箱',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    role ENUM('admin', 'user') DEFAULT 'user' COMMENT '角色',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    last_login TIMESTAMP NULL COMMENT '最后登录时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_role (role)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 系统配置表
CREATE TABLE IF NOT EXISTS system_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT NOT NULL COMMENT '配置值',
    description VARCHAR(255) COMMENT '描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_config_key (config_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 插入初始数据
-- 生肖配置数据
INSERT INTO zodiac_config (zodiac_name, numbers, color, element, gender, type) VALUES
('鼠', '[6, 18, 30, 42]', '绿', '水', 'male', 'wild'),
('牛', '[5, 17, 29, 41]', '红', '土', 'male', 'domestic'),
('虎', '[4, 16, 28, 40]', '绿', '木', 'male', 'wild'),
('兔', '[3, 15, 27, 39]', '绿', '木', 'female', 'wild'),
('龙', '[2, 14, 26, 38]', '红', '土', 'male', 'wild'),
('蛇', '[1, 13, 25, 37, 49]', '红', '火', 'female', 'wild'),
('马', '[12, 24, 36, 48]', '红', '火', 'male', 'domestic'),
('羊', '[11, 23, 35, 47]', '绿', '土', 'female', 'domestic'),
('猴', '[10, 22, 34, 46]', '蓝', '金', 'male', 'wild'),
('鸡', '[9, 21, 33, 45]', '红', '金', 'female', 'domestic'),
('狗', '[8, 20, 32, 44]', '绿', '土', 'male', 'domestic'),
('猪', '[7, 19, 31, 43]', '蓝', '水', 'female', 'domestic');

-- 波色配置数据
INSERT INTO color_config (color_name, numbers) VALUES
('红波', '[1, 2, 7, 8, 12, 13, 18, 19, 23, 24, 29, 30, 34, 35, 40, 45, 46]'),
('蓝波', '[3, 4, 9, 10, 14, 15, 20, 25, 26, 31, 36, 37, 41, 42, 47, 48]'),
('绿波', '[5, 6, 11, 16, 17, 21, 22, 27, 28, 32, 33, 38, 39, 43, 44, 49]');

-- 五行配置数据
INSERT INTO element_config (element_name, numbers) VALUES
('金', '[3, 4, 11, 12, 25, 26, 33, 34, 41, 42]'),
('木', '[7, 8, 15, 16, 23, 24, 37, 38, 45, 46]'),
('水', '[13, 14, 21, 22, 29, 30, 43, 44]'),
('火', '[1, 2, 9, 10, 17, 18, 31, 32, 39, 40, 47, 48]'),
('土', '[5, 6, 19, 20, 27, 28, 35, 36, 49]');

-- 插入当前期数
INSERT INTO lottery_periods (period_number, draw_time, status) VALUES
(187, '2025-07-07 22:39:00', 'drawn'),
(188, '2025-07-08 22:39:00', 'pending');

-- 插入第187期开奖结果
INSERT INTO lottery_results (period_id, number_1, number_2, number_3, number_4, number_5, number_6, special_number, zodiac_1, zodiac_2, zodiac_3, zodiac_4, zodiac_5, zodiac_6, special_zodiac) VALUES
(1, 8, 17, 48, 11, 4, 45, 37, '狗', '牛', '马', '羊', '虎', '鸡', '蛇');

-- 插入系统配置
INSERT INTO system_config (config_key, config_value, description) VALUES
('site_title', '澳门葡京新彩', '网站标题'),
('current_period', '188', '当前期数'),
('draw_time_interval', '86400', '开奖时间间隔(秒)'),
('countdown_time', '22:39:00', '每日开奖时间');

-- 创建管理员用户
INSERT INTO users (username, email, password_hash, role) VALUES
('admin', '<EMAIL>', '$2b$10$rQZ8kqH5rJ5rJ5rJ5rJ5rOQZ8kqH5rJ5rJ5rJ5rJ5rOQZ8kqH5rJ5r', 'admin');
