[{"D:\\liu\\html\\frontend\\src\\index.tsx": "1", "D:\\liu\\html\\frontend\\src\\App.tsx": "2", "D:\\liu\\html\\frontend\\src\\pages\\PredictionPage.tsx": "3", "D:\\liu\\html\\frontend\\src\\services\\socketService.ts": "4", "D:\\liu\\html\\frontend\\src\\services\\apiService.ts": "5", "D:\\liu\\html\\frontend\\src\\pages\\HomePage.tsx": "6", "D:\\liu\\html\\frontend\\src\\pages\\StatisticsPage.tsx": "7", "D:\\liu\\html\\frontend\\src\\pages\\SettingsPage.tsx": "8", "D:\\liu\\html\\frontend\\src\\pages\\HistoryPage.tsx": "9", "D:\\liu\\html\\frontend\\src\\components\\CountdownDisplay.tsx": "10", "D:\\liu\\html\\frontend\\src\\components\\Header.tsx": "11", "D:\\liu\\html\\frontend\\src\\components\\PredictionSection.tsx": "12", "D:\\liu\\html\\frontend\\src\\components\\ZodiacTable.tsx": "13", "D:\\liu\\html\\frontend\\src\\components\\LotterySelector.tsx": "14", "D:\\liu\\html\\frontend\\src\\components\\LatestResults.tsx": "15"}, {"size": 1341, "mtime": 1751891081516, "results": "16", "hashOfConfig": "17"}, {"size": 2838, "mtime": 1751896984289, "results": "18", "hashOfConfig": "17"}, {"size": 9491, "mtime": 1751891348641, "results": "19", "hashOfConfig": "17"}, {"size": 3827, "mtime": 1751882506675, "results": "20", "hashOfConfig": "17"}, {"size": 6553, "mtime": 1751896965700, "results": "21", "hashOfConfig": "17"}, {"size": 9001, "mtime": 1751891264420, "results": "22", "hashOfConfig": "17"}, {"size": 1369, "mtime": 1751882706679, "results": "23", "hashOfConfig": "17"}, {"size": 1613, "mtime": 1751882720343, "results": "24", "hashOfConfig": "17"}, {"size": 4626, "mtime": 1751891228853, "results": "25", "hashOfConfig": "17"}, {"size": 3006, "mtime": 1751896816166, "results": "26", "hashOfConfig": "17"}, {"size": 2723, "mtime": 1751893888567, "results": "27", "hashOfConfig": "17"}, {"size": 7166, "mtime": 1751893678415, "results": "28", "hashOfConfig": "17"}, {"size": 5928, "mtime": 1751893717251, "results": "29", "hashOfConfig": "17"}, {"size": 1527, "mtime": 1751893580988, "results": "30", "hashOfConfig": "17"}, {"size": 4442, "mtime": 1751896838674, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ubg53z", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\liu\\html\\frontend\\src\\index.tsx", [], [], "D:\\liu\\html\\frontend\\src\\App.tsx", [], [], "D:\\liu\\html\\frontend\\src\\pages\\PredictionPage.tsx", ["77", "78"], [], "D:\\liu\\html\\frontend\\src\\services\\socketService.ts", [], [], "D:\\liu\\html\\frontend\\src\\services\\apiService.ts", [], [], "D:\\liu\\html\\frontend\\src\\pages\\HomePage.tsx", ["79"], [], "D:\\liu\\html\\frontend\\src\\pages\\StatisticsPage.tsx", ["80"], [], "D:\\liu\\html\\frontend\\src\\pages\\SettingsPage.tsx", [], [], "D:\\liu\\html\\frontend\\src\\pages\\HistoryPage.tsx", ["81"], [], "D:\\liu\\html\\frontend\\src\\components\\CountdownDisplay.tsx", [], [], "D:\\liu\\html\\frontend\\src\\components\\Header.tsx", [], [], "D:\\liu\\html\\frontend\\src\\components\\PredictionSection.tsx", [], [], "D:\\liu\\html\\frontend\\src\\components\\ZodiacTable.tsx", [], [], "D:\\liu\\html\\frontend\\src\\components\\LotterySelector.tsx", [], [], "D:\\liu\\html\\frontend\\src\\components\\LatestResults.tsx", ["82"], [], {"ruleId": "83", "severity": 1, "message": "84", "line": 9, "column": 9, "nodeType": "85", "messageId": "86", "endLine": 9, "endColumn": 16}, {"ruleId": "87", "severity": 1, "message": "88", "line": 109, "column": 6, "nodeType": "89", "endLine": 109, "endColumn": 8, "suggestions": "90"}, {"ruleId": "87", "severity": 1, "message": "88", "line": 121, "column": 6, "nodeType": "89", "endLine": 121, "endColumn": 8, "suggestions": "91"}, {"ruleId": "83", "severity": 1, "message": "92", "line": 10, "column": 10, "nodeType": "85", "messageId": "86", "endLine": 10, "endColumn": 15}, {"ruleId": "83", "severity": 1, "message": "93", "line": 2, "column": 29, "nodeType": "85", "messageId": "86", "endLine": 2, "endColumn": 33}, {"ruleId": "83", "severity": 1, "message": "94", "line": 16, "column": 7, "nodeType": "85", "messageId": "86", "endLine": 16, "endColumn": 20}, "@typescript-eslint/no-unused-vars", "'TabPane' is assigned a value but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'setupSocketListeners'. Either include it or remove the dependency array.", "ArrayExpression", ["95"], ["96"], "'stats' is assigned a value but never used.", "'Spin' is defined but never used.", "'ResultsHeader' is assigned a value but never used.", {"desc": "97", "fix": "98"}, {"desc": "97", "fix": "99"}, "Update the dependencies array to be: [setupSocketListeners]", {"range": "100", "text": "101"}, {"range": "102", "text": "101"}, [2233, 2235], "[setupSocketListeners]", [2966, 2968]]