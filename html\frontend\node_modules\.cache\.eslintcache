[{"D:\\liu\\html\\frontend\\src\\index.tsx": "1", "D:\\liu\\html\\frontend\\src\\App.tsx": "2", "D:\\liu\\html\\frontend\\src\\pages\\PredictionPage.tsx": "3", "D:\\liu\\html\\frontend\\src\\services\\socketService.ts": "4", "D:\\liu\\html\\frontend\\src\\services\\apiService.ts": "5", "D:\\liu\\html\\frontend\\src\\pages\\HomePage.tsx": "6", "D:\\liu\\html\\frontend\\src\\pages\\StatisticsPage.tsx": "7", "D:\\liu\\html\\frontend\\src\\pages\\SettingsPage.tsx": "8", "D:\\liu\\html\\frontend\\src\\pages\\HistoryPage.tsx": "9"}, {"size": 1341, "mtime": 1751891081516, "results": "10", "hashOfConfig": "11"}, {"size": 5639, "mtime": 1751882411054, "results": "12", "hashOfConfig": "11"}, {"size": 9491, "mtime": 1751891348641, "results": "13", "hashOfConfig": "11"}, {"size": 3827, "mtime": 1751882506675, "results": "14", "hashOfConfig": "11"}, {"size": 6562, "mtime": 1751882540861, "results": "15", "hashOfConfig": "11"}, {"size": 9001, "mtime": 1751891264420, "results": "16", "hashOfConfig": "11"}, {"size": 1369, "mtime": 1751882706679, "results": "17", "hashOfConfig": "11"}, {"size": 1613, "mtime": 1751882720343, "results": "18", "hashOfConfig": "11"}, {"size": 4626, "mtime": 1751891228853, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ubg53z", {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\liu\\html\\frontend\\src\\index.tsx", [], [], "D:\\liu\\html\\frontend\\src\\App.tsx", [], [], "D:\\liu\\html\\frontend\\src\\pages\\PredictionPage.tsx", ["47", "48"], [], "D:\\liu\\html\\frontend\\src\\services\\socketService.ts", [], [], "D:\\liu\\html\\frontend\\src\\services\\apiService.ts", [], [], "D:\\liu\\html\\frontend\\src\\pages\\HomePage.tsx", ["49"], [], "D:\\liu\\html\\frontend\\src\\pages\\StatisticsPage.tsx", ["50"], [], "D:\\liu\\html\\frontend\\src\\pages\\SettingsPage.tsx", [], [], "D:\\liu\\html\\frontend\\src\\pages\\HistoryPage.tsx", ["51"], [], {"ruleId": "52", "severity": 1, "message": "53", "line": 9, "column": 9, "nodeType": "54", "messageId": "55", "endLine": 9, "endColumn": 16}, {"ruleId": "56", "severity": 1, "message": "57", "line": 109, "column": 6, "nodeType": "58", "endLine": 109, "endColumn": 8, "suggestions": "59"}, {"ruleId": "56", "severity": 1, "message": "57", "line": 121, "column": 6, "nodeType": "58", "endLine": 121, "endColumn": 8, "suggestions": "60"}, {"ruleId": "52", "severity": 1, "message": "61", "line": 10, "column": 10, "nodeType": "54", "messageId": "55", "endLine": 10, "endColumn": 15}, {"ruleId": "52", "severity": 1, "message": "62", "line": 2, "column": 29, "nodeType": "54", "messageId": "55", "endLine": 2, "endColumn": 33}, "@typescript-eslint/no-unused-vars", "'TabPane' is assigned a value but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'setupSocketListeners'. Either include it or remove the dependency array.", "ArrayExpression", ["63"], ["64"], "'stats' is assigned a value but never used.", "'Spin' is defined but never used.", {"desc": "65", "fix": "66"}, {"desc": "65", "fix": "67"}, "Update the dependencies array to be: [setupSocketListeners]", {"range": "68", "text": "69"}, {"range": "70", "text": "69"}, [2233, 2235], "[setupSocketListeners]", [2966, 2968]]