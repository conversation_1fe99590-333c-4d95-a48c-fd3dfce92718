{"ast": null, "code": "var _jsxFileName = \"D:\\\\liu\\\\html\\\\frontend\\\\src\\\\pages\\\\PredictionPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Row, Col, Card, Typography, Spin, message, Tabs, Tag } from 'antd';\nimport { ThunderboltOutlined, TrophyOutlined, FireOutlined } from '@ant-design/icons';\nimport styled from 'styled-components';\nimport { apiService } from '../services/apiService';\nimport { socketService } from '../services/socketService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  TabPane\n} = Tabs;\n\n// 样式组件\nconst StyledCard = styled(Card)`\n  margin-bottom: 16px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  border-radius: 8px;\n  \n  &:hover {\n    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\n    transform: translateY(-2px);\n    transition: all 0.3s ease;\n  }\n`;\nconst PredictionCard = styled(StyledCard)`\n  .prediction-title {\n    font-size: 16px;\n    font-weight: bold;\n    color: #1890ff;\n    margin-bottom: 8px;\n  }\n  \n  .prediction-content {\n    color: #666;\n    line-height: 1.6;\n  }\n  \n  .accuracy-rate {\n    margin-top: 8px;\n    text-align: right;\n  }\n`;\n_c = PredictionCard;\nconst PredictionData = styled.div`\n  margin: 12px 0;\n  \n  .data-item {\n    margin: 8px 0;\n    padding: 8px 12px;\n    background: #f5f5f5;\n    border-radius: 4px;\n    border-left: 4px solid #1890ff;\n  }\n  \n  .data-label {\n    font-weight: bold;\n    color: #333;\n    margin-right: 8px;\n  }\n  \n  .data-value {\n    color: #666;\n  }\n`;\n_c2 = PredictionData;\nconst CodeList = styled.div`\n  display: flex;\n  flex-wrap: wrap;\n  gap: 4px;\n  margin-top: 8px;\n  \n  .code-item {\n    background: #1890ff;\n    color: white;\n    padding: 2px 6px;\n    border-radius: 4px;\n    font-size: 12px;\n    font-weight: bold;\n  }\n`;\n_c3 = CodeList;\nconst ZodiacList = styled.div`\n  display: flex;\n  flex-wrap: wrap;\n  gap: 4px;\n  margin-top: 8px;\n  \n  .zodiac-item {\n    background: #52c41a;\n    color: white;\n    padding: 2px 6px;\n    border-radius: 4px;\n    font-size: 12px;\n  }\n`;\n_c4 = ZodiacList;\nconst PredictionPage = () => {\n  _s();\n  const [loading, setLoading] = useState(true);\n  const [predictions, setPredictions] = useState([]);\n  const [currentPeriod, setCurrentPeriod] = useState(null);\n  useEffect(() => {\n    loadPredictions();\n    setupSocketListeners();\n    return () => {\n      socketService.off('prediction_update');\n      socketService.off('current_period');\n    };\n  }, []);\n  const loadPredictions = async () => {\n    try {\n      const [predictionsData, currentData] = await Promise.all([apiService.getCurrentPredictions(), apiService.getCurrentPeriod()]);\n      setPredictions(predictionsData);\n      setCurrentPeriod(currentData.currentPeriod);\n      setLoading(false);\n    } catch (error) {\n      console.error('加载预测数据失败:', error);\n      message.error('加载预测数据失败');\n      setLoading(false);\n    }\n  };\n  const setupSocketListeners = () => {\n    socketService.onPredictionUpdate(prediction => {\n      message.info('预测数据已更新');\n      loadPredictions();\n    });\n    socketService.onCurrentPeriod(data => {\n      setCurrentPeriod(data.currentPeriod);\n    });\n  };\n  const renderPredictionContent = prediction => {\n    var _prediction_data$eigh, _prediction_data$lose, _prediction_data$code, _prediction_data$colo;\n    const {\n      prediction_type,\n      prediction_data\n    } = prediction;\n    switch (prediction_type) {\n      case 'one_zodiac_one_code':\n        return /*#__PURE__*/_jsxDEV(PredictionData, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"data-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"data-label\",\n              children: \"\\u4E03\\u8096:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"data-value\",\n              children: prediction_data.seven_zodiacs\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"data-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"data-label\",\n              children: \"\\u4E94\\u8096:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"data-value\",\n              children: prediction_data.five_zodiacs\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"data-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"data-label\",\n              children: \"\\u4E09\\u8096:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"data-value\",\n              children: prediction_data.three_zodiacs\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"data-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"data-label\",\n              children: \"\\u4E00\\u8096:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"data-value\",\n              children: prediction_data.one_zodiac\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"data-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"data-label\",\n              children: \"\\u516B\\u7801:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(CodeList, {\n              children: (_prediction_data$eigh = prediction_data.eight_codes) === null || _prediction_data$eigh === void 0 ? void 0 : _prediction_data$eigh.map((code, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"code-item\",\n                children: code\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this);\n      case 'lose_all_three':\n        return /*#__PURE__*/_jsxDEV(PredictionData, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"data-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"data-label\",\n              children: \"\\u8F93\\u5C3D\\u5149:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ZodiacList, {\n              children: (_prediction_data$lose = prediction_data.lose_zodiacs) === null || _prediction_data$lose === void 0 ? void 0 : _prediction_data$lose.map((zodiac, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"zodiac-item\",\n                children: zodiac\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"data-item\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"data-value\",\n              children: prediction_data.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this);\n      case 'surprise_24_codes':\n        return /*#__PURE__*/_jsxDEV(PredictionData, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"data-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"data-label\",\n              children: \"\\u60CA\\u559C24\\u7801:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(CodeList, {\n              children: (_prediction_data$code = prediction_data.codes) === null || _prediction_data$code === void 0 ? void 0 : _prediction_data$code.map((code, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"code-item\",\n                children: code\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this);\n      case 'color_special':\n        return /*#__PURE__*/_jsxDEV(PredictionData, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"data-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"data-label\",\n              children: \"\\u6CE2\\u8272\\u4E2D\\u7279:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"data-value\",\n              children: (_prediction_data$colo = prediction_data.colors) === null || _prediction_data$colo === void 0 ? void 0 : _prediction_data$colo.join('、')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this);\n      case 'single_double':\n        return /*#__PURE__*/_jsxDEV(PredictionData, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"data-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"data-label\",\n              children: \"\\u5355\\u53CC\\u4E2D\\u7279:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"data-value\",\n              children: prediction_data.type\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 11\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(PredictionData, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"data-item\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"data-value\",\n              children: JSON.stringify(prediction_data, null, 2)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 11\n        }, this);\n    }\n  };\n  const getPredictionTitle = type => {\n    const titles = {\n      'one_zodiac_one_code': '一肖一码',\n      'lose_all_three': '输尽光三肖',\n      'surprise_24_codes': '惊喜24码',\n      'color_special': '波色中特',\n      'three_head_special': '三头中特',\n      'single_double': '单双中特',\n      'kill_two_zodiac_one_tail': '杀二肖一尾',\n      'seven_tail_special': '七尾中特',\n      'home_wild_special': '家野中特',\n      'big_small_special': '大小中特',\n      'flat_one_tail': '平特一尾',\n      'idiom_flat_special': '成语出平特',\n      'four_zodiac_four_code': '四肖四码',\n      'five_element_special': '五行中特',\n      'kill_one_door': '杀一门',\n      'male_female_special': '男女特肖',\n      'home_wild_vs': '家禽VS野兽',\n      'black_white_special': '黑白肖中特'\n    };\n    return titles[type] || type;\n  };\n  const getAccuracyColor = rate => {\n    if (rate >= 80) return 'success';\n    if (rate >= 60) return 'warning';\n    return 'error';\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-container\",\n      children: /*#__PURE__*/_jsxDEV(Spin, {\n        size: \"large\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 269,\n      columnNumber: 7\n    }, this);\n  }\n\n  // 按类型分组预测数据\n  const groupedPredictions = predictions.reduce((groups, prediction) => {\n    const type = prediction.prediction_type;\n    if (!groups[type]) {\n      groups[type] = [];\n    }\n    groups[type].push(prediction);\n    return groups;\n  }, {});\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      children: [/*#__PURE__*/_jsxDEV(ThunderboltOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 9\n      }, this), \" \\u9884\\u6D4B\\u4E2D\\u5FC3\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 287,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Text, {\n      type: \"secondary\",\n      children: [\"\\u7B2C \", (currentPeriod === null || currentPeriod === void 0 ? void 0 : currentPeriod.period_number) || '---', \" \\u671F\\u9884\\u6D4B\\u6570\\u636E\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 290,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginTop: 24\n      },\n      children: Object.entries(groupedPredictions).map(([type, typePredictions]) => {\n        var _typePredictions$, _typePredictions$2, _typePredictions$3;\n        return /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          lg: 8,\n          children: /*#__PURE__*/_jsxDEV(PredictionCard, {\n            title: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"prediction-title\",\n              children: [/*#__PURE__*/_jsxDEV(TrophyOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 19\n              }, this), \" \", getPredictionTitle(type)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 17\n            }, this),\n            extra: /*#__PURE__*/_jsxDEV(Tag, {\n              color: getAccuracyColor(((_typePredictions$ = typePredictions[0]) === null || _typePredictions$ === void 0 ? void 0 : _typePredictions$.accuracy_rate) || 0),\n              children: [(((_typePredictions$2 = typePredictions[0]) === null || _typePredictions$2 === void 0 ? void 0 : _typePredictions$2.accuracy_rate) || 0).toFixed(1), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 17\n            }, this),\n            children: [renderPredictionContent(typePredictions[0]), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"accuracy-rate\",\n              children: /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                style: {\n                  fontSize: 12\n                },\n                children: [\"\\u51C6\\u786E\\u7387: \", (((_typePredictions$3 = typePredictions[0]) === null || _typePredictions$3 === void 0 ? void 0 : _typePredictions$3.accuracy_rate) || 0).toFixed(1), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 13\n          }, this)\n        }, type, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 11\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 294,\n      columnNumber: 7\n    }, this), predictions.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"empty-container\",\n      children: [/*#__PURE__*/_jsxDEV(FireOutlined, {\n        style: {\n          fontSize: 48,\n          color: '#ccc',\n          marginBottom: 16\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 322,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"\\u6682\\u65E0\\u9884\\u6D4B\\u6570\\u636E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 323,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 321,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 286,\n    columnNumber: 5\n  }, this);\n};\n_s(PredictionPage, \"+nJUmAOhP+X/KrPC4AZwJamjfGc=\");\n_c5 = PredictionPage;\nexport default PredictionPage;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"PredictionCard\");\n$RefreshReg$(_c2, \"PredictionData\");\n$RefreshReg$(_c3, \"CodeList\");\n$RefreshReg$(_c4, \"ZodiacList\");\n$RefreshReg$(_c5, \"PredictionPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Row", "Col", "Card", "Typography", "Spin", "message", "Tabs", "Tag", "ThunderboltOutlined", "TrophyOutlined", "FireOutlined", "styled", "apiService", "socketService", "jsxDEV", "_jsxDEV", "Title", "Text", "TabPane", "StyledCard", "PredictionCard", "_c", "PredictionData", "div", "_c2", "CodeList", "_c3", "ZodiacList", "_c4", "PredictionPage", "_s", "loading", "setLoading", "predictions", "setPredictions", "currentPeriod", "setCurrentPeriod", "loadPredictions", "setupSocketListeners", "off", "predictionsData", "currentData", "Promise", "all", "getCurrentPredictions", "getCurrentPeriod", "error", "console", "onPredictionUpdate", "prediction", "info", "onCurrentPeriod", "data", "renderPredictionContent", "_prediction_data$eigh", "_prediction_data$lose", "_prediction_data$code", "_prediction_data$colo", "prediction_type", "prediction_data", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "seven_zodiacs", "five_zodiacs", "three_zodiacs", "one_zodiac", "eight_codes", "map", "code", "index", "lose_zodiacs", "zodiac", "description", "codes", "colors", "join", "type", "JSON", "stringify", "getPredictionTitle", "titles", "getAccuracyColor", "rate", "size", "groupedPredictions", "reduce", "groups", "push", "level", "period_number", "gutter", "style", "marginTop", "Object", "entries", "typePredictions", "_typePredictions$", "_typePredictions$2", "_typePredictions$3", "xs", "sm", "lg", "title", "extra", "color", "accuracy_rate", "toFixed", "fontSize", "length", "marginBottom", "_c5", "$RefreshReg$"], "sources": ["D:/liu/html/frontend/src/pages/PredictionPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Row, Col, Card, Typography, Spin, message, Tabs, Tag } from 'antd';\nimport { ThunderboltOutlined, TrophyOutlined, FireOutlined } from '@ant-design/icons';\nimport styled from 'styled-components';\nimport { apiService } from '../services/apiService';\nimport { socketService } from '../services/socketService';\n\nconst { Title, Text } = Typography;\nconst { TabPane } = Tabs;\n\n// 样式组件\nconst StyledCard = styled(Card)`\n  margin-bottom: 16px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  border-radius: 8px;\n  \n  &:hover {\n    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\n    transform: translateY(-2px);\n    transition: all 0.3s ease;\n  }\n`;\n\nconst PredictionCard = styled(StyledCard)`\n  .prediction-title {\n    font-size: 16px;\n    font-weight: bold;\n    color: #1890ff;\n    margin-bottom: 8px;\n  }\n  \n  .prediction-content {\n    color: #666;\n    line-height: 1.6;\n  }\n  \n  .accuracy-rate {\n    margin-top: 8px;\n    text-align: right;\n  }\n`;\n\nconst PredictionData = styled.div`\n  margin: 12px 0;\n  \n  .data-item {\n    margin: 8px 0;\n    padding: 8px 12px;\n    background: #f5f5f5;\n    border-radius: 4px;\n    border-left: 4px solid #1890ff;\n  }\n  \n  .data-label {\n    font-weight: bold;\n    color: #333;\n    margin-right: 8px;\n  }\n  \n  .data-value {\n    color: #666;\n  }\n`;\n\nconst CodeList = styled.div`\n  display: flex;\n  flex-wrap: wrap;\n  gap: 4px;\n  margin-top: 8px;\n  \n  .code-item {\n    background: #1890ff;\n    color: white;\n    padding: 2px 6px;\n    border-radius: 4px;\n    font-size: 12px;\n    font-weight: bold;\n  }\n`;\n\nconst ZodiacList = styled.div`\n  display: flex;\n  flex-wrap: wrap;\n  gap: 4px;\n  margin-top: 8px;\n  \n  .zodiac-item {\n    background: #52c41a;\n    color: white;\n    padding: 2px 6px;\n    border-radius: 4px;\n    font-size: 12px;\n  }\n`;\n\nconst PredictionPage: React.FC = () => {\n  const [loading, setLoading] = useState(true);\n  const [predictions, setPredictions] = useState<any[]>([]);\n  const [currentPeriod, setCurrentPeriod] = useState<any>(null);\n\n  useEffect(() => {\n    loadPredictions();\n    setupSocketListeners();\n    \n    return () => {\n      socketService.off('prediction_update');\n      socketService.off('current_period');\n    };\n  }, []);\n\n  const loadPredictions = async () => {\n    try {\n      const [predictionsData, currentData] = await Promise.all([\n        apiService.getCurrentPredictions(),\n        apiService.getCurrentPeriod()\n      ]);\n      \n      setPredictions(predictionsData);\n      setCurrentPeriod(currentData.currentPeriod);\n      setLoading(false);\n    } catch (error) {\n      console.error('加载预测数据失败:', error);\n      message.error('加载预测数据失败');\n      setLoading(false);\n    }\n  };\n\n  const setupSocketListeners = () => {\n    socketService.onPredictionUpdate((prediction: any) => {\n      message.info('预测数据已更新');\n      loadPredictions();\n    });\n\n    socketService.onCurrentPeriod((data: any) => {\n      setCurrentPeriod(data.currentPeriod);\n    });\n  };\n\n  const renderPredictionContent = (prediction: any) => {\n    const { prediction_type, prediction_data } = prediction;\n    \n    switch (prediction_type) {\n      case 'one_zodiac_one_code':\n        return (\n          <PredictionData>\n            <div className=\"data-item\">\n              <span className=\"data-label\">七肖:</span>\n              <span className=\"data-value\">{prediction_data.seven_zodiacs}</span>\n            </div>\n            <div className=\"data-item\">\n              <span className=\"data-label\">五肖:</span>\n              <span className=\"data-value\">{prediction_data.five_zodiacs}</span>\n            </div>\n            <div className=\"data-item\">\n              <span className=\"data-label\">三肖:</span>\n              <span className=\"data-value\">{prediction_data.three_zodiacs}</span>\n            </div>\n            <div className=\"data-item\">\n              <span className=\"data-label\">一肖:</span>\n              <span className=\"data-value\">{prediction_data.one_zodiac}</span>\n            </div>\n            <div className=\"data-item\">\n              <span className=\"data-label\">八码:</span>\n              <CodeList>\n                {prediction_data.eight_codes?.map((code: string, index: number) => (\n                  <span key={index} className=\"code-item\">{code}</span>\n                ))}\n              </CodeList>\n            </div>\n          </PredictionData>\n        );\n        \n      case 'lose_all_three':\n        return (\n          <PredictionData>\n            <div className=\"data-item\">\n              <span className=\"data-label\">输尽光:</span>\n              <ZodiacList>\n                {prediction_data.lose_zodiacs?.map((zodiac: string, index: number) => (\n                  <span key={index} className=\"zodiac-item\">{zodiac}</span>\n                ))}\n              </ZodiacList>\n            </div>\n            <div className=\"data-item\">\n              <span className=\"data-value\">{prediction_data.description}</span>\n            </div>\n          </PredictionData>\n        );\n        \n      case 'surprise_24_codes':\n        return (\n          <PredictionData>\n            <div className=\"data-item\">\n              <span className=\"data-label\">惊喜24码:</span>\n              <CodeList>\n                {prediction_data.codes?.map((code: string, index: number) => (\n                  <span key={index} className=\"code-item\">{code}</span>\n                ))}\n              </CodeList>\n            </div>\n          </PredictionData>\n        );\n        \n      case 'color_special':\n        return (\n          <PredictionData>\n            <div className=\"data-item\">\n              <span className=\"data-label\">波色中特:</span>\n              <span className=\"data-value\">{prediction_data.colors?.join('、')}</span>\n            </div>\n          </PredictionData>\n        );\n        \n      case 'single_double':\n        return (\n          <PredictionData>\n            <div className=\"data-item\">\n              <span className=\"data-label\">单双中特:</span>\n              <span className=\"data-value\">{prediction_data.type}</span>\n            </div>\n          </PredictionData>\n        );\n        \n      default:\n        return (\n          <PredictionData>\n            <div className=\"data-item\">\n              <span className=\"data-value\">\n                {JSON.stringify(prediction_data, null, 2)}\n              </span>\n            </div>\n          </PredictionData>\n        );\n    }\n  };\n\n  const getPredictionTitle = (type: string) => {\n    const titles: { [key: string]: string } = {\n      'one_zodiac_one_code': '一肖一码',\n      'lose_all_three': '输尽光三肖',\n      'surprise_24_codes': '惊喜24码',\n      'color_special': '波色中特',\n      'three_head_special': '三头中特',\n      'single_double': '单双中特',\n      'kill_two_zodiac_one_tail': '杀二肖一尾',\n      'seven_tail_special': '七尾中特',\n      'home_wild_special': '家野中特',\n      'big_small_special': '大小中特',\n      'flat_one_tail': '平特一尾',\n      'idiom_flat_special': '成语出平特',\n      'four_zodiac_four_code': '四肖四码',\n      'five_element_special': '五行中特',\n      'kill_one_door': '杀一门',\n      'male_female_special': '男女特肖',\n      'home_wild_vs': '家禽VS野兽',\n      'black_white_special': '黑白肖中特'\n    };\n    return titles[type] || type;\n  };\n\n  const getAccuracyColor = (rate: number) => {\n    if (rate >= 80) return 'success';\n    if (rate >= 60) return 'warning';\n    return 'error';\n  };\n\n  if (loading) {\n    return (\n      <div className=\"loading-container\">\n        <Spin size=\"large\" />\n      </div>\n    );\n  }\n\n  // 按类型分组预测数据\n  const groupedPredictions = predictions.reduce((groups: any, prediction: any) => {\n    const type = prediction.prediction_type;\n    if (!groups[type]) {\n      groups[type] = [];\n    }\n    groups[type].push(prediction);\n    return groups;\n  }, {});\n\n  return (\n    <div>\n      <Title level={2}>\n        <ThunderboltOutlined /> 预测中心\n      </Title>\n      <Text type=\"secondary\">\n        第 {currentPeriod?.period_number || '---'} 期预测数据\n      </Text>\n      \n      <Row gutter={[16, 16]} style={{ marginTop: 24 }}>\n        {Object.entries(groupedPredictions).map(([type, typePredictions]: [string, any]) => (\n          <Col xs={24} sm={12} lg={8} key={type}>\n            <PredictionCard\n              title={\n                <div className=\"prediction-title\">\n                  <TrophyOutlined /> {getPredictionTitle(type)}\n                </div>\n              }\n              extra={\n                <Tag color={getAccuracyColor(typePredictions[0]?.accuracy_rate || 0)}>\n                  {(typePredictions[0]?.accuracy_rate || 0).toFixed(1)}%\n                </Tag>\n              }\n            >\n              {renderPredictionContent(typePredictions[0])}\n              <div className=\"accuracy-rate\">\n                <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                  准确率: {(typePredictions[0]?.accuracy_rate || 0).toFixed(1)}%\n                </Text>\n              </div>\n            </PredictionCard>\n          </Col>\n        ))}\n      </Row>\n      \n      {predictions.length === 0 && (\n        <div className=\"empty-container\">\n          <FireOutlined style={{ fontSize: 48, color: '#ccc', marginBottom: 16 }} />\n          <div>暂无预测数据</div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default PredictionPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,UAAU,EAAEC,IAAI,EAAEC,OAAO,EAAEC,IAAI,EAAEC,GAAG,QAAQ,MAAM;AAC3E,SAASC,mBAAmB,EAAEC,cAAc,EAAEC,YAAY,QAAQ,mBAAmB;AACrF,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,UAAU,QAAQ,wBAAwB;AACnD,SAASC,aAAa,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGd,UAAU;AAClC,MAAM;EAAEe;AAAQ,CAAC,GAAGZ,IAAI;;AAExB;AACA,MAAMa,UAAU,GAAGR,MAAM,CAACT,IAAI,CAAC;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMkB,cAAc,GAAGT,MAAM,CAACQ,UAAU,CAAC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACE,EAAA,GAjBID,cAAc;AAmBpB,MAAME,cAAc,GAAGX,MAAM,CAACY,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GApBIF,cAAc;AAsBpB,MAAMG,QAAQ,GAAGd,MAAM,CAACY,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAdID,QAAQ;AAgBd,MAAME,UAAU,GAAGhB,MAAM,CAACY,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACK,GAAA,GAbID,UAAU;AAehB,MAAME,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmC,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAQ,EAAE,CAAC;EACzD,MAAM,CAACqC,aAAa,EAAEC,gBAAgB,CAAC,GAAGtC,QAAQ,CAAM,IAAI,CAAC;EAE7DC,SAAS,CAAC,MAAM;IACdsC,eAAe,CAAC,CAAC;IACjBC,oBAAoB,CAAC,CAAC;IAEtB,OAAO,MAAM;MACXzB,aAAa,CAAC0B,GAAG,CAAC,mBAAmB,CAAC;MACtC1B,aAAa,CAAC0B,GAAG,CAAC,gBAAgB,CAAC;IACrC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMF,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAM,CAACG,eAAe,EAAEC,WAAW,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACvD/B,UAAU,CAACgC,qBAAqB,CAAC,CAAC,EAClChC,UAAU,CAACiC,gBAAgB,CAAC,CAAC,CAC9B,CAAC;MAEFX,cAAc,CAACM,eAAe,CAAC;MAC/BJ,gBAAgB,CAACK,WAAW,CAACN,aAAa,CAAC;MAC3CH,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,OAAOc,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCzC,OAAO,CAACyC,KAAK,CAAC,UAAU,CAAC;MACzBd,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMM,oBAAoB,GAAGA,CAAA,KAAM;IACjCzB,aAAa,CAACmC,kBAAkB,CAAEC,UAAe,IAAK;MACpD5C,OAAO,CAAC6C,IAAI,CAAC,SAAS,CAAC;MACvBb,eAAe,CAAC,CAAC;IACnB,CAAC,CAAC;IAEFxB,aAAa,CAACsC,eAAe,CAAEC,IAAS,IAAK;MAC3ChB,gBAAgB,CAACgB,IAAI,CAACjB,aAAa,CAAC;IACtC,CAAC,CAAC;EACJ,CAAC;EAED,MAAMkB,uBAAuB,GAAIJ,UAAe,IAAK;IAAA,IAAAK,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;IACnD,MAAM;MAAEC,eAAe;MAAEC;IAAgB,CAAC,GAAGV,UAAU;IAEvD,QAAQS,eAAe;MACrB,KAAK,qBAAqB;QACxB,oBACE3C,OAAA,CAACO,cAAc;UAAAsC,QAAA,gBACb7C,OAAA;YAAK8C,SAAS,EAAC,WAAW;YAAAD,QAAA,gBACxB7C,OAAA;cAAM8C,SAAS,EAAC,YAAY;cAAAD,QAAA,EAAC;YAAG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvClD,OAAA;cAAM8C,SAAS,EAAC,YAAY;cAAAD,QAAA,EAAED,eAAe,CAACO;YAAa;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC,eACNlD,OAAA;YAAK8C,SAAS,EAAC,WAAW;YAAAD,QAAA,gBACxB7C,OAAA;cAAM8C,SAAS,EAAC,YAAY;cAAAD,QAAA,EAAC;YAAG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvClD,OAAA;cAAM8C,SAAS,EAAC,YAAY;cAAAD,QAAA,EAAED,eAAe,CAACQ;YAAY;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC,eACNlD,OAAA;YAAK8C,SAAS,EAAC,WAAW;YAAAD,QAAA,gBACxB7C,OAAA;cAAM8C,SAAS,EAAC,YAAY;cAAAD,QAAA,EAAC;YAAG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvClD,OAAA;cAAM8C,SAAS,EAAC,YAAY;cAAAD,QAAA,EAAED,eAAe,CAACS;YAAa;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC,eACNlD,OAAA;YAAK8C,SAAS,EAAC,WAAW;YAAAD,QAAA,gBACxB7C,OAAA;cAAM8C,SAAS,EAAC,YAAY;cAAAD,QAAA,EAAC;YAAG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvClD,OAAA;cAAM8C,SAAS,EAAC,YAAY;cAAAD,QAAA,EAAED,eAAe,CAACU;YAAU;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC,eACNlD,OAAA;YAAK8C,SAAS,EAAC,WAAW;YAAAD,QAAA,gBACxB7C,OAAA;cAAM8C,SAAS,EAAC,YAAY;cAAAD,QAAA,EAAC;YAAG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvClD,OAAA,CAACU,QAAQ;cAAAmC,QAAA,GAAAN,qBAAA,GACNK,eAAe,CAACW,WAAW,cAAAhB,qBAAA,uBAA3BA,qBAAA,CAA6BiB,GAAG,CAAC,CAACC,IAAY,EAAEC,KAAa,kBAC5D1D,OAAA;gBAAkB8C,SAAS,EAAC,WAAW;gBAAAD,QAAA,EAAEY;cAAI,GAAlCC,KAAK;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAoC,CACrD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC;MAGrB,KAAK,gBAAgB;QACnB,oBACElD,OAAA,CAACO,cAAc;UAAAsC,QAAA,gBACb7C,OAAA;YAAK8C,SAAS,EAAC,WAAW;YAAAD,QAAA,gBACxB7C,OAAA;cAAM8C,SAAS,EAAC,YAAY;cAAAD,QAAA,EAAC;YAAI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxClD,OAAA,CAACY,UAAU;cAAAiC,QAAA,GAAAL,qBAAA,GACRI,eAAe,CAACe,YAAY,cAAAnB,qBAAA,uBAA5BA,qBAAA,CAA8BgB,GAAG,CAAC,CAACI,MAAc,EAAEF,KAAa,kBAC/D1D,OAAA;gBAAkB8C,SAAS,EAAC,aAAa;gBAAAD,QAAA,EAAEe;cAAM,GAAtCF,KAAK;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAwC,CACzD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNlD,OAAA;YAAK8C,SAAS,EAAC,WAAW;YAAAD,QAAA,eACxB7C,OAAA;cAAM8C,SAAS,EAAC,YAAY;cAAAD,QAAA,EAAED,eAAe,CAACiB;YAAW;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC;MAGrB,KAAK,mBAAmB;QACtB,oBACElD,OAAA,CAACO,cAAc;UAAAsC,QAAA,eACb7C,OAAA;YAAK8C,SAAS,EAAC,WAAW;YAAAD,QAAA,gBACxB7C,OAAA;cAAM8C,SAAS,EAAC,YAAY;cAAAD,QAAA,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1ClD,OAAA,CAACU,QAAQ;cAAAmC,QAAA,GAAAJ,qBAAA,GACNG,eAAe,CAACkB,KAAK,cAAArB,qBAAA,uBAArBA,qBAAA,CAAuBe,GAAG,CAAC,CAACC,IAAY,EAAEC,KAAa,kBACtD1D,OAAA;gBAAkB8C,SAAS,EAAC,WAAW;gBAAAD,QAAA,EAAEY;cAAI,GAAlCC,KAAK;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAoC,CACrD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC;MAGrB,KAAK,eAAe;QAClB,oBACElD,OAAA,CAACO,cAAc;UAAAsC,QAAA,eACb7C,OAAA;YAAK8C,SAAS,EAAC,WAAW;YAAAD,QAAA,gBACxB7C,OAAA;cAAM8C,SAAS,EAAC,YAAY;cAAAD,QAAA,EAAC;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzClD,OAAA;cAAM8C,SAAS,EAAC,YAAY;cAAAD,QAAA,GAAAH,qBAAA,GAAEE,eAAe,CAACmB,MAAM,cAAArB,qBAAA,uBAAtBA,qBAAA,CAAwBsB,IAAI,CAAC,GAAG;YAAC;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC;MAGrB,KAAK,eAAe;QAClB,oBACElD,OAAA,CAACO,cAAc;UAAAsC,QAAA,eACb7C,OAAA;YAAK8C,SAAS,EAAC,WAAW;YAAAD,QAAA,gBACxB7C,OAAA;cAAM8C,SAAS,EAAC,YAAY;cAAAD,QAAA,EAAC;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzClD,OAAA;cAAM8C,SAAS,EAAC,YAAY;cAAAD,QAAA,EAAED,eAAe,CAACqB;YAAI;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC;MAGrB;QACE,oBACElD,OAAA,CAACO,cAAc;UAAAsC,QAAA,eACb7C,OAAA;YAAK8C,SAAS,EAAC,WAAW;YAAAD,QAAA,eACxB7C,OAAA;cAAM8C,SAAS,EAAC,YAAY;cAAAD,QAAA,EACzBqB,IAAI,CAACC,SAAS,CAACvB,eAAe,EAAE,IAAI,EAAE,CAAC;YAAC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC;IAEvB;EACF,CAAC;EAED,MAAMkB,kBAAkB,GAAIH,IAAY,IAAK;IAC3C,MAAMI,MAAiC,GAAG;MACxC,qBAAqB,EAAE,MAAM;MAC7B,gBAAgB,EAAE,OAAO;MACzB,mBAAmB,EAAE,OAAO;MAC5B,eAAe,EAAE,MAAM;MACvB,oBAAoB,EAAE,MAAM;MAC5B,eAAe,EAAE,MAAM;MACvB,0BAA0B,EAAE,OAAO;MACnC,oBAAoB,EAAE,MAAM;MAC5B,mBAAmB,EAAE,MAAM;MAC3B,mBAAmB,EAAE,MAAM;MAC3B,eAAe,EAAE,MAAM;MACvB,oBAAoB,EAAE,OAAO;MAC7B,uBAAuB,EAAE,MAAM;MAC/B,sBAAsB,EAAE,MAAM;MAC9B,eAAe,EAAE,KAAK;MACtB,qBAAqB,EAAE,MAAM;MAC7B,cAAc,EAAE,QAAQ;MACxB,qBAAqB,EAAE;IACzB,CAAC;IACD,OAAOA,MAAM,CAACJ,IAAI,CAAC,IAAIA,IAAI;EAC7B,CAAC;EAED,MAAMK,gBAAgB,GAAIC,IAAY,IAAK;IACzC,IAAIA,IAAI,IAAI,EAAE,EAAE,OAAO,SAAS;IAChC,IAAIA,IAAI,IAAI,EAAE,EAAE,OAAO,SAAS;IAChC,OAAO,OAAO;EAChB,CAAC;EAED,IAAIvD,OAAO,EAAE;IACX,oBACEhB,OAAA;MAAK8C,SAAS,EAAC,mBAAmB;MAAAD,QAAA,eAChC7C,OAAA,CAACX,IAAI;QAACmF,IAAI,EAAC;MAAO;QAAAzB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC;EAEV;;EAEA;EACA,MAAMuB,kBAAkB,GAAGvD,WAAW,CAACwD,MAAM,CAAC,CAACC,MAAW,EAAEzC,UAAe,KAAK;IAC9E,MAAM+B,IAAI,GAAG/B,UAAU,CAACS,eAAe;IACvC,IAAI,CAACgC,MAAM,CAACV,IAAI,CAAC,EAAE;MACjBU,MAAM,CAACV,IAAI,CAAC,GAAG,EAAE;IACnB;IACAU,MAAM,CAACV,IAAI,CAAC,CAACW,IAAI,CAAC1C,UAAU,CAAC;IAC7B,OAAOyC,MAAM;EACf,CAAC,EAAE,CAAC,CAAC,CAAC;EAEN,oBACE3E,OAAA;IAAA6C,QAAA,gBACE7C,OAAA,CAACC,KAAK;MAAC4E,KAAK,EAAE,CAAE;MAAAhC,QAAA,gBACd7C,OAAA,CAACP,mBAAmB;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,6BACzB;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eACRlD,OAAA,CAACE,IAAI;MAAC+D,IAAI,EAAC,WAAW;MAAApB,QAAA,GAAC,SACnB,EAAC,CAAAzB,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE0D,aAAa,KAAI,KAAK,EAAC,iCAC3C;IAAA;MAAA/B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAEPlD,OAAA,CAACf,GAAG;MAAC8F,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACC,KAAK,EAAE;QAAEC,SAAS,EAAE;MAAG,CAAE;MAAApC,QAAA,EAC7CqC,MAAM,CAACC,OAAO,CAACV,kBAAkB,CAAC,CAACjB,GAAG,CAAC,CAAC,CAACS,IAAI,EAAEmB,eAAe,CAAgB;QAAA,IAAAC,iBAAA,EAAAC,kBAAA,EAAAC,kBAAA;QAAA,oBAC7EvF,OAAA,CAACd,GAAG;UAACsG,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAA7C,QAAA,eACzB7C,OAAA,CAACK,cAAc;YACbsF,KAAK,eACH3F,OAAA;cAAK8C,SAAS,EAAC,kBAAkB;cAAAD,QAAA,gBAC/B7C,OAAA,CAACN,cAAc;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,KAAC,EAACkB,kBAAkB,CAACH,IAAI,CAAC;YAAA;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CACN;YACD0C,KAAK,eACH5F,OAAA,CAACR,GAAG;cAACqG,KAAK,EAAEvB,gBAAgB,CAAC,EAAAe,iBAAA,GAAAD,eAAe,CAAC,CAAC,CAAC,cAAAC,iBAAA,uBAAlBA,iBAAA,CAAoBS,aAAa,KAAI,CAAC,CAAE;cAAAjD,QAAA,GAClE,CAAC,EAAAyC,kBAAA,GAAAF,eAAe,CAAC,CAAC,CAAC,cAAAE,kBAAA,uBAAlBA,kBAAA,CAAoBQ,aAAa,KAAI,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,GACvD;YAAA;cAAAhD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN;YAAAL,QAAA,GAEAP,uBAAuB,CAAC8C,eAAe,CAAC,CAAC,CAAC,CAAC,eAC5CpF,OAAA;cAAK8C,SAAS,EAAC,eAAe;cAAAD,QAAA,eAC5B7C,OAAA,CAACE,IAAI;gBAAC+D,IAAI,EAAC,WAAW;gBAACe,KAAK,EAAE;kBAAEgB,QAAQ,EAAE;gBAAG,CAAE;gBAAAnD,QAAA,GAAC,sBACzC,EAAC,CAAC,EAAA0C,kBAAA,GAAAH,eAAe,CAAC,CAAC,CAAC,cAAAG,kBAAA,uBAAlBA,kBAAA,CAAoBO,aAAa,KAAI,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,GAC5D;cAAA;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ;QAAC,GAnBce,IAAI;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAoBhC,CAAC;MAAA,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAELhC,WAAW,CAAC+E,MAAM,KAAK,CAAC,iBACvBjG,OAAA;MAAK8C,SAAS,EAAC,iBAAiB;MAAAD,QAAA,gBAC9B7C,OAAA,CAACL,YAAY;QAACqF,KAAK,EAAE;UAAEgB,QAAQ,EAAE,EAAE;UAAEH,KAAK,EAAE,MAAM;UAAEK,YAAY,EAAE;QAAG;MAAE;QAAAnD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1ElD,OAAA;QAAA6C,QAAA,EAAK;MAAM;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACnC,EAAA,CAxOID,cAAwB;AAAAqF,GAAA,GAAxBrF,cAAwB;AA0O9B,eAAeA,cAAc;AAAC,IAAAR,EAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAsF,GAAA;AAAAC,YAAA,CAAA9F,EAAA;AAAA8F,YAAA,CAAA3F,GAAA;AAAA2F,YAAA,CAAAzF,GAAA;AAAAyF,YAAA,CAAAvF,GAAA;AAAAuF,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}