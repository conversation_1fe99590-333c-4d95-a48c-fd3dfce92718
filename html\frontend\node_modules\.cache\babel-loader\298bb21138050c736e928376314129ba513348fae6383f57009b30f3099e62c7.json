{"ast": null, "code": "'use strict';\n\nvar $String = String;\nmodule.exports = function (argument) {\n  try {\n    return $String(argument);\n  } catch (error) {\n    return 'Object';\n  }\n};", "map": {"version": 3, "names": ["$String", "String", "module", "exports", "argument", "error"], "sources": ["D:/liu/html/frontend/node_modules/core-js-pure/internals/try-to-string.js"], "sourcesContent": ["'use strict';\nvar $String = String;\n\nmodule.exports = function (argument) {\n  try {\n    return $String(argument);\n  } catch (error) {\n    return 'Object';\n  }\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,OAAO,GAAGC,MAAM;AAEpBC,MAAM,CAACC,OAAO,GAAG,UAAUC,QAAQ,EAAE;EACnC,IAAI;IACF,OAAOJ,OAAO,CAACI,QAAQ,CAAC;EAC1B,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,OAAO,QAAQ;EACjB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}