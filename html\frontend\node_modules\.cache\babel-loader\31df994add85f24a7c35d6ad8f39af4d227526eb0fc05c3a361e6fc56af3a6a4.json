{"ast": null, "code": "var _jsxFileName = \"D:\\\\liu\\\\html\\\\frontend\\\\src\\\\components\\\\Header.tsx\";\nimport React from 'react';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HeaderContainer = styled.header`\n  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);\n  padding: 10px 0;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n`;\n_c = HeaderContainer;\nconst TopNav = styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 16px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-size: 12px;\n  color: #fff;\n  \n  .nav-links {\n    display: flex;\n    gap: 20px;\n    \n    a {\n      color: #fff;\n      text-decoration: none;\n      \n      &:hover {\n        color: #ffd700;\n      }\n    }\n  }\n  \n  .return-home {\n    color: #ffd700;\n  }\n`;\n_c2 = TopNav;\nconst MainHeader = styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 20px 16px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n`;\n_c3 = MainHeader;\nconst Logo = styled.div`\n  display: flex;\n  align-items: center;\n\n  .logo-icon {\n    width: 60px;\n    height: 60px;\n    background: linear-gradient(135deg, #ffd700, #ffed4e);\n    border-radius: 50%;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    font-size: 24px;\n    font-weight: bold;\n    color: #1e3c72;\n    margin-right: 15px;\n    box-shadow: 0 4px 8px rgba(0,0,0,0.2);\n  }\n\n  .logo-text {\n    color: #ffd700;\n    font-size: 28px;\n    font-weight: bold;\n    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);\n  }\n`;\n_c4 = Logo;\nconst OnlineStatus = styled.div`\n  color: #fff;\n  font-size: 14px;\n  display: flex;\n  align-items: center;\n  \n  .status-dot {\n    width: 8px;\n    height: 8px;\n    border-radius: 50%;\n    background: #52c41a;\n    margin-right: 8px;\n    animation: pulse 2s infinite;\n  }\n  \n  @keyframes pulse {\n    0% { opacity: 1; }\n    50% { opacity: 0.5; }\n    100% { opacity: 1; }\n  }\n`;\n_c5 = OnlineStatus;\nconst Header = ({\n  onlineUsers\n}) => {\n  return /*#__PURE__*/_jsxDEV(HeaderContainer, {\n    children: [/*#__PURE__*/_jsxDEV(TopNav, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"nav-links\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"https://681680.com\",\n          children: \"\\u91D1\\u8D22\\u795E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"https://681680.com\",\n          children: \"\\u6FB3\\u95E849\\u7F51\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"https://681680.com\",\n          children: \"\\u8BF8\\u845B\\u4EAE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"https://681680.com\",\n          children: \"\\u94B1\\u591A\\u591A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"https://681680.com\",\n          children: \"\\u7BA1\\u5BB6\\u5A46\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"https://681680.com\",\n          children: \"\\u8D22\\u795E\\u7237\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"return-home\",\n        children: \"\\u8FD4\\u56DE\\u9996\\u9875\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MainHeader, {\n      children: [/*#__PURE__*/_jsxDEV(Logo, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"logo-icon\",\n          children: \"\\u6FB3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"logo-text\",\n          children: \"\\u6FB3\\u95E8\\u8461\\u4EAC\\u65B0\\u5F69\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(OnlineStatus, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"status-dot\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this), \"\\u5728\\u7EBF\\u7528\\u6237: \", onlineUsers]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 103,\n    columnNumber: 5\n  }, this);\n};\n_c6 = Header;\nexport default Header;\nvar _c, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"HeaderContainer\");\n$RefreshReg$(_c2, \"TopNav\");\n$RefreshReg$(_c3, \"MainHeader\");\n$RefreshReg$(_c4, \"Logo\");\n$RefreshReg$(_c5, \"OnlineStatus\");\n$RefreshReg$(_c6, \"Header\");", "map": {"version": 3, "names": ["React", "styled", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "header", "_c", "TopNav", "div", "_c2", "<PERSON><PERSON><PERSON><PERSON>", "_c3", "Logo", "_c4", "OnlineStatus", "_c5", "Header", "onlineUsers", "children", "className", "href", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c6", "$RefreshReg$"], "sources": ["D:/liu/html/frontend/src/components/Header.tsx"], "sourcesContent": ["import React from 'react';\nimport styled from 'styled-components';\n\ninterface HeaderProps {\n  onlineUsers: number;\n}\n\nconst HeaderContainer = styled.header`\n  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);\n  padding: 10px 0;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n`;\n\nconst TopNav = styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 16px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-size: 12px;\n  color: #fff;\n  \n  .nav-links {\n    display: flex;\n    gap: 20px;\n    \n    a {\n      color: #fff;\n      text-decoration: none;\n      \n      &:hover {\n        color: #ffd700;\n      }\n    }\n  }\n  \n  .return-home {\n    color: #ffd700;\n  }\n`;\n\nconst MainHeader = styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 20px 16px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n`;\n\nconst Logo = styled.div`\n  display: flex;\n  align-items: center;\n\n  .logo-icon {\n    width: 60px;\n    height: 60px;\n    background: linear-gradient(135deg, #ffd700, #ffed4e);\n    border-radius: 50%;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    font-size: 24px;\n    font-weight: bold;\n    color: #1e3c72;\n    margin-right: 15px;\n    box-shadow: 0 4px 8px rgba(0,0,0,0.2);\n  }\n\n  .logo-text {\n    color: #ffd700;\n    font-size: 28px;\n    font-weight: bold;\n    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);\n  }\n`;\n\nconst OnlineStatus = styled.div`\n  color: #fff;\n  font-size: 14px;\n  display: flex;\n  align-items: center;\n  \n  .status-dot {\n    width: 8px;\n    height: 8px;\n    border-radius: 50%;\n    background: #52c41a;\n    margin-right: 8px;\n    animation: pulse 2s infinite;\n  }\n  \n  @keyframes pulse {\n    0% { opacity: 1; }\n    50% { opacity: 0.5; }\n    100% { opacity: 1; }\n  }\n`;\n\nconst Header: React.FC<HeaderProps> = ({ onlineUsers }) => {\n  return (\n    <HeaderContainer>\n      <TopNav>\n        <div className=\"nav-links\">\n          <a href=\"https://681680.com\">金财神</a>\n          <a href=\"https://681680.com\">澳门49网</a>\n          <a href=\"https://681680.com\">诸葛亮</a>\n          <a href=\"https://681680.com\">钱多多</a>\n          <a href=\"https://681680.com\">管家婆</a>\n          <a href=\"https://681680.com\">财神爷</a>\n        </div>\n        <div className=\"return-home\">返回首页</div>\n      </TopNav>\n      \n      <MainHeader>\n        <Logo>\n          <div className=\"logo-icon\">澳</div>\n          <div className=\"logo-text\">澳门葡京新彩</div>\n        </Logo>\n        \n        <OnlineStatus>\n          <div className=\"status-dot\"></div>\n          在线用户: {onlineUsers}\n        </OnlineStatus>\n      </MainHeader>\n    </HeaderContainer>\n  );\n};\n\nexport default Header;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAMvC,MAAMC,eAAe,GAAGH,MAAM,CAACI,MAAM;AACrC;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAJIF,eAAe;AAMrB,MAAMG,MAAM,GAAGN,MAAM,CAACO,GAAG;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GA3BIF,MAAM;AA6BZ,MAAMG,UAAU,GAAGT,MAAM,CAACO,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAPID,UAAU;AAShB,MAAME,IAAI,GAAGX,MAAM,CAACO,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACK,GAAA,GAzBID,IAAI;AA2BV,MAAME,YAAY,GAAGb,MAAM,CAACO,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACO,GAAA,GApBID,YAAY;AAsBlB,MAAME,MAA6B,GAAGA,CAAC;EAAEC;AAAY,CAAC,KAAK;EACzD,oBACEd,OAAA,CAACC,eAAe;IAAAc,QAAA,gBACdf,OAAA,CAACI,MAAM;MAAAW,QAAA,gBACLf,OAAA;QAAKgB,SAAS,EAAC,WAAW;QAAAD,QAAA,gBACxBf,OAAA;UAAGiB,IAAI,EAAC,oBAAoB;UAAAF,QAAA,EAAC;QAAG;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACpCrB,OAAA;UAAGiB,IAAI,EAAC,oBAAoB;UAAAF,QAAA,EAAC;QAAK;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACtCrB,OAAA;UAAGiB,IAAI,EAAC,oBAAoB;UAAAF,QAAA,EAAC;QAAG;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACpCrB,OAAA;UAAGiB,IAAI,EAAC,oBAAoB;UAAAF,QAAA,EAAC;QAAG;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACpCrB,OAAA;UAAGiB,IAAI,EAAC,oBAAoB;UAAAF,QAAA,EAAC;QAAG;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACpCrB,OAAA;UAAGiB,IAAI,EAAC,oBAAoB;UAAAF,QAAA,EAAC;QAAG;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,eACNrB,OAAA;QAAKgB,SAAS,EAAC,aAAa;QAAAD,QAAA,EAAC;MAAI;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC,CAAC,eAETrB,OAAA,CAACO,UAAU;MAAAQ,QAAA,gBACTf,OAAA,CAACS,IAAI;QAAAM,QAAA,gBACHf,OAAA;UAAKgB,SAAS,EAAC,WAAW;UAAAD,QAAA,EAAC;QAAC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAClCrB,OAAA;UAAKgB,SAAS,EAAC,WAAW;UAAAD,QAAA,EAAC;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC,eAEPrB,OAAA,CAACW,YAAY;QAAAI,QAAA,gBACXf,OAAA;UAAKgB,SAAS,EAAC;QAAY;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,8BAC5B,EAACP,WAAW;MAAA;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEtB,CAAC;AAACC,GAAA,GA5BIT,MAA6B;AA8BnC,eAAeA,MAAM;AAAC,IAAAV,EAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAU,GAAA;AAAAC,YAAA,CAAApB,EAAA;AAAAoB,YAAA,CAAAjB,GAAA;AAAAiB,YAAA,CAAAf,GAAA;AAAAe,YAAA,CAAAb,GAAA;AAAAa,YAAA,CAAAX,GAAA;AAAAW,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}