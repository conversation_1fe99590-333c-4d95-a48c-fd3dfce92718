{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { useEffect, useRef } from 'react';\nimport CacheMap from \"../utils/CacheMap\";\nfunction parseNumber(value) {\n  var num = parseFloat(value);\n  return isNaN(num) ? 0 : num;\n}\nexport default function useHeights(getKey, onItemAdd, onItemRemove) {\n  var _React$useState = React.useState(0),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    updatedMark = _React$useState2[0],\n    setUpdatedMark = _React$useState2[1];\n  var instanceRef = useRef(new Map());\n  var heightsRef = useRef(new CacheMap());\n  var promiseIdRef = useRef(0);\n  function cancelRaf() {\n    promiseIdRef.current += 1;\n  }\n  function collectHeight() {\n    var sync = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    cancelRaf();\n    var doCollect = function doCollect() {\n      var changed = false;\n      instanceRef.current.forEach(function (element, key) {\n        if (element && element.offsetParent) {\n          var offsetHeight = element.offsetHeight;\n          var _getComputedStyle = getComputedStyle(element),\n            marginTop = _getComputedStyle.marginTop,\n            marginBottom = _getComputedStyle.marginBottom;\n          var marginTopNum = parseNumber(marginTop);\n          var marginBottomNum = parseNumber(marginBottom);\n          var totalHeight = offsetHeight + marginTopNum + marginBottomNum;\n          if (heightsRef.current.get(key) !== totalHeight) {\n            heightsRef.current.set(key, totalHeight);\n            changed = true;\n          }\n        }\n      });\n\n      // Always trigger update mark to tell parent that should re-calculate heights when resized\n      if (changed) {\n        setUpdatedMark(function (c) {\n          return c + 1;\n        });\n      }\n    };\n    if (sync) {\n      doCollect();\n    } else {\n      promiseIdRef.current += 1;\n      var id = promiseIdRef.current;\n      Promise.resolve().then(function () {\n        if (id === promiseIdRef.current) {\n          doCollect();\n        }\n      });\n    }\n  }\n  function setInstanceRef(item, instance) {\n    var key = getKey(item);\n    var origin = instanceRef.current.get(key);\n    if (instance) {\n      instanceRef.current.set(key, instance);\n      collectHeight();\n    } else {\n      instanceRef.current.delete(key);\n    }\n\n    // Instance changed\n    if (!origin !== !instance) {\n      if (instance) {\n        onItemAdd === null || onItemAdd === void 0 || onItemAdd(item);\n      } else {\n        onItemRemove === null || onItemRemove === void 0 || onItemRemove(item);\n      }\n    }\n  }\n  useEffect(function () {\n    return cancelRaf;\n  }, []);\n  return [setInstanceRef, collectHeight, heightsRef.current, updatedMark];\n}", "map": {"version": 3, "names": ["_slicedToArray", "React", "useEffect", "useRef", "CacheMap", "parseNumber", "value", "num", "parseFloat", "isNaN", "useHeights", "<PERSON><PERSON><PERSON>", "onItemAdd", "onItemRemove", "_React$useState", "useState", "_React$useState2", "updatedMark", "setUpdatedMark", "instanceRef", "Map", "heightsRef", "promiseIdRef", "cancelRaf", "current", "collectHeight", "sync", "arguments", "length", "undefined", "doCollect", "changed", "for<PERSON>ach", "element", "key", "offsetParent", "offsetHeight", "_getComputedStyle", "getComputedStyle", "marginTop", "marginBottom", "marginTopNum", "marginBottomNum", "totalHeight", "get", "set", "c", "id", "Promise", "resolve", "then", "setInstanceRef", "item", "instance", "origin", "delete"], "sources": ["D:/liu/html/frontend/node_modules/rc-virtual-list/es/hooks/useHeights.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { useEffect, useRef } from 'react';\nimport CacheMap from \"../utils/CacheMap\";\nfunction parseNumber(value) {\n  var num = parseFloat(value);\n  return isNaN(num) ? 0 : num;\n}\nexport default function useHeights(getKey, onItemAdd, onItemRemove) {\n  var _React$useState = React.useState(0),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    updatedMark = _React$useState2[0],\n    setUpdatedMark = _React$useState2[1];\n  var instanceRef = useRef(new Map());\n  var heightsRef = useRef(new CacheMap());\n  var promiseIdRef = useRef(0);\n  function cancelRaf() {\n    promiseIdRef.current += 1;\n  }\n  function collectHeight() {\n    var sync = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    cancelRaf();\n    var doCollect = function doCollect() {\n      var changed = false;\n      instanceRef.current.forEach(function (element, key) {\n        if (element && element.offsetParent) {\n          var offsetHeight = element.offsetHeight;\n          var _getComputedStyle = getComputedStyle(element),\n            marginTop = _getComputedStyle.marginTop,\n            marginBottom = _getComputedStyle.marginBottom;\n          var marginTopNum = parseNumber(marginTop);\n          var marginBottomNum = parseNumber(marginBottom);\n          var totalHeight = offsetHeight + marginTopNum + marginBottomNum;\n          if (heightsRef.current.get(key) !== totalHeight) {\n            heightsRef.current.set(key, totalHeight);\n            changed = true;\n          }\n        }\n      });\n\n      // Always trigger update mark to tell parent that should re-calculate heights when resized\n      if (changed) {\n        setUpdatedMark(function (c) {\n          return c + 1;\n        });\n      }\n    };\n    if (sync) {\n      doCollect();\n    } else {\n      promiseIdRef.current += 1;\n      var id = promiseIdRef.current;\n      Promise.resolve().then(function () {\n        if (id === promiseIdRef.current) {\n          doCollect();\n        }\n      });\n    }\n  }\n  function setInstanceRef(item, instance) {\n    var key = getKey(item);\n    var origin = instanceRef.current.get(key);\n    if (instance) {\n      instanceRef.current.set(key, instance);\n      collectHeight();\n    } else {\n      instanceRef.current.delete(key);\n    }\n\n    // Instance changed\n    if (!origin !== !instance) {\n      if (instance) {\n        onItemAdd === null || onItemAdd === void 0 || onItemAdd(item);\n      } else {\n        onItemRemove === null || onItemRemove === void 0 || onItemRemove(item);\n      }\n    }\n  }\n  useEffect(function () {\n    return cancelRaf;\n  }, []);\n  return [setInstanceRef, collectHeight, heightsRef.current, updatedMark];\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACzC,OAAOC,QAAQ,MAAM,mBAAmB;AACxC,SAASC,WAAWA,CAACC,KAAK,EAAE;EAC1B,IAAIC,GAAG,GAAGC,UAAU,CAACF,KAAK,CAAC;EAC3B,OAAOG,KAAK,CAACF,GAAG,CAAC,GAAG,CAAC,GAAGA,GAAG;AAC7B;AACA,eAAe,SAASG,UAAUA,CAACC,MAAM,EAAEC,SAAS,EAAEC,YAAY,EAAE;EAClE,IAAIC,eAAe,GAAGb,KAAK,CAACc,QAAQ,CAAC,CAAC,CAAC;IACrCC,gBAAgB,GAAGhB,cAAc,CAACc,eAAe,EAAE,CAAC,CAAC;IACrDG,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACjCE,cAAc,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACtC,IAAIG,WAAW,GAAGhB,MAAM,CAAC,IAAIiB,GAAG,CAAC,CAAC,CAAC;EACnC,IAAIC,UAAU,GAAGlB,MAAM,CAAC,IAAIC,QAAQ,CAAC,CAAC,CAAC;EACvC,IAAIkB,YAAY,GAAGnB,MAAM,CAAC,CAAC,CAAC;EAC5B,SAASoB,SAASA,CAAA,EAAG;IACnBD,YAAY,CAACE,OAAO,IAAI,CAAC;EAC3B;EACA,SAASC,aAAaA,CAAA,EAAG;IACvB,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;IACpFJ,SAAS,CAAC,CAAC;IACX,IAAIO,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG;MACnC,IAAIC,OAAO,GAAG,KAAK;MACnBZ,WAAW,CAACK,OAAO,CAACQ,OAAO,CAAC,UAAUC,OAAO,EAAEC,GAAG,EAAE;QAClD,IAAID,OAAO,IAAIA,OAAO,CAACE,YAAY,EAAE;UACnC,IAAIC,YAAY,GAAGH,OAAO,CAACG,YAAY;UACvC,IAAIC,iBAAiB,GAAGC,gBAAgB,CAACL,OAAO,CAAC;YAC/CM,SAAS,GAAGF,iBAAiB,CAACE,SAAS;YACvCC,YAAY,GAAGH,iBAAiB,CAACG,YAAY;UAC/C,IAAIC,YAAY,GAAGpC,WAAW,CAACkC,SAAS,CAAC;UACzC,IAAIG,eAAe,GAAGrC,WAAW,CAACmC,YAAY,CAAC;UAC/C,IAAIG,WAAW,GAAGP,YAAY,GAAGK,YAAY,GAAGC,eAAe;UAC/D,IAAIrB,UAAU,CAACG,OAAO,CAACoB,GAAG,CAACV,GAAG,CAAC,KAAKS,WAAW,EAAE;YAC/CtB,UAAU,CAACG,OAAO,CAACqB,GAAG,CAACX,GAAG,EAAES,WAAW,CAAC;YACxCZ,OAAO,GAAG,IAAI;UAChB;QACF;MACF,CAAC,CAAC;;MAEF;MACA,IAAIA,OAAO,EAAE;QACXb,cAAc,CAAC,UAAU4B,CAAC,EAAE;UAC1B,OAAOA,CAAC,GAAG,CAAC;QACd,CAAC,CAAC;MACJ;IACF,CAAC;IACD,IAAIpB,IAAI,EAAE;MACRI,SAAS,CAAC,CAAC;IACb,CAAC,MAAM;MACLR,YAAY,CAACE,OAAO,IAAI,CAAC;MACzB,IAAIuB,EAAE,GAAGzB,YAAY,CAACE,OAAO;MAC7BwB,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,YAAY;QACjC,IAAIH,EAAE,KAAKzB,YAAY,CAACE,OAAO,EAAE;UAC/BM,SAAS,CAAC,CAAC;QACb;MACF,CAAC,CAAC;IACJ;EACF;EACA,SAASqB,cAAcA,CAACC,IAAI,EAAEC,QAAQ,EAAE;IACtC,IAAInB,GAAG,GAAGvB,MAAM,CAACyC,IAAI,CAAC;IACtB,IAAIE,MAAM,GAAGnC,WAAW,CAACK,OAAO,CAACoB,GAAG,CAACV,GAAG,CAAC;IACzC,IAAImB,QAAQ,EAAE;MACZlC,WAAW,CAACK,OAAO,CAACqB,GAAG,CAACX,GAAG,EAAEmB,QAAQ,CAAC;MACtC5B,aAAa,CAAC,CAAC;IACjB,CAAC,MAAM;MACLN,WAAW,CAACK,OAAO,CAAC+B,MAAM,CAACrB,GAAG,CAAC;IACjC;;IAEA;IACA,IAAI,CAACoB,MAAM,KAAK,CAACD,QAAQ,EAAE;MACzB,IAAIA,QAAQ,EAAE;QACZzC,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,IAAIA,SAAS,CAACwC,IAAI,CAAC;MAC/D,CAAC,MAAM;QACLvC,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,IAAIA,YAAY,CAACuC,IAAI,CAAC;MACxE;IACF;EACF;EACAlD,SAAS,CAAC,YAAY;IACpB,OAAOqB,SAAS;EAClB,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,CAAC4B,cAAc,EAAE1B,aAAa,EAAEJ,UAAU,CAACG,OAAO,EAAEP,WAAW,CAAC;AACzE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}