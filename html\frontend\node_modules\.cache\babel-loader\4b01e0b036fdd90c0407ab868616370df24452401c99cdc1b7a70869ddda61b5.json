{"ast": null, "code": "import { io } from 'socket.io-client';\nclass SocketService {\n  constructor() {\n    this.socket = null;\n    this.reconnectAttempts = 0;\n    this.maxReconnectAttempts = 5;\n    this.reconnectDelay = 1000;\n  }\n  connect() {\n    var _this$socket;\n    if ((_this$socket = this.socket) !== null && _this$socket !== void 0 && _this$socket.connected) {\n      return;\n    }\n    const serverUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001';\n    this.socket = io(serverUrl, {\n      transports: ['websocket', 'polling'],\n      timeout: 20000,\n      forceNew: true\n    });\n    this.setupEventListeners();\n  }\n  setupEventListeners() {\n    if (!this.socket) return;\n    this.socket.on('connect', () => {\n      var _this$socket2;\n      console.log('Socket连接成功');\n      this.reconnectAttempts = 0;\n\n      // 请求当前期数信息\n      (_this$socket2 = this.socket) === null || _this$socket2 === void 0 ? void 0 : _this$socket2.emit('request_current_period');\n    });\n    this.socket.on('disconnect', reason => {\n      console.log('Socket连接断开:', reason);\n      if (reason === 'io server disconnect') {\n        // 服务器主动断开，需要重新连接\n        this.reconnect();\n      }\n    });\n    this.socket.on('connect_error', error => {\n      console.error('Socket连接错误:', error);\n      this.reconnect();\n    });\n    this.socket.on('reconnect', attemptNumber => {\n      console.log('Socket重连成功，尝试次数:', attemptNumber);\n    });\n    this.socket.on('reconnect_error', error => {\n      console.error('Socket重连失败:', error);\n    });\n    this.socket.on('reconnect_failed', () => {\n      console.error('Socket重连失败，已达到最大尝试次数');\n    });\n  }\n  reconnect() {\n    if (this.reconnectAttempts >= this.maxReconnectAttempts) {\n      console.error('已达到最大重连次数');\n      return;\n    }\n    this.reconnectAttempts++;\n    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);\n    console.log(`${delay}ms后尝试第${this.reconnectAttempts}次重连`);\n    setTimeout(() => {\n      this.connect();\n    }, delay);\n  }\n  disconnect() {\n    if (this.socket) {\n      this.socket.disconnect();\n      this.socket = null;\n    }\n  }\n\n  // 监听事件\n  on(event, callback) {\n    var _this$socket3;\n    (_this$socket3 = this.socket) === null || _this$socket3 === void 0 ? void 0 : _this$socket3.on(event, callback);\n  }\n\n  // 移除事件监听\n  off(event, callback) {\n    var _this$socket4;\n    (_this$socket4 = this.socket) === null || _this$socket4 === void 0 ? void 0 : _this$socket4.off(event, callback);\n  }\n\n  // 发送事件\n  emit(event, data) {\n    var _this$socket5;\n    (_this$socket5 = this.socket) === null || _this$socket5 === void 0 ? void 0 : _this$socket5.emit(event, data);\n  }\n\n  // 获取连接状态\n  get connected() {\n    var _this$socket6;\n    return ((_this$socket6 = this.socket) === null || _this$socket6 === void 0 ? void 0 : _this$socket6.connected) || false;\n  }\n\n  // 请求当前期数信息\n  requestCurrentPeriod() {\n    this.emit('request_current_period');\n  }\n\n  // 请求历史开奖结果\n  requestHistoryResults(limit = 10) {\n    this.emit('request_history_results', {\n      limit\n    });\n  }\n\n  // 监听当前期数更新\n  onCurrentPeriod(callback) {\n    this.on('current_period', callback);\n  }\n\n  // 监听倒计时更新\n  onCountdownUpdate(callback) {\n    this.on('countdown_update', callback);\n  }\n\n  // 监听新开奖结果\n  onNewResult(callback) {\n    this.on('new_result', callback);\n  }\n\n  // 监听历史结果\n  onHistoryResults(callback) {\n    this.on('history_results', callback);\n  }\n\n  // 监听预测数据更新\n  onPredictionUpdate(callback) {\n    this.on('prediction_update', callback);\n  }\n\n  // 监听系统消息\n  onSystemMessage(callback) {\n    this.on('system_message', callback);\n  }\n\n  // 监听在线用户数更新\n  onOnlineUsersUpdate(callback) {\n    this.on('online_users_update', callback);\n  }\n\n  // 监听错误\n  onError(callback) {\n    this.on('error', callback);\n  }\n}\n\n// 导出单例\nexport const socketService = new SocketService();", "map": {"version": 3, "names": ["io", "SocketService", "constructor", "socket", "reconnectAttempts", "maxReconnectAttempts", "reconnectDelay", "connect", "_this$socket", "connected", "serverUrl", "process", "env", "REACT_APP_API_URL", "transports", "timeout", "forceNew", "setupEventListeners", "on", "_this$socket2", "console", "log", "emit", "reason", "reconnect", "error", "attemptNumber", "delay", "Math", "pow", "setTimeout", "disconnect", "event", "callback", "_this$socket3", "off", "_this$socket4", "data", "_this$socket5", "_this$socket6", "requestCurrentPeriod", "requestHistoryResults", "limit", "onCurrentPeriod", "onCountdownUpdate", "onNewResult", "onHistoryResults", "onPredictionUpdate", "onSystemMessage", "onOnlineUsersUpdate", "onError", "socketService"], "sources": ["D:/liu/html/frontend/src/services/socketService.ts"], "sourcesContent": ["import { io, Socket } from 'socket.io-client';\n\nclass SocketService {\n  private socket: Socket | null = null;\n  private reconnectAttempts = 0;\n  private maxReconnectAttempts = 5;\n  private reconnectDelay = 1000;\n\n  connect() {\n    if (this.socket?.connected) {\n      return;\n    }\n\n    const serverUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001';\n    \n    this.socket = io(serverUrl, {\n      transports: ['websocket', 'polling'],\n      timeout: 20000,\n      forceNew: true,\n    });\n\n    this.setupEventListeners();\n  }\n\n  private setupEventListeners() {\n    if (!this.socket) return;\n\n    this.socket.on('connect', () => {\n      console.log('Socket连接成功');\n      this.reconnectAttempts = 0;\n      \n      // 请求当前期数信息\n      this.socket?.emit('request_current_period');\n    });\n\n    this.socket.on('disconnect', (reason) => {\n      console.log('Socket连接断开:', reason);\n      \n      if (reason === 'io server disconnect') {\n        // 服务器主动断开，需要重新连接\n        this.reconnect();\n      }\n    });\n\n    this.socket.on('connect_error', (error) => {\n      console.error('Socket连接错误:', error);\n      this.reconnect();\n    });\n\n    this.socket.on('reconnect', (attemptNumber) => {\n      console.log('Socket重连成功，尝试次数:', attemptNumber);\n    });\n\n    this.socket.on('reconnect_error', (error) => {\n      console.error('Socket重连失败:', error);\n    });\n\n    this.socket.on('reconnect_failed', () => {\n      console.error('Socket重连失败，已达到最大尝试次数');\n    });\n  }\n\n  private reconnect() {\n    if (this.reconnectAttempts >= this.maxReconnectAttempts) {\n      console.error('已达到最大重连次数');\n      return;\n    }\n\n    this.reconnectAttempts++;\n    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);\n    \n    console.log(`${delay}ms后尝试第${this.reconnectAttempts}次重连`);\n    \n    setTimeout(() => {\n      this.connect();\n    }, delay);\n  }\n\n  disconnect() {\n    if (this.socket) {\n      this.socket.disconnect();\n      this.socket = null;\n    }\n  }\n\n  // 监听事件\n  on(event: string, callback: (...args: any[]) => void) {\n    this.socket?.on(event, callback);\n  }\n\n  // 移除事件监听\n  off(event: string, callback?: (...args: any[]) => void) {\n    this.socket?.off(event, callback);\n  }\n\n  // 发送事件\n  emit(event: string, data?: any) {\n    this.socket?.emit(event, data);\n  }\n\n  // 获取连接状态\n  get connected() {\n    return this.socket?.connected || false;\n  }\n\n  // 请求当前期数信息\n  requestCurrentPeriod() {\n    this.emit('request_current_period');\n  }\n\n  // 请求历史开奖结果\n  requestHistoryResults(limit = 10) {\n    this.emit('request_history_results', { limit });\n  }\n\n  // 监听当前期数更新\n  onCurrentPeriod(callback: (data: any) => void) {\n    this.on('current_period', callback);\n  }\n\n  // 监听倒计时更新\n  onCountdownUpdate(callback: (data: any) => void) {\n    this.on('countdown_update', callback);\n  }\n\n  // 监听新开奖结果\n  onNewResult(callback: (data: any) => void) {\n    this.on('new_result', callback);\n  }\n\n  // 监听历史结果\n  onHistoryResults(callback: (data: any) => void) {\n    this.on('history_results', callback);\n  }\n\n  // 监听预测数据更新\n  onPredictionUpdate(callback: (data: any) => void) {\n    this.on('prediction_update', callback);\n  }\n\n  // 监听系统消息\n  onSystemMessage(callback: (data: any) => void) {\n    this.on('system_message', callback);\n  }\n\n  // 监听在线用户数更新\n  onOnlineUsersUpdate(callback: (count: number) => void) {\n    this.on('online_users_update', callback);\n  }\n\n  // 监听错误\n  onError(callback: (error: any) => void) {\n    this.on('error', callback);\n  }\n}\n\n// 导出单例\nexport const socketService = new SocketService();\n"], "mappings": "AAAA,SAASA,EAAE,QAAgB,kBAAkB;AAE7C,MAAMC,aAAa,CAAC;EAAAC,YAAA;IAAA,KACVC,MAAM,GAAkB,IAAI;IAAA,KAC5BC,iBAAiB,GAAG,CAAC;IAAA,KACrBC,oBAAoB,GAAG,CAAC;IAAA,KACxBC,cAAc,GAAG,IAAI;EAAA;EAE7BC,OAAOA,CAAA,EAAG;IAAA,IAAAC,YAAA;IACR,KAAAA,YAAA,GAAI,IAAI,CAACL,MAAM,cAAAK,YAAA,eAAXA,YAAA,CAAaC,SAAS,EAAE;MAC1B;IACF;IAEA,MAAMC,SAAS,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;IAE1E,IAAI,CAACV,MAAM,GAAGH,EAAE,CAACU,SAAS,EAAE;MAC1BI,UAAU,EAAE,CAAC,WAAW,EAAE,SAAS,CAAC;MACpCC,OAAO,EAAE,KAAK;MACdC,QAAQ,EAAE;IACZ,CAAC,CAAC;IAEF,IAAI,CAACC,mBAAmB,CAAC,CAAC;EAC5B;EAEQA,mBAAmBA,CAAA,EAAG;IAC5B,IAAI,CAAC,IAAI,CAACd,MAAM,EAAE;IAElB,IAAI,CAACA,MAAM,CAACe,EAAE,CAAC,SAAS,EAAE,MAAM;MAAA,IAAAC,aAAA;MAC9BC,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;MACzB,IAAI,CAACjB,iBAAiB,GAAG,CAAC;;MAE1B;MACA,CAAAe,aAAA,OAAI,CAAChB,MAAM,cAAAgB,aAAA,uBAAXA,aAAA,CAAaG,IAAI,CAAC,wBAAwB,CAAC;IAC7C,CAAC,CAAC;IAEF,IAAI,CAACnB,MAAM,CAACe,EAAE,CAAC,YAAY,EAAGK,MAAM,IAAK;MACvCH,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEE,MAAM,CAAC;MAElC,IAAIA,MAAM,KAAK,sBAAsB,EAAE;QACrC;QACA,IAAI,CAACC,SAAS,CAAC,CAAC;MAClB;IACF,CAAC,CAAC;IAEF,IAAI,CAACrB,MAAM,CAACe,EAAE,CAAC,eAAe,EAAGO,KAAK,IAAK;MACzCL,OAAO,CAACK,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnC,IAAI,CAACD,SAAS,CAAC,CAAC;IAClB,CAAC,CAAC;IAEF,IAAI,CAACrB,MAAM,CAACe,EAAE,CAAC,WAAW,EAAGQ,aAAa,IAAK;MAC7CN,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEK,aAAa,CAAC;IAChD,CAAC,CAAC;IAEF,IAAI,CAACvB,MAAM,CAACe,EAAE,CAAC,iBAAiB,EAAGO,KAAK,IAAK;MAC3CL,OAAO,CAACK,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IACrC,CAAC,CAAC;IAEF,IAAI,CAACtB,MAAM,CAACe,EAAE,CAAC,kBAAkB,EAAE,MAAM;MACvCE,OAAO,CAACK,KAAK,CAAC,sBAAsB,CAAC;IACvC,CAAC,CAAC;EACJ;EAEQD,SAASA,CAAA,EAAG;IAClB,IAAI,IAAI,CAACpB,iBAAiB,IAAI,IAAI,CAACC,oBAAoB,EAAE;MACvDe,OAAO,CAACK,KAAK,CAAC,WAAW,CAAC;MAC1B;IACF;IAEA,IAAI,CAACrB,iBAAiB,EAAE;IACxB,MAAMuB,KAAK,GAAG,IAAI,CAACrB,cAAc,GAAGsB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAACzB,iBAAiB,GAAG,CAAC,CAAC;IAE3EgB,OAAO,CAACC,GAAG,CAAC,GAAGM,KAAK,SAAS,IAAI,CAACvB,iBAAiB,KAAK,CAAC;IAEzD0B,UAAU,CAAC,MAAM;MACf,IAAI,CAACvB,OAAO,CAAC,CAAC;IAChB,CAAC,EAAEoB,KAAK,CAAC;EACX;EAEAI,UAAUA,CAAA,EAAG;IACX,IAAI,IAAI,CAAC5B,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAAC4B,UAAU,CAAC,CAAC;MACxB,IAAI,CAAC5B,MAAM,GAAG,IAAI;IACpB;EACF;;EAEA;EACAe,EAAEA,CAACc,KAAa,EAAEC,QAAkC,EAAE;IAAA,IAAAC,aAAA;IACpD,CAAAA,aAAA,OAAI,CAAC/B,MAAM,cAAA+B,aAAA,uBAAXA,aAAA,CAAahB,EAAE,CAACc,KAAK,EAAEC,QAAQ,CAAC;EAClC;;EAEA;EACAE,GAAGA,CAACH,KAAa,EAAEC,QAAmC,EAAE;IAAA,IAAAG,aAAA;IACtD,CAAAA,aAAA,OAAI,CAACjC,MAAM,cAAAiC,aAAA,uBAAXA,aAAA,CAAaD,GAAG,CAACH,KAAK,EAAEC,QAAQ,CAAC;EACnC;;EAEA;EACAX,IAAIA,CAACU,KAAa,EAAEK,IAAU,EAAE;IAAA,IAAAC,aAAA;IAC9B,CAAAA,aAAA,OAAI,CAACnC,MAAM,cAAAmC,aAAA,uBAAXA,aAAA,CAAahB,IAAI,CAACU,KAAK,EAAEK,IAAI,CAAC;EAChC;;EAEA;EACA,IAAI5B,SAASA,CAAA,EAAG;IAAA,IAAA8B,aAAA;IACd,OAAO,EAAAA,aAAA,OAAI,CAACpC,MAAM,cAAAoC,aAAA,uBAAXA,aAAA,CAAa9B,SAAS,KAAI,KAAK;EACxC;;EAEA;EACA+B,oBAAoBA,CAAA,EAAG;IACrB,IAAI,CAAClB,IAAI,CAAC,wBAAwB,CAAC;EACrC;;EAEA;EACAmB,qBAAqBA,CAACC,KAAK,GAAG,EAAE,EAAE;IAChC,IAAI,CAACpB,IAAI,CAAC,yBAAyB,EAAE;MAAEoB;IAAM,CAAC,CAAC;EACjD;;EAEA;EACAC,eAAeA,CAACV,QAA6B,EAAE;IAC7C,IAAI,CAACf,EAAE,CAAC,gBAAgB,EAAEe,QAAQ,CAAC;EACrC;;EAEA;EACAW,iBAAiBA,CAACX,QAA6B,EAAE;IAC/C,IAAI,CAACf,EAAE,CAAC,kBAAkB,EAAEe,QAAQ,CAAC;EACvC;;EAEA;EACAY,WAAWA,CAACZ,QAA6B,EAAE;IACzC,IAAI,CAACf,EAAE,CAAC,YAAY,EAAEe,QAAQ,CAAC;EACjC;;EAEA;EACAa,gBAAgBA,CAACb,QAA6B,EAAE;IAC9C,IAAI,CAACf,EAAE,CAAC,iBAAiB,EAAEe,QAAQ,CAAC;EACtC;;EAEA;EACAc,kBAAkBA,CAACd,QAA6B,EAAE;IAChD,IAAI,CAACf,EAAE,CAAC,mBAAmB,EAAEe,QAAQ,CAAC;EACxC;;EAEA;EACAe,eAAeA,CAACf,QAA6B,EAAE;IAC7C,IAAI,CAACf,EAAE,CAAC,gBAAgB,EAAEe,QAAQ,CAAC;EACrC;;EAEA;EACAgB,mBAAmBA,CAAChB,QAAiC,EAAE;IACrD,IAAI,CAACf,EAAE,CAAC,qBAAqB,EAAEe,QAAQ,CAAC;EAC1C;;EAEA;EACAiB,OAAOA,CAACjB,QAA8B,EAAE;IACtC,IAAI,CAACf,EAAE,CAAC,OAAO,EAAEe,QAAQ,CAAC;EAC5B;AACF;;AAEA;AACA,OAAO,MAAMkB,aAAa,GAAG,IAAIlD,aAAa,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}