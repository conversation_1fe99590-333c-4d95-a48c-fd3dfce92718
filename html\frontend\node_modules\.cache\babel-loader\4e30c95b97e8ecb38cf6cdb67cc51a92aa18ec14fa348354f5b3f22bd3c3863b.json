{"ast": null, "code": "var _jsxFileName = \"D:\\\\liu\\\\html\\\\frontend\\\\src\\\\components\\\\CountdownDisplay.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CountdownContainer = styled.div`\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);\n  color: white;\n  padding: 20px;\n  border-radius: 8px;\n  text-align: center;\n  margin: 20px 0;\n  box-shadow: 0 4px 12px rgba(0,0,0,0.15);\n`;\n_c = CountdownContainer;\nconst PeriodInfo = styled.div`\n  font-size: 24px;\n  font-weight: bold;\n  margin-bottom: 10px;\n`;\n_c2 = PeriodInfo;\nconst CountdownText = styled.div`\n  font-size: 18px;\n  margin-bottom: 15px;\n`;\n_c3 = CountdownText;\nconst TimeDisplay = styled.div`\n  font-size: 32px;\n  font-weight: bold;\n  font-family: 'Courier New', monospace;\n  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);\n  letter-spacing: 2px;\n`;\n_c4 = TimeDisplay;\nconst HistoryButton = styled.button`\n  background: rgba(255,255,255,0.2);\n  border: 2px solid rgba(255,255,255,0.5);\n  color: white;\n  padding: 8px 16px;\n  border-radius: 4px;\n  margin-top: 15px;\n  cursor: pointer;\n  font-size: 14px;\n  \n  &:hover {\n    background: rgba(255,255,255,0.3);\n  }\n`;\n_c5 = HistoryButton;\nconst CountdownDisplay = ({\n  currentPeriod\n}) => {\n  _s();\n  const [timeLeft, setTimeLeft] = useState('00:00:00');\n  useEffect(() => {\n    const timer = setInterval(() => {\n      if (currentPeriod !== null && currentPeriod !== void 0 && currentPeriod.drawTime) {\n        const now = new Date().getTime();\n        const drawTime = new Date(currentPeriod.drawTime).getTime();\n        const difference = drawTime - now;\n        if (difference > 0) {\n          const hours = Math.floor(difference % (1000 * 60 * 60 * 24) / (1000 * 60 * 60));\n          const minutes = Math.floor(difference % (1000 * 60 * 60) / (1000 * 60));\n          const seconds = Math.floor(difference % (1000 * 60) / 1000);\n          setTimeLeft(`${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`);\n        } else {\n          setTimeLeft('00:00:00');\n        }\n      }\n    }, 1000);\n    return () => clearInterval(timer);\n  }, [currentPeriod]);\n  return /*#__PURE__*/_jsxDEV(CountdownContainer, {\n    children: [/*#__PURE__*/_jsxDEV(PeriodInfo, {\n      children: [\"\\u6FB3\\u95E8\\u8461\\u4EAC\\u65B0\\u5F69\\u7B2C\", (currentPeriod === null || currentPeriod === void 0 ? void 0 : currentPeriod.period) || '---', \"\\u671F\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CountdownText, {\n      children: \"\\u5F00\\u5956\\u5012\\u8BA1\\u65F6:\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TimeDisplay, {\n      children: timeLeft\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(HistoryButton, {\n      children: \"\\u5386\\u53F2\\u8BB0\\u5F55\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 80,\n    columnNumber: 5\n  }, this);\n};\n_s(CountdownDisplay, \"1UOJJAbuALh+BXmSvv5S2OU2Hx4=\");\n_c6 = CountdownDisplay;\nexport default CountdownDisplay;\nvar _c, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"CountdownContainer\");\n$RefreshReg$(_c2, \"PeriodInfo\");\n$RefreshReg$(_c3, \"CountdownText\");\n$RefreshReg$(_c4, \"TimeDisplay\");\n$RefreshReg$(_c5, \"HistoryButton\");\n$RefreshReg$(_c6, \"CountdownDisplay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "styled", "jsxDEV", "_jsxDEV", "CountdownContainer", "div", "_c", "PeriodInfo", "_c2", "CountdownText", "_c3", "TimeDisplay", "_c4", "HistoryButton", "button", "_c5", "CountdownDisplay", "currentPeriod", "_s", "timeLeft", "setTimeLeft", "timer", "setInterval", "drawTime", "now", "Date", "getTime", "difference", "hours", "Math", "floor", "minutes", "seconds", "toString", "padStart", "clearInterval", "children", "period", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c6", "$RefreshReg$"], "sources": ["D:/liu/html/frontend/src/components/CountdownDisplay.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\n\ninterface CountdownDisplayProps {\n  currentPeriod: any;\n}\n\nconst CountdownContainer = styled.div`\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);\n  color: white;\n  padding: 20px;\n  border-radius: 8px;\n  text-align: center;\n  margin: 20px 0;\n  box-shadow: 0 4px 12px rgba(0,0,0,0.15);\n`;\n\nconst PeriodInfo = styled.div`\n  font-size: 24px;\n  font-weight: bold;\n  margin-bottom: 10px;\n`;\n\nconst CountdownText = styled.div`\n  font-size: 18px;\n  margin-bottom: 15px;\n`;\n\nconst TimeDisplay = styled.div`\n  font-size: 32px;\n  font-weight: bold;\n  font-family: 'Courier New', monospace;\n  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);\n  letter-spacing: 2px;\n`;\n\nconst HistoryButton = styled.button`\n  background: rgba(255,255,255,0.2);\n  border: 2px solid rgba(255,255,255,0.5);\n  color: white;\n  padding: 8px 16px;\n  border-radius: 4px;\n  margin-top: 15px;\n  cursor: pointer;\n  font-size: 14px;\n  \n  &:hover {\n    background: rgba(255,255,255,0.3);\n  }\n`;\n\nconst CountdownDisplay: React.FC<CountdownDisplayProps> = ({ currentPeriod }) => {\n  const [timeLeft, setTimeLeft] = useState('00:00:00');\n\n  useEffect(() => {\n    const timer = setInterval(() => {\n      if (currentPeriod?.drawTime) {\n        const now = new Date().getTime();\n        const drawTime = new Date(currentPeriod.drawTime).getTime();\n        const difference = drawTime - now;\n\n        if (difference > 0) {\n          const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));\n          const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));\n          const seconds = Math.floor((difference % (1000 * 60)) / 1000);\n\n          setTimeLeft(\n            `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`\n          );\n        } else {\n          setTimeLeft('00:00:00');\n        }\n      }\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, [currentPeriod]);\n\n  return (\n    <CountdownContainer>\n      <PeriodInfo>\n        澳门葡京新彩第{currentPeriod?.period || '---'}期\n      </PeriodInfo>\n      <CountdownText>\n        开奖倒计时: \n      </CountdownText>\n      <TimeDisplay>\n        {timeLeft}\n      </TimeDisplay>\n      <HistoryButton>\n        历史记录\n      </HistoryButton>\n    </CountdownContainer>\n  );\n};\n\nexport default CountdownDisplay;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAMvC,MAAMC,kBAAkB,GAAGH,MAAM,CAACI,GAAG;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GARIF,kBAAkB;AAUxB,MAAMG,UAAU,GAAGN,MAAM,CAACI,GAAG;AAC7B;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAJID,UAAU;AAMhB,MAAME,aAAa,GAAGR,MAAM,CAACI,GAAG;AAChC;AACA;AACA,CAAC;AAACK,GAAA,GAHID,aAAa;AAKnB,MAAME,WAAW,GAAGV,MAAM,CAACI,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACO,GAAA,GANID,WAAW;AAQjB,MAAME,aAAa,GAAGZ,MAAM,CAACa,MAAM;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAbIF,aAAa;AAenB,MAAMG,gBAAiD,GAAGA,CAAC;EAAEC;AAAc,CAAC,KAAK;EAAAC,EAAA;EAC/E,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAC,UAAU,CAAC;EAEpDC,SAAS,CAAC,MAAM;IACd,MAAMqB,KAAK,GAAGC,WAAW,CAAC,MAAM;MAC9B,IAAIL,aAAa,aAAbA,aAAa,eAAbA,aAAa,CAAEM,QAAQ,EAAE;QAC3B,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;QAChC,MAAMH,QAAQ,GAAG,IAAIE,IAAI,CAACR,aAAa,CAACM,QAAQ,CAAC,CAACG,OAAO,CAAC,CAAC;QAC3D,MAAMC,UAAU,GAAGJ,QAAQ,GAAGC,GAAG;QAEjC,IAAIG,UAAU,GAAG,CAAC,EAAE;UAClB,MAAMC,KAAK,GAAGC,IAAI,CAACC,KAAK,CAAEH,UAAU,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,IAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;UACjF,MAAMI,OAAO,GAAGF,IAAI,CAACC,KAAK,CAAEH,UAAU,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,IAAK,IAAI,GAAG,EAAE,CAAC,CAAC;UACzE,MAAMK,OAAO,GAAGH,IAAI,CAACC,KAAK,CAAEH,UAAU,IAAI,IAAI,GAAG,EAAE,CAAC,GAAI,IAAI,CAAC;UAE7DP,WAAW,CACT,GAAGQ,KAAK,CAACK,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIH,OAAO,CAACE,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,OAAO,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EACpH,CAAC;QACH,CAAC,MAAM;UACLd,WAAW,CAAC,UAAU,CAAC;QACzB;MACF;IACF,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMe,aAAa,CAACd,KAAK,CAAC;EACnC,CAAC,EAAE,CAACJ,aAAa,CAAC,CAAC;EAEnB,oBACEd,OAAA,CAACC,kBAAkB;IAAAgC,QAAA,gBACjBjC,OAAA,CAACI,UAAU;MAAA6B,QAAA,GAAC,4CACH,EAAC,CAAAnB,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEoB,MAAM,KAAI,KAAK,EAAC,QACzC;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eACbtC,OAAA,CAACM,aAAa;MAAA2B,QAAA,EAAC;IAEf;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAe,CAAC,eAChBtC,OAAA,CAACQ,WAAW;MAAAyB,QAAA,EACTjB;IAAQ;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eACdtC,OAAA,CAACU,aAAa;MAAAuB,QAAA,EAAC;IAEf;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAe,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEzB,CAAC;AAACvB,EAAA,CA3CIF,gBAAiD;AAAA0B,GAAA,GAAjD1B,gBAAiD;AA6CvD,eAAeA,gBAAgB;AAAC,IAAAV,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAA2B,GAAA;AAAAC,YAAA,CAAArC,EAAA;AAAAqC,YAAA,CAAAnC,GAAA;AAAAmC,YAAA,CAAAjC,GAAA;AAAAiC,YAAA,CAAA/B,GAAA;AAAA+B,YAAA,CAAA5B,GAAA;AAAA4B,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}