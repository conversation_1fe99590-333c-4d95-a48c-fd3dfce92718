{"ast": null, "code": "var _jsxFileName = \"D:\\\\liu\\\\html\\\\frontend\\\\src\\\\pages\\\\StatisticsPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Row, Col, Card, Typography, Spin, message } from 'antd';\nimport { BarChartOutlined } from '@ant-design/icons';\nimport { apiService } from '../services/apiService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst StatisticsPage = () => {\n  _s();\n  const [loading, setLoading] = useState(true);\n  const [stats, setStats] = useState(null);\n  useEffect(() => {\n    loadStats();\n  }, []);\n  const loadStats = async () => {\n    try {\n      const statsData = await apiService.getLotteryStats();\n      setStats(statsData);\n      setLoading(false);\n    } catch (error) {\n      console.error('加载统计数据失败:', error);\n      message.error('加载统计数据失败');\n      setLoading(false);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-container\",\n      children: /*#__PURE__*/_jsxDEV(Spin, {\n        size: \"large\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      children: [/*#__PURE__*/_jsxDEV(BarChartOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this), \" \\u7EDF\\u8BA1\\u5206\\u6790\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Text, {\n      type: \"secondary\",\n      children: \"\\u5F69\\u7968\\u6570\\u636E\\u7EDF\\u8BA1\\u5206\\u6790\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginTop: 24\n      },\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u7EDF\\u8BA1\\u529F\\u80FD\\u5F00\\u53D1\\u4E2D\",\n          children: /*#__PURE__*/_jsxDEV(Text, {\n            children: \"\\u7EDF\\u8BA1\\u5206\\u6790\\u529F\\u80FD\\u6B63\\u5728\\u5F00\\u53D1\\u4E2D\\uFF0C\\u656C\\u8BF7\\u671F\\u5F85...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 37,\n    columnNumber: 5\n  }, this);\n};\n_s(StatisticsPage, \"vZMOJxkaAUIiEQ08Aa7a/Gw2gZU=\");\n_c = StatisticsPage;\nexport default StatisticsPage;\nvar _c;\n$RefreshReg$(_c, \"StatisticsPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Row", "Col", "Card", "Typography", "Spin", "message", "BarChartOutlined", "apiService", "jsxDEV", "_jsxDEV", "Title", "Text", "StatisticsPage", "_s", "loading", "setLoading", "stats", "setStats", "loadStats", "statsData", "getLotteryStats", "error", "console", "className", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "level", "type", "gutter", "style", "marginTop", "span", "title", "_c", "$RefreshReg$"], "sources": ["D:/liu/html/frontend/src/pages/StatisticsPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Row, Col, Card, Typography, Spin, message } from 'antd';\nimport { BarChartOutlined } from '@ant-design/icons';\nimport { apiService } from '../services/apiService';\n\nconst { Title, Text } = Typography;\n\nconst StatisticsPage: React.FC = () => {\n  const [loading, setLoading] = useState(true);\n  const [stats, setStats] = useState<any>(null);\n\n  useEffect(() => {\n    loadStats();\n  }, []);\n\n  const loadStats = async () => {\n    try {\n      const statsData = await apiService.getLotteryStats();\n      setStats(statsData);\n      setLoading(false);\n    } catch (error) {\n      console.error('加载统计数据失败:', error);\n      message.error('加载统计数据失败');\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"loading-container\">\n        <Spin size=\"large\" />\n      </div>\n    );\n  }\n\n  return (\n    <div>\n      <Title level={2}>\n        <BarChartOutlined /> 统计分析\n      </Title>\n      <Text type=\"secondary\">彩票数据统计分析</Text>\n      \n      <Row gutter={[16, 16]} style={{ marginTop: 24 }}>\n        <Col span={24}>\n          <Card title=\"统计功能开发中\">\n            <Text>统计分析功能正在开发中，敬请期待...</Text>\n          </Card>\n        </Col>\n      </Row>\n    </div>\n  );\n};\n\nexport default StatisticsPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,UAAU,EAAEC,IAAI,EAAEC,OAAO,QAAQ,MAAM;AAChE,SAASC,gBAAgB,QAAQ,mBAAmB;AACpD,SAASC,UAAU,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGR,UAAU;AAElC,MAAMS,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkB,KAAK,EAAEC,QAAQ,CAAC,GAAGnB,QAAQ,CAAM,IAAI,CAAC;EAE7CC,SAAS,CAAC,MAAM;IACdmB,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACF,MAAMC,SAAS,GAAG,MAAMZ,UAAU,CAACa,eAAe,CAAC,CAAC;MACpDH,QAAQ,CAACE,SAAS,CAAC;MACnBJ,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjChB,OAAO,CAACgB,KAAK,CAAC,UAAU,CAAC;MACzBN,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAID,OAAO,EAAE;IACX,oBACEL,OAAA;MAAKc,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChCf,OAAA,CAACL,IAAI;QAACqB,IAAI,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC;EAEV;EAEA,oBACEpB,OAAA;IAAAe,QAAA,gBACEf,OAAA,CAACC,KAAK;MAACoB,KAAK,EAAE,CAAE;MAAAN,QAAA,gBACdf,OAAA,CAACH,gBAAgB;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,6BACtB;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eACRpB,OAAA,CAACE,IAAI;MAACoB,IAAI,EAAC,WAAW;MAAAP,QAAA,EAAC;IAAQ;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAEtCpB,OAAA,CAACT,GAAG;MAACgC,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACC,KAAK,EAAE;QAAEC,SAAS,EAAE;MAAG,CAAE;MAAAV,QAAA,eAC9Cf,OAAA,CAACR,GAAG;QAACkC,IAAI,EAAE,EAAG;QAAAX,QAAA,eACZf,OAAA,CAACP,IAAI;UAACkC,KAAK,EAAC,4CAAS;UAAAZ,QAAA,eACnBf,OAAA,CAACE,IAAI;YAAAa,QAAA,EAAC;UAAmB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChB,EAAA,CA5CID,cAAwB;AAAAyB,EAAA,GAAxBzB,cAAwB;AA8C9B,eAAeA,cAAc;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}