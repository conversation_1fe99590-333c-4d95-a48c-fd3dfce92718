{"ast": null, "code": "var _jsxFileName = \"D:\\\\liu\\\\html\\\\frontend\\\\src\\\\components\\\\CountdownDisplay.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CountdownContainer = styled.div`\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);\n  color: white;\n  padding: 20px;\n  border-radius: 8px;\n  text-align: center;\n  margin: 20px 0;\n  box-shadow: 0 4px 12px rgba(0,0,0,0.15);\n`;\n_c = CountdownContainer;\nconst PeriodInfo = styled.div`\n  font-size: 24px;\n  font-weight: bold;\n  margin-bottom: 10px;\n`;\n_c2 = PeriodInfo;\nconst CountdownText = styled.div`\n  font-size: 18px;\n  margin-bottom: 15px;\n`;\n_c3 = CountdownText;\nconst TimeDisplay = styled.div`\n  font-size: 32px;\n  font-weight: bold;\n  font-family: 'Courier New', monospace;\n  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);\n  letter-spacing: 2px;\n`;\n_c4 = TimeDisplay;\nconst HistoryButton = styled.button`\n  background: rgba(255,255,255,0.2);\n  border: 2px solid rgba(255,255,255,0.5);\n  color: white;\n  padding: 8px 16px;\n  border-radius: 4px;\n  margin-top: 15px;\n  cursor: pointer;\n  font-size: 14px;\n  \n  &:hover {\n    background: rgba(255,255,255,0.3);\n  }\n`;\n_c5 = HistoryButton;\nconst CountdownDisplay = ({\n  currentPeriod\n}) => {\n  _s();\n  const [timeLeft, setTimeLeft] = useState('00:00:00');\n  const [currentPeriodNumber, setCurrentPeriodNumber] = useState(188);\n  useEffect(() => {\n    // 模拟下一期开奖时间（每5分钟一期）\n    const getNextDrawTime = () => {\n      const now = new Date();\n      const nextDraw = new Date(now);\n      nextDraw.setMinutes(Math.ceil(now.getMinutes() / 5) * 5, 0, 0);\n      if (nextDraw <= now) {\n        nextDraw.setMinutes(nextDraw.getMinutes() + 5);\n      }\n      return nextDraw;\n    };\n    const timer = setInterval(() => {\n      const now = new Date().getTime();\n      const drawTime = getNextDrawTime().getTime();\n      const difference = drawTime - now;\n      if (difference > 0) {\n        const hours = Math.floor(difference % (1000 * 60 * 60 * 24) / (1000 * 60 * 60));\n        const minutes = Math.floor(difference % (1000 * 60 * 60) / (1000 * 60));\n        const seconds = Math.floor(difference % (1000 * 60) / 1000);\n        const milliseconds = Math.floor(difference % 1000 / 10);\n        setTimeLeft(`${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}:${milliseconds.toString().padStart(2, '0')}`);\n      } else {\n        setTimeLeft('00:00:00:00');\n        // 当倒计时结束时，增加期数\n        setCurrentPeriodNumber(prev => prev + 1);\n      }\n    }, 100); // 更频繁更新以显示毫秒\n\n    return () => clearInterval(timer);\n  }, []);\n  return /*#__PURE__*/_jsxDEV(CountdownContainer, {\n    children: [/*#__PURE__*/_jsxDEV(PeriodInfo, {\n      children: [\"\\u6FB3\\u95E8\\u8461\\u4EAC\\u65B0\\u5F69\\u7B2C\", (currentPeriod === null || currentPeriod === void 0 ? void 0 : currentPeriod.period) || '---', \"\\u671F\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CountdownText, {\n      children: \"\\u5F00\\u5956\\u5012\\u8BA1\\u65F6:\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TimeDisplay, {\n      children: timeLeft\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(HistoryButton, {\n      children: \"\\u5386\\u53F2\\u8BB0\\u5F55\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 93,\n    columnNumber: 5\n  }, this);\n};\n_s(CountdownDisplay, \"xQDvpfNw5EdJYXMBR7dqX1xn1tk=\");\n_c6 = CountdownDisplay;\nexport default CountdownDisplay;\nvar _c, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"CountdownContainer\");\n$RefreshReg$(_c2, \"PeriodInfo\");\n$RefreshReg$(_c3, \"CountdownText\");\n$RefreshReg$(_c4, \"TimeDisplay\");\n$RefreshReg$(_c5, \"HistoryButton\");\n$RefreshReg$(_c6, \"CountdownDisplay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "styled", "jsxDEV", "_jsxDEV", "CountdownContainer", "div", "_c", "PeriodInfo", "_c2", "CountdownText", "_c3", "TimeDisplay", "_c4", "HistoryButton", "button", "_c5", "CountdownDisplay", "currentPeriod", "_s", "timeLeft", "setTimeLeft", "currentPeriodNumber", "setCurrentPeriodNumber", "getNextDrawTime", "now", "Date", "nextDraw", "setMinutes", "Math", "ceil", "getMinutes", "timer", "setInterval", "getTime", "drawTime", "difference", "hours", "floor", "minutes", "seconds", "milliseconds", "toString", "padStart", "prev", "clearInterval", "children", "period", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c6", "$RefreshReg$"], "sources": ["D:/liu/html/frontend/src/components/CountdownDisplay.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\n\ninterface CountdownDisplayProps {\n  currentPeriod: any;\n}\n\nconst CountdownContainer = styled.div`\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);\n  color: white;\n  padding: 20px;\n  border-radius: 8px;\n  text-align: center;\n  margin: 20px 0;\n  box-shadow: 0 4px 12px rgba(0,0,0,0.15);\n`;\n\nconst PeriodInfo = styled.div`\n  font-size: 24px;\n  font-weight: bold;\n  margin-bottom: 10px;\n`;\n\nconst CountdownText = styled.div`\n  font-size: 18px;\n  margin-bottom: 15px;\n`;\n\nconst TimeDisplay = styled.div`\n  font-size: 32px;\n  font-weight: bold;\n  font-family: 'Courier New', monospace;\n  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);\n  letter-spacing: 2px;\n`;\n\nconst HistoryButton = styled.button`\n  background: rgba(255,255,255,0.2);\n  border: 2px solid rgba(255,255,255,0.5);\n  color: white;\n  padding: 8px 16px;\n  border-radius: 4px;\n  margin-top: 15px;\n  cursor: pointer;\n  font-size: 14px;\n  \n  &:hover {\n    background: rgba(255,255,255,0.3);\n  }\n`;\n\nconst CountdownDisplay: React.FC<CountdownDisplayProps> = ({ currentPeriod }) => {\n  const [timeLeft, setTimeLeft] = useState('00:00:00');\n  const [currentPeriodNumber, setCurrentPeriodNumber] = useState(188);\n\n  useEffect(() => {\n    // 模拟下一期开奖时间（每5分钟一期）\n    const getNextDrawTime = () => {\n      const now = new Date();\n      const nextDraw = new Date(now);\n      nextDraw.setMinutes(Math.ceil(now.getMinutes() / 5) * 5, 0, 0);\n      if (nextDraw <= now) {\n        nextDraw.setMinutes(nextDraw.getMinutes() + 5);\n      }\n      return nextDraw;\n    };\n\n    const timer = setInterval(() => {\n      const now = new Date().getTime();\n      const drawTime = getNextDrawTime().getTime();\n      const difference = drawTime - now;\n\n      if (difference > 0) {\n        const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));\n        const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));\n        const seconds = Math.floor((difference % (1000 * 60)) / 1000);\n        const milliseconds = Math.floor((difference % 1000) / 10);\n\n        setTimeLeft(\n          `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}:${milliseconds.toString().padStart(2, '0')}`\n        );\n      } else {\n        setTimeLeft('00:00:00:00');\n        // 当倒计时结束时，增加期数\n        setCurrentPeriodNumber(prev => prev + 1);\n      }\n    }, 100); // 更频繁更新以显示毫秒\n\n    return () => clearInterval(timer);\n  }, []);\n\n  return (\n    <CountdownContainer>\n      <PeriodInfo>\n        澳门葡京新彩第{currentPeriod?.period || '---'}期\n      </PeriodInfo>\n      <CountdownText>\n        开奖倒计时: \n      </CountdownText>\n      <TimeDisplay>\n        {timeLeft}\n      </TimeDisplay>\n      <HistoryButton>\n        历史记录\n      </HistoryButton>\n    </CountdownContainer>\n  );\n};\n\nexport default CountdownDisplay;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAMvC,MAAMC,kBAAkB,GAAGH,MAAM,CAACI,GAAG;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GARIF,kBAAkB;AAUxB,MAAMG,UAAU,GAAGN,MAAM,CAACI,GAAG;AAC7B;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAJID,UAAU;AAMhB,MAAME,aAAa,GAAGR,MAAM,CAACI,GAAG;AAChC;AACA;AACA,CAAC;AAACK,GAAA,GAHID,aAAa;AAKnB,MAAME,WAAW,GAAGV,MAAM,CAACI,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACO,GAAA,GANID,WAAW;AAQjB,MAAME,aAAa,GAAGZ,MAAM,CAACa,MAAM;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAbIF,aAAa;AAenB,MAAMG,gBAAiD,GAAGA,CAAC;EAAEC;AAAc,CAAC,KAAK;EAAAC,EAAA;EAC/E,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAC,UAAU,CAAC;EACpD,MAAM,CAACsB,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGvB,QAAQ,CAAC,GAAG,CAAC;EAEnEC,SAAS,CAAC,MAAM;IACd;IACA,MAAMuB,eAAe,GAAGA,CAAA,KAAM;MAC5B,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;MACtB,MAAMC,QAAQ,GAAG,IAAID,IAAI,CAACD,GAAG,CAAC;MAC9BE,QAAQ,CAACC,UAAU,CAACC,IAAI,CAACC,IAAI,CAACL,GAAG,CAACM,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC9D,IAAIJ,QAAQ,IAAIF,GAAG,EAAE;QACnBE,QAAQ,CAACC,UAAU,CAACD,QAAQ,CAACI,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC;MAChD;MACA,OAAOJ,QAAQ;IACjB,CAAC;IAED,MAAMK,KAAK,GAAGC,WAAW,CAAC,MAAM;MAC9B,MAAMR,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACQ,OAAO,CAAC,CAAC;MAChC,MAAMC,QAAQ,GAAGX,eAAe,CAAC,CAAC,CAACU,OAAO,CAAC,CAAC;MAC5C,MAAME,UAAU,GAAGD,QAAQ,GAAGV,GAAG;MAEjC,IAAIW,UAAU,GAAG,CAAC,EAAE;QAClB,MAAMC,KAAK,GAAGR,IAAI,CAACS,KAAK,CAAEF,UAAU,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,IAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;QACjF,MAAMG,OAAO,GAAGV,IAAI,CAACS,KAAK,CAAEF,UAAU,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,IAAK,IAAI,GAAG,EAAE,CAAC,CAAC;QACzE,MAAMI,OAAO,GAAGX,IAAI,CAACS,KAAK,CAAEF,UAAU,IAAI,IAAI,GAAG,EAAE,CAAC,GAAI,IAAI,CAAC;QAC7D,MAAMK,YAAY,GAAGZ,IAAI,CAACS,KAAK,CAAEF,UAAU,GAAG,IAAI,GAAI,EAAE,CAAC;QAEzDf,WAAW,CACT,GAAGgB,KAAK,CAACK,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIJ,OAAO,CAACG,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIH,OAAO,CAACE,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,YAAY,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAChK,CAAC;MACH,CAAC,MAAM;QACLtB,WAAW,CAAC,aAAa,CAAC;QAC1B;QACAE,sBAAsB,CAACqB,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MAC1C;IACF,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;IAET,OAAO,MAAMC,aAAa,CAACb,KAAK,CAAC;EACnC,CAAC,EAAE,EAAE,CAAC;EAEN,oBACE5B,OAAA,CAACC,kBAAkB;IAAAyC,QAAA,gBACjB1C,OAAA,CAACI,UAAU;MAAAsC,QAAA,GAAC,4CACH,EAAC,CAAA5B,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE6B,MAAM,KAAI,KAAK,EAAC,QACzC;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eACb/C,OAAA,CAACM,aAAa;MAAAoC,QAAA,EAAC;IAEf;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAe,CAAC,eAChB/C,OAAA,CAACQ,WAAW;MAAAkC,QAAA,EACT1B;IAAQ;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eACd/C,OAAA,CAACU,aAAa;MAAAgC,QAAA,EAAC;IAEf;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAe,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEzB,CAAC;AAAChC,EAAA,CAxDIF,gBAAiD;AAAAmC,GAAA,GAAjDnC,gBAAiD;AA0DvD,eAAeA,gBAAgB;AAAC,IAAAV,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAoC,GAAA;AAAAC,YAAA,CAAA9C,EAAA;AAAA8C,YAAA,CAAA5C,GAAA;AAAA4C,YAAA,CAAA1C,GAAA;AAAA0C,YAAA,CAAAxC,GAAA;AAAAwC,YAAA,CAAArC,GAAA;AAAAqC,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}