{"ast": null, "code": "import axios from 'axios';\n\n// API响应接口\n\n// 分页响应接口\n\nclass ApiService {\n  constructor() {\n    this.api = void 0;\n    this.configs = {};\n    this.api = axios.create({\n      baseURL: process.env.REACT_APP_API_URL || 'http://localhost:3001/api',\n      timeout: 10000,\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n    this.setupInterceptors();\n  }\n  setupInterceptors() {\n    // 请求拦截器\n    this.api.interceptors.request.use(config => {\n      // 添加认证token\n      const token = localStorage.getItem('auth_token');\n      if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n      }\n      return config;\n    }, error => {\n      return Promise.reject(error);\n    });\n\n    // 响应拦截器\n    this.api.interceptors.response.use(response => {\n      return response;\n    }, error => {\n      var _error$response;\n      if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n        // 清除过期token\n        localStorage.removeItem('auth_token');\n        // 可以在这里触发登录页面跳转\n      }\n      return Promise.reject(error);\n    });\n  }\n\n  // 通用请求方法\n  async request(method, url, data) {\n    try {\n      const response = await this.api.request({\n        method,\n        url,\n        data\n      });\n      if (response.data.success) {\n        return response.data.data;\n      } else {\n        throw new Error(response.data.message || '请求失败');\n      }\n    } catch (error) {\n      console.error(`API请求失败 [${method} ${url}]:`, error);\n      throw error;\n    }\n  }\n\n  // GET请求\n  async get(url) {\n    return this.request('GET', url);\n  }\n\n  // POST请求\n  async post(url, data) {\n    return this.request('POST', url, data);\n  }\n\n  // PUT请求\n  async put(url, data) {\n    return this.request('PUT', url, data);\n  }\n\n  // DELETE请求\n  async delete(url) {\n    return this.request('DELETE', url);\n  }\n\n  // 彩票相关API\n  async getCurrentPeriod() {\n    return this.get('/lottery/current');\n  }\n  async getLotteryResults(page = 1, limit = 10) {\n    return this.get(`/lottery/results?page=${page}&limit=${limit}`);\n  }\n  async getLotteryResult(periodNumber) {\n    return this.get(`/lottery/results/${periodNumber}`);\n  }\n  async getLatestResults(count = 5) {\n    return this.get(`/lottery/latest/${count}`);\n  }\n  async getLotteryPeriods(status, page = 1, limit = 20) {\n    const params = new URLSearchParams();\n    if (status) params.append('status', status);\n    params.append('page', page.toString());\n    params.append('limit', limit.toString());\n    return this.get(`/lottery/periods?${params}`);\n  }\n  async getLotteryStats() {\n    return this.get('/lottery/stats');\n  }\n\n  // 预测相关API\n  async getCurrentPredictions() {\n    return this.get('/predictions/current');\n  }\n  async getPredictionsByType(type, periodId, limit = 10) {\n    const params = new URLSearchParams();\n    if (periodId) params.append('periodId', periodId.toString());\n    params.append('limit', limit.toString());\n    return this.get(`/predictions/type/${type}?${params}`);\n  }\n  async getPredictionsByPeriod(periodNumber) {\n    return this.get(`/predictions/period/${periodNumber}`);\n  }\n  async getPredictionTypes() {\n    return this.get('/predictions/types');\n  }\n  async getPredictionAccuracy(type) {\n    const params = type ? `?type=${type}` : '';\n    return this.get(`/predictions/accuracy${params}`);\n  }\n\n  // 配置相关API\n  async getZodiacConfig() {\n    return this.get('/config/zodiac');\n  }\n  async getColorConfig() {\n    return this.get('/config/color');\n  }\n  async getElementConfig() {\n    return this.get('/config/element');\n  }\n  async getSystemConfig() {\n    return this.get('/config/system');\n  }\n  async getAllConfigs() {\n    return this.get('/config/all');\n  }\n  async getZodiacByNumber(number) {\n    return this.get(`/config/zodiac/number/${number}`);\n  }\n  async getNumbersByZodiac(zodiacName) {\n    return this.get(`/config/zodiac/${zodiacName}/numbers`);\n  }\n\n  // 认证相关API\n  async login(username, password) {\n    return this.post('/auth/login', {\n      username,\n      password\n    });\n  }\n  async register(username, email, password) {\n    return this.post('/auth/register', {\n      username,\n      email,\n      password\n    });\n  }\n  async verifyToken() {\n    return this.get('/auth/verify');\n  }\n  async getUserProfile() {\n    return this.get('/auth/profile');\n  }\n  async updateProfile(data) {\n    return this.put('/auth/profile', data);\n  }\n  async changePassword(currentPassword, newPassword) {\n    return this.put('/auth/password', {\n      currentPassword,\n      newPassword\n    });\n  }\n\n  // 加载配置数据\n  async loadConfigs() {\n    try {\n      this.configs = await this.getAllConfigs();\n      return this.configs;\n    } catch (error) {\n      console.error('加载配置失败:', error);\n      throw error;\n    }\n  }\n\n  // 获取缓存的配置\n  getConfigs() {\n    return this.configs;\n  }\n\n  // 根据号码获取生肖\n  getZodiacByNumberFromCache(number) {\n    const zodiacs = this.configs.zodiacs || [];\n    for (const zodiac of zodiacs) {\n      if (zodiac.numbers.includes(number)) {\n        return zodiac.name;\n      }\n    }\n    return null;\n  }\n\n  // 根据号码获取波色\n  getColorByNumberFromCache(number) {\n    const colors = this.configs.colors || [];\n    for (const color of colors) {\n      if (color.numbers.includes(number)) {\n        return color.name;\n      }\n    }\n    return null;\n  }\n\n  // 根据号码获取五行\n  getElementByNumberFromCache(number) {\n    const elements = this.configs.elements || [];\n    for (const element of elements) {\n      if (element.numbers.includes(number)) {\n        return element.name;\n      }\n    }\n    return null;\n  }\n}\n\n// 导出单例\nexport const apiService = new ApiService();", "map": {"version": 3, "names": ["axios", "ApiService", "constructor", "api", "configs", "create", "baseURL", "process", "env", "REACT_APP_API_URL", "timeout", "headers", "setupInterceptors", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "_error$response", "status", "removeItem", "method", "url", "data", "success", "Error", "message", "console", "get", "post", "put", "delete", "getCurrentPeriod", "getLotteryResults", "page", "limit", "getLotteryResult", "periodNumber", "getLatestResults", "count", "getLotteryPeriods", "params", "URLSearchParams", "append", "toString", "getLotteryStats", "getCurrentPredictions", "getPredictionsByType", "type", "periodId", "getPredictionsByPeriod", "getPredictionTypes", "getPredictionAccuracy", "getZodiacConfig", "getColorConfig", "getElementConfig", "getSystemConfig", "getAllConfigs", "getZodiacByNumber", "number", "getNumbersByZodiac", "zodiacName", "login", "username", "password", "register", "email", "verifyToken", "getUserProfile", "updateProfile", "changePassword", "currentPassword", "newPassword", "loadConfigs", "getConfigs", "getZodiacByNumberFromCache", "zodiacs", "zodiac", "numbers", "includes", "name", "getColorByNumberFromCache", "colors", "color", "getElementByNumberFromCache", "elements", "element", "apiService"], "sources": ["D:/liu/html/frontend/src/services/apiService.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosResponse } from 'axios';\n\n// API响应接口\ninterface ApiResponse<T = any> {\n  success: boolean;\n  data?: T;\n  message?: string;\n}\n\n// 分页响应接口\ninterface PaginatedResponse<T> {\n  results: T[];\n  pagination: {\n    total: number;\n    page: number;\n    limit: number;\n    pages: number;\n  };\n}\n\nclass ApiService {\n  private api: AxiosInstance;\n  private configs: any = {};\n\n  constructor() {\n    this.api = axios.create({\n      baseURL: process.env.REACT_APP_API_URL || 'http://localhost:3001/api',\n      timeout: 10000,\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    this.setupInterceptors();\n  }\n\n  private setupInterceptors() {\n    // 请求拦截器\n    this.api.interceptors.request.use(\n      (config) => {\n        // 添加认证token\n        const token = localStorage.getItem('auth_token');\n        if (token) {\n          config.headers.Authorization = `Bearer ${token}`;\n        }\n        return config;\n      },\n      (error) => {\n        return Promise.reject(error);\n      }\n    );\n\n    // 响应拦截器\n    this.api.interceptors.response.use(\n      (response: AxiosResponse<ApiResponse>) => {\n        return response;\n      },\n      (error) => {\n        if (error.response?.status === 401) {\n          // 清除过期token\n          localStorage.removeItem('auth_token');\n          // 可以在这里触发登录页面跳转\n        }\n        return Promise.reject(error);\n      }\n    );\n  }\n\n  // 通用请求方法\n  private async request<T>(method: string, url: string, data?: any): Promise<T> {\n    try {\n      const response = await this.api.request<ApiResponse<T>>({\n        method,\n        url,\n        data,\n      });\n      \n      if (response.data.success) {\n        return response.data.data as T;\n      } else {\n        throw new Error(response.data.message || '请求失败');\n      }\n    } catch (error: any) {\n      console.error(`API请求失败 [${method} ${url}]:`, error);\n      throw error;\n    }\n  }\n\n  // GET请求\n  async get<T>(url: string): Promise<T> {\n    return this.request<T>('GET', url);\n  }\n\n  // POST请求\n  async post<T>(url: string, data?: any): Promise<T> {\n    return this.request<T>('POST', url, data);\n  }\n\n  // PUT请求\n  async put<T>(url: string, data?: any): Promise<T> {\n    return this.request<T>('PUT', url, data);\n  }\n\n  // DELETE请求\n  async delete<T>(url: string): Promise<T> {\n    return this.request<T>('DELETE', url);\n  }\n\n  // 彩票相关API\n  async getCurrentPeriod() {\n    return this.get('/lottery/current');\n  }\n\n  async getLotteryResults(page = 1, limit = 10) {\n    return this.get<PaginatedResponse<any>>(`/lottery/results?page=${page}&limit=${limit}`);\n  }\n\n  async getLotteryResult(periodNumber: number) {\n    return this.get(`/lottery/results/${periodNumber}`);\n  }\n\n  async getLatestResults(count = 5) {\n    return this.get(`/lottery/latest/${count}`);\n  }\n\n  async getLotteryPeriods(status?: string, page = 1, limit = 20) {\n    const params = new URLSearchParams();\n    if (status) params.append('status', status);\n    params.append('page', page.toString());\n    params.append('limit', limit.toString());\n    \n    return this.get<PaginatedResponse<any>>(`/lottery/periods?${params}`);\n  }\n\n  async getLotteryStats() {\n    return this.get('/lottery/stats');\n  }\n\n  // 预测相关API\n  async getCurrentPredictions() {\n    return this.get('/predictions/current');\n  }\n\n  async getPredictionsByType(type: string, periodId?: number, limit = 10) {\n    const params = new URLSearchParams();\n    if (periodId) params.append('periodId', periodId.toString());\n    params.append('limit', limit.toString());\n    \n    return this.get(`/predictions/type/${type}?${params}`);\n  }\n\n  async getPredictionsByPeriod(periodNumber: number) {\n    return this.get(`/predictions/period/${periodNumber}`);\n  }\n\n  async getPredictionTypes() {\n    return this.get('/predictions/types');\n  }\n\n  async getPredictionAccuracy(type?: string) {\n    const params = type ? `?type=${type}` : '';\n    return this.get(`/predictions/accuracy${params}`);\n  }\n\n  // 配置相关API\n  async getZodiacConfig() {\n    return this.get('/config/zodiac');\n  }\n\n  async getColorConfig() {\n    return this.get('/config/color');\n  }\n\n  async getElementConfig() {\n    return this.get('/config/element');\n  }\n\n  async getSystemConfig() {\n    return this.get('/config/system');\n  }\n\n  async getAllConfigs() {\n    return this.get('/config/all');\n  }\n\n  async getZodiacByNumber(number: number) {\n    return this.get(`/config/zodiac/number/${number}`);\n  }\n\n  async getNumbersByZodiac(zodiacName: string) {\n    return this.get(`/config/zodiac/${zodiacName}/numbers`);\n  }\n\n  // 认证相关API\n  async login(username: string, password: string) {\n    return this.post('/auth/login', { username, password });\n  }\n\n  async register(username: string, email: string, password: string) {\n    return this.post('/auth/register', { username, email, password });\n  }\n\n  async verifyToken() {\n    return this.get('/auth/verify');\n  }\n\n  async getUserProfile() {\n    return this.get('/auth/profile');\n  }\n\n  async updateProfile(data: any) {\n    return this.put('/auth/profile', data);\n  }\n\n  async changePassword(currentPassword: string, newPassword: string) {\n    return this.put('/auth/password', { currentPassword, newPassword });\n  }\n\n  // 加载配置数据\n  async loadConfigs() {\n    try {\n      this.configs = await this.getAllConfigs();\n      return this.configs;\n    } catch (error) {\n      console.error('加载配置失败:', error);\n      throw error;\n    }\n  }\n\n  // 获取缓存的配置\n  getConfigs() {\n    return this.configs;\n  }\n\n  // 根据号码获取生肖\n  getZodiacByNumberFromCache(number: number): string | null {\n    const zodiacs = this.configs.zodiacs || [];\n    for (const zodiac of zodiacs) {\n      if (zodiac.numbers.includes(number)) {\n        return zodiac.name;\n      }\n    }\n    return null;\n  }\n\n  // 根据号码获取波色\n  getColorByNumberFromCache(number: number): string | null {\n    const colors = this.configs.colors || [];\n    for (const color of colors) {\n      if (color.numbers.includes(number)) {\n        return color.name;\n      }\n    }\n    return null;\n  }\n\n  // 根据号码获取五行\n  getElementByNumberFromCache(number: number): string | null {\n    const elements = this.configs.elements || [];\n    for (const element of elements) {\n      if (element.numbers.includes(number)) {\n        return element.name;\n      }\n    }\n    return null;\n  }\n}\n\n// 导出单例\nexport const apiService = new ApiService();\n"], "mappings": "AAAA,OAAOA,KAAK,MAAwC,OAAO;;AAE3D;;AAOA;;AAWA,MAAMC,UAAU,CAAC;EAIfC,WAAWA,CAAA,EAAG;IAAA,KAHNC,GAAG;IAAA,KACHC,OAAO,GAAQ,CAAC,CAAC;IAGvB,IAAI,CAACD,GAAG,GAAGH,KAAK,CAACK,MAAM,CAAC;MACtBC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;MACrEC,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IAEF,IAAI,CAACC,iBAAiB,CAAC,CAAC;EAC1B;EAEQA,iBAAiBA,CAAA,EAAG;IAC1B;IACA,IAAI,CAACT,GAAG,CAACU,YAAY,CAACC,OAAO,CAACC,GAAG,CAC9BC,MAAM,IAAK;MACV;MACA,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;MAChD,IAAIF,KAAK,EAAE;QACTD,MAAM,CAACL,OAAO,CAACS,aAAa,GAAG,UAAUH,KAAK,EAAE;MAClD;MACA,OAAOD,MAAM;IACf,CAAC,EACAK,KAAK,IAAK;MACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;IAC9B,CACF,CAAC;;IAED;IACA,IAAI,CAAClB,GAAG,CAACU,YAAY,CAACW,QAAQ,CAACT,GAAG,CAC/BS,QAAoC,IAAK;MACxC,OAAOA,QAAQ;IACjB,CAAC,EACAH,KAAK,IAAK;MAAA,IAAAI,eAAA;MACT,IAAI,EAAAA,eAAA,GAAAJ,KAAK,CAACG,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;QAClC;QACAR,YAAY,CAACS,UAAU,CAAC,YAAY,CAAC;QACrC;MACF;MACA,OAAOL,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;IAC9B,CACF,CAAC;EACH;;EAEA;EACA,MAAcP,OAAOA,CAAIc,MAAc,EAAEC,GAAW,EAAEC,IAAU,EAAc;IAC5E,IAAI;MACF,MAAMN,QAAQ,GAAG,MAAM,IAAI,CAACrB,GAAG,CAACW,OAAO,CAAiB;QACtDc,MAAM;QACNC,GAAG;QACHC;MACF,CAAC,CAAC;MAEF,IAAIN,QAAQ,CAACM,IAAI,CAACC,OAAO,EAAE;QACzB,OAAOP,QAAQ,CAACM,IAAI,CAACA,IAAI;MAC3B,CAAC,MAAM;QACL,MAAM,IAAIE,KAAK,CAACR,QAAQ,CAACM,IAAI,CAACG,OAAO,IAAI,MAAM,CAAC;MAClD;IACF,CAAC,CAAC,OAAOZ,KAAU,EAAE;MACnBa,OAAO,CAACb,KAAK,CAAC,YAAYO,MAAM,IAAIC,GAAG,IAAI,EAAER,KAAK,CAAC;MACnD,MAAMA,KAAK;IACb;EACF;;EAEA;EACA,MAAMc,GAAGA,CAAIN,GAAW,EAAc;IACpC,OAAO,IAAI,CAACf,OAAO,CAAI,KAAK,EAAEe,GAAG,CAAC;EACpC;;EAEA;EACA,MAAMO,IAAIA,CAAIP,GAAW,EAAEC,IAAU,EAAc;IACjD,OAAO,IAAI,CAAChB,OAAO,CAAI,MAAM,EAAEe,GAAG,EAAEC,IAAI,CAAC;EAC3C;;EAEA;EACA,MAAMO,GAAGA,CAAIR,GAAW,EAAEC,IAAU,EAAc;IAChD,OAAO,IAAI,CAAChB,OAAO,CAAI,KAAK,EAAEe,GAAG,EAAEC,IAAI,CAAC;EAC1C;;EAEA;EACA,MAAMQ,MAAMA,CAAIT,GAAW,EAAc;IACvC,OAAO,IAAI,CAACf,OAAO,CAAI,QAAQ,EAAEe,GAAG,CAAC;EACvC;;EAEA;EACA,MAAMU,gBAAgBA,CAAA,EAAG;IACvB,OAAO,IAAI,CAACJ,GAAG,CAAC,kBAAkB,CAAC;EACrC;EAEA,MAAMK,iBAAiBA,CAACC,IAAI,GAAG,CAAC,EAAEC,KAAK,GAAG,EAAE,EAAE;IAC5C,OAAO,IAAI,CAACP,GAAG,CAAyB,yBAAyBM,IAAI,UAAUC,KAAK,EAAE,CAAC;EACzF;EAEA,MAAMC,gBAAgBA,CAACC,YAAoB,EAAE;IAC3C,OAAO,IAAI,CAACT,GAAG,CAAC,oBAAoBS,YAAY,EAAE,CAAC;EACrD;EAEA,MAAMC,gBAAgBA,CAACC,KAAK,GAAG,CAAC,EAAE;IAChC,OAAO,IAAI,CAACX,GAAG,CAAC,mBAAmBW,KAAK,EAAE,CAAC;EAC7C;EAEA,MAAMC,iBAAiBA,CAACrB,MAAe,EAAEe,IAAI,GAAG,CAAC,EAAEC,KAAK,GAAG,EAAE,EAAE;IAC7D,MAAMM,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;IACpC,IAAIvB,MAAM,EAAEsB,MAAM,CAACE,MAAM,CAAC,QAAQ,EAAExB,MAAM,CAAC;IAC3CsB,MAAM,CAACE,MAAM,CAAC,MAAM,EAAET,IAAI,CAACU,QAAQ,CAAC,CAAC,CAAC;IACtCH,MAAM,CAACE,MAAM,CAAC,OAAO,EAAER,KAAK,CAACS,QAAQ,CAAC,CAAC,CAAC;IAExC,OAAO,IAAI,CAAChB,GAAG,CAAyB,oBAAoBa,MAAM,EAAE,CAAC;EACvE;EAEA,MAAMI,eAAeA,CAAA,EAAG;IACtB,OAAO,IAAI,CAACjB,GAAG,CAAC,gBAAgB,CAAC;EACnC;;EAEA;EACA,MAAMkB,qBAAqBA,CAAA,EAAG;IAC5B,OAAO,IAAI,CAAClB,GAAG,CAAC,sBAAsB,CAAC;EACzC;EAEA,MAAMmB,oBAAoBA,CAACC,IAAY,EAAEC,QAAiB,EAAEd,KAAK,GAAG,EAAE,EAAE;IACtE,MAAMM,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;IACpC,IAAIO,QAAQ,EAAER,MAAM,CAACE,MAAM,CAAC,UAAU,EAAEM,QAAQ,CAACL,QAAQ,CAAC,CAAC,CAAC;IAC5DH,MAAM,CAACE,MAAM,CAAC,OAAO,EAAER,KAAK,CAACS,QAAQ,CAAC,CAAC,CAAC;IAExC,OAAO,IAAI,CAAChB,GAAG,CAAC,qBAAqBoB,IAAI,IAAIP,MAAM,EAAE,CAAC;EACxD;EAEA,MAAMS,sBAAsBA,CAACb,YAAoB,EAAE;IACjD,OAAO,IAAI,CAACT,GAAG,CAAC,uBAAuBS,YAAY,EAAE,CAAC;EACxD;EAEA,MAAMc,kBAAkBA,CAAA,EAAG;IACzB,OAAO,IAAI,CAACvB,GAAG,CAAC,oBAAoB,CAAC;EACvC;EAEA,MAAMwB,qBAAqBA,CAACJ,IAAa,EAAE;IACzC,MAAMP,MAAM,GAAGO,IAAI,GAAG,SAASA,IAAI,EAAE,GAAG,EAAE;IAC1C,OAAO,IAAI,CAACpB,GAAG,CAAC,wBAAwBa,MAAM,EAAE,CAAC;EACnD;;EAEA;EACA,MAAMY,eAAeA,CAAA,EAAG;IACtB,OAAO,IAAI,CAACzB,GAAG,CAAC,gBAAgB,CAAC;EACnC;EAEA,MAAM0B,cAAcA,CAAA,EAAG;IACrB,OAAO,IAAI,CAAC1B,GAAG,CAAC,eAAe,CAAC;EAClC;EAEA,MAAM2B,gBAAgBA,CAAA,EAAG;IACvB,OAAO,IAAI,CAAC3B,GAAG,CAAC,iBAAiB,CAAC;EACpC;EAEA,MAAM4B,eAAeA,CAAA,EAAG;IACtB,OAAO,IAAI,CAAC5B,GAAG,CAAC,gBAAgB,CAAC;EACnC;EAEA,MAAM6B,aAAaA,CAAA,EAAG;IACpB,OAAO,IAAI,CAAC7B,GAAG,CAAC,aAAa,CAAC;EAChC;EAEA,MAAM8B,iBAAiBA,CAACC,MAAc,EAAE;IACtC,OAAO,IAAI,CAAC/B,GAAG,CAAC,yBAAyB+B,MAAM,EAAE,CAAC;EACpD;EAEA,MAAMC,kBAAkBA,CAACC,UAAkB,EAAE;IAC3C,OAAO,IAAI,CAACjC,GAAG,CAAC,kBAAkBiC,UAAU,UAAU,CAAC;EACzD;;EAEA;EACA,MAAMC,KAAKA,CAACC,QAAgB,EAAEC,QAAgB,EAAE;IAC9C,OAAO,IAAI,CAACnC,IAAI,CAAC,aAAa,EAAE;MAAEkC,QAAQ;MAAEC;IAAS,CAAC,CAAC;EACzD;EAEA,MAAMC,QAAQA,CAACF,QAAgB,EAAEG,KAAa,EAAEF,QAAgB,EAAE;IAChE,OAAO,IAAI,CAACnC,IAAI,CAAC,gBAAgB,EAAE;MAAEkC,QAAQ;MAAEG,KAAK;MAAEF;IAAS,CAAC,CAAC;EACnE;EAEA,MAAMG,WAAWA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACvC,GAAG,CAAC,cAAc,CAAC;EACjC;EAEA,MAAMwC,cAAcA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACxC,GAAG,CAAC,eAAe,CAAC;EAClC;EAEA,MAAMyC,aAAaA,CAAC9C,IAAS,EAAE;IAC7B,OAAO,IAAI,CAACO,GAAG,CAAC,eAAe,EAAEP,IAAI,CAAC;EACxC;EAEA,MAAM+C,cAAcA,CAACC,eAAuB,EAAEC,WAAmB,EAAE;IACjE,OAAO,IAAI,CAAC1C,GAAG,CAAC,gBAAgB,EAAE;MAAEyC,eAAe;MAAEC;IAAY,CAAC,CAAC;EACrE;;EAEA;EACA,MAAMC,WAAWA,CAAA,EAAG;IAClB,IAAI;MACF,IAAI,CAAC5E,OAAO,GAAG,MAAM,IAAI,CAAC4D,aAAa,CAAC,CAAC;MACzC,OAAO,IAAI,CAAC5D,OAAO;IACrB,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACda,OAAO,CAACb,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B,MAAMA,KAAK;IACb;EACF;;EAEA;EACA4D,UAAUA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC7E,OAAO;EACrB;;EAEA;EACA8E,0BAA0BA,CAAChB,MAAc,EAAiB;IACxD,MAAMiB,OAAO,GAAG,IAAI,CAAC/E,OAAO,CAAC+E,OAAO,IAAI,EAAE;IAC1C,KAAK,MAAMC,MAAM,IAAID,OAAO,EAAE;MAC5B,IAAIC,MAAM,CAACC,OAAO,CAACC,QAAQ,CAACpB,MAAM,CAAC,EAAE;QACnC,OAAOkB,MAAM,CAACG,IAAI;MACpB;IACF;IACA,OAAO,IAAI;EACb;;EAEA;EACAC,yBAAyBA,CAACtB,MAAc,EAAiB;IACvD,MAAMuB,MAAM,GAAG,IAAI,CAACrF,OAAO,CAACqF,MAAM,IAAI,EAAE;IACxC,KAAK,MAAMC,KAAK,IAAID,MAAM,EAAE;MAC1B,IAAIC,KAAK,CAACL,OAAO,CAACC,QAAQ,CAACpB,MAAM,CAAC,EAAE;QAClC,OAAOwB,KAAK,CAACH,IAAI;MACnB;IACF;IACA,OAAO,IAAI;EACb;;EAEA;EACAI,2BAA2BA,CAACzB,MAAc,EAAiB;IACzD,MAAM0B,QAAQ,GAAG,IAAI,CAACxF,OAAO,CAACwF,QAAQ,IAAI,EAAE;IAC5C,KAAK,MAAMC,OAAO,IAAID,QAAQ,EAAE;MAC9B,IAAIC,OAAO,CAACR,OAAO,CAACC,QAAQ,CAACpB,MAAM,CAAC,EAAE;QACpC,OAAO2B,OAAO,CAACN,IAAI;MACrB;IACF;IACA,OAAO,IAAI;EACb;AACF;;AAEA;AACA,OAAO,MAAMO,UAAU,GAAG,IAAI7F,UAAU,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}