{"ast": null, "code": "import { XHR } from \"./polling-xhr.node.js\";\nimport { WS } from \"./websocket.node.js\";\nimport { WT } from \"./webtransport.js\";\nexport const transports = {\n  websocket: WS,\n  webtransport: WT,\n  polling: XHR\n};", "map": {"version": 3, "names": ["XHR", "WS", "WT", "transports", "websocket", "webtransport", "polling"], "sources": ["D:/liu/html/frontend/node_modules/engine.io-client/build/esm/transports/index.js"], "sourcesContent": ["import { XHR } from \"./polling-xhr.node.js\";\nimport { WS } from \"./websocket.node.js\";\nimport { WT } from \"./webtransport.js\";\nexport const transports = {\n    websocket: WS,\n    webtransport: WT,\n    polling: XHR,\n};\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,uBAAuB;AAC3C,SAASC,EAAE,QAAQ,qBAAqB;AACxC,SAASC,EAAE,QAAQ,mBAAmB;AACtC,OAAO,MAAMC,UAAU,GAAG;EACtBC,SAAS,EAAEH,EAAE;EACbI,YAAY,EAAEH,EAAE;EAChBI,OAAO,EAAEN;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}