{"ast": null, "code": "var _jsxFileName = \"D:\\\\liu\\\\html\\\\frontend\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport styled from 'styled-components';\n\n// 导入组件\nimport Header from './components/Header';\nimport LotterySelector from './components/LotterySelector';\nimport CountdownDisplay from './components/CountdownDisplay';\nimport LatestResults from './components/LatestResults';\nimport PredictionSection from './components/PredictionSection';\nimport ZodiacTable from './components/ZodiacTable';\n\n// 导入服务\nimport { socketService } from './services/socketService';\nimport { apiService } from './services/apiService';\n\n// 主容器样式\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AppContainer = styled.div`\n  min-height: 100vh;\n  background: #f0f2f5;\n  font-family: 'Microsoft YaHei', Arial, sans-serif;\n`;\n\n// 内容容器\n_c = AppContainer;\nconst ContentContainer = styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 16px;\n`;\n\n// 主应用组件\n_c2 = ContentContainer;\nconst App = () => {\n  _s();\n  const [loading, setLoading] = useState(true);\n  const [currentPeriod, setCurrentPeriod] = useState(null);\n  const [latestResults, setLatestResults] = useState(null);\n  const [onlineUsers, setOnlineUsers] = useState(0);\n  useEffect(() => {\n    initializeApp();\n    setupSocketListeners();\n    return () => {\n      socketService.disconnect();\n    };\n  }, []);\n  const initializeApp = async () => {\n    try {\n      // 连接Socket.io\n      socketService.connect();\n\n      // 加载初始数据\n      const [currentData, resultsData] = await Promise.all([apiService.getCurrentPeriod(), apiService.getLatestResults()]);\n      setCurrentPeriod(currentData);\n      setLatestResults(resultsData);\n      setLoading(false);\n    } catch (error) {\n      console.error('初始化应用失败:', error);\n      setLoading(false);\n    }\n  };\n  const setupSocketListeners = () => {\n    socketService.on('userCount', count => {\n      setOnlineUsers(count);\n    });\n    socketService.on('newResult', result => {\n      setLatestResults(result);\n    });\n    socketService.on('periodUpdate', period => {\n      setCurrentPeriod(period);\n    });\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(AppContainer, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          height: '100vh',\n          fontSize: '18px'\n        },\n        children: \"\\u52A0\\u8F7D\\u4E2D...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(AppContainer, {\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      onlineUsers: onlineUsers\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ContentContainer, {\n      children: [/*#__PURE__*/_jsxDEV(LotterySelector, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CountdownDisplay, {\n        currentPeriod: currentPeriod\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(LatestResults, {\n        results: latestResults\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(PredictionSection, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ZodiacTable, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 97,\n    columnNumber: 5\n  }, this);\n};\n_s(App, \"dIAbcKcXYox9z+DhKasVq+XWx48=\");\n_c3 = App;\nexport default App;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"AppContainer\");\n$RefreshReg$(_c2, \"ContentContainer\");\n$RefreshReg$(_c3, \"App\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "styled", "Header", "LotterySelector", "CountdownDisplay", "LatestResults", "PredictionSection", "ZodiacTable", "socketService", "apiService", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "_c", "ContentContainer", "_c2", "App", "_s", "loading", "setLoading", "currentPeriod", "setCurrentPeriod", "latestResults", "setLatestResults", "onlineUsers", "setOnlineUsers", "initializeApp", "setupSocketListeners", "disconnect", "connect", "currentData", "resultsData", "Promise", "all", "getCurrentPeriod", "getLatestResults", "error", "console", "on", "count", "result", "period", "children", "style", "display", "justifyContent", "alignItems", "height", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "results", "_c3", "$RefreshReg$"], "sources": ["D:/liu/html/frontend/src/App.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport styled from 'styled-components';\n\n// 导入组件\nimport Header from './components/Header';\nimport LotterySelector from './components/LotterySelector';\nimport CountdownDisplay from './components/CountdownDisplay';\nimport LatestResults from './components/LatestResults';\nimport PredictionSection from './components/PredictionSection';\nimport ZodiacTable from './components/ZodiacTable';\n\n// 导入服务\nimport { socketService } from './services/socketService';\nimport { apiService } from './services/apiService';\n\n// 主容器样式\nconst AppContainer = styled.div`\n  min-height: 100vh;\n  background: #f0f2f5;\n  font-family: 'Microsoft YaHei', Arial, sans-serif;\n`;\n\n// 内容容器\nconst ContentContainer = styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 16px;\n`;\n\n// 主应用组件\nconst App: React.FC = () => {\n  const [loading, setLoading] = useState(true);\n  const [currentPeriod, setCurrentPeriod] = useState<any>(null);\n  const [latestResults, setLatestResults] = useState<any>(null);\n  const [onlineUsers, setOnlineUsers] = useState(0);\n\n  useEffect(() => {\n    initializeApp();\n    setupSocketListeners();\n    \n    return () => {\n      socketService.disconnect();\n    };\n  }, []);\n\n  const initializeApp = async () => {\n    try {\n      // 连接Socket.io\n      socketService.connect();\n      \n      // 加载初始数据\n      const [currentData, resultsData] = await Promise.all([\n        apiService.getCurrentPeriod(),\n        apiService.getLatestResults()\n      ]);\n      \n      setCurrentPeriod(currentData);\n      setLatestResults(resultsData);\n      setLoading(false);\n    } catch (error) {\n      console.error('初始化应用失败:', error);\n      setLoading(false);\n    }\n  };\n\n  const setupSocketListeners = () => {\n    socketService.on('userCount', (count: number) => {\n      setOnlineUsers(count);\n    });\n\n    socketService.on('newResult', (result: any) => {\n      setLatestResults(result);\n    });\n\n    socketService.on('periodUpdate', (period: any) => {\n      setCurrentPeriod(period);\n    });\n  };\n\n  if (loading) {\n    return (\n      <AppContainer>\n        <div style={{ \n          display: 'flex', \n          justifyContent: 'center', \n          alignItems: 'center', \n          height: '100vh',\n          fontSize: '18px'\n        }}>\n          加载中...\n        </div>\n      </AppContainer>\n    );\n  }\n\n  return (\n    <AppContainer>\n      <Header onlineUsers={onlineUsers} />\n      \n      <ContentContainer>\n        <LotterySelector />\n        <CountdownDisplay currentPeriod={currentPeriod} />\n        <LatestResults results={latestResults} />\n        <PredictionSection />\n        <ZodiacTable />\n      </ContentContainer>\n    </AppContainer>\n  );\n};\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,mBAAmB;;AAEtC;AACA,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,iBAAiB,MAAM,gCAAgC;AAC9D,OAAOC,WAAW,MAAM,0BAA0B;;AAElD;AACA,SAASC,aAAa,QAAQ,0BAA0B;AACxD,SAASC,UAAU,QAAQ,uBAAuB;;AAElD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,YAAY,GAAGX,MAAM,CAACY,GAAG;AAC/B;AACA;AACA;AACA,CAAC;;AAED;AAAAC,EAAA,GANMF,YAAY;AAOlB,MAAMG,gBAAgB,GAAGd,MAAM,CAACY,GAAG;AACnC;AACA;AACA;AACA,CAAC;;AAED;AAAAG,GAAA,GANMD,gBAAgB;AAOtB,MAAME,GAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACqB,aAAa,EAAEC,gBAAgB,CAAC,GAAGtB,QAAQ,CAAM,IAAI,CAAC;EAC7D,MAAM,CAACuB,aAAa,EAAEC,gBAAgB,CAAC,GAAGxB,QAAQ,CAAM,IAAI,CAAC;EAC7D,MAAM,CAACyB,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,CAAC,CAAC;EAEjDD,SAAS,CAAC,MAAM;IACd4B,aAAa,CAAC,CAAC;IACfC,oBAAoB,CAAC,CAAC;IAEtB,OAAO,MAAM;MACXpB,aAAa,CAACqB,UAAU,CAAC,CAAC;IAC5B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMF,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF;MACAnB,aAAa,CAACsB,OAAO,CAAC,CAAC;;MAEvB;MACA,MAAM,CAACC,WAAW,EAAEC,WAAW,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACnDzB,UAAU,CAAC0B,gBAAgB,CAAC,CAAC,EAC7B1B,UAAU,CAAC2B,gBAAgB,CAAC,CAAC,CAC9B,CAAC;MAEFd,gBAAgB,CAACS,WAAW,CAAC;MAC7BP,gBAAgB,CAACQ,WAAW,CAAC;MAC7BZ,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAChCjB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMQ,oBAAoB,GAAGA,CAAA,KAAM;IACjCpB,aAAa,CAAC+B,EAAE,CAAC,WAAW,EAAGC,KAAa,IAAK;MAC/Cd,cAAc,CAACc,KAAK,CAAC;IACvB,CAAC,CAAC;IAEFhC,aAAa,CAAC+B,EAAE,CAAC,WAAW,EAAGE,MAAW,IAAK;MAC7CjB,gBAAgB,CAACiB,MAAM,CAAC;IAC1B,CAAC,CAAC;IAEFjC,aAAa,CAAC+B,EAAE,CAAC,cAAc,EAAGG,MAAW,IAAK;MAChDpB,gBAAgB,CAACoB,MAAM,CAAC;IAC1B,CAAC,CAAC;EACJ,CAAC;EAED,IAAIvB,OAAO,EAAE;IACX,oBACER,OAAA,CAACC,YAAY;MAAA+B,QAAA,eACXhC,OAAA;QAAKiC,KAAK,EAAE;UACVC,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE,QAAQ;UACxBC,UAAU,EAAE,QAAQ;UACpBC,MAAM,EAAE,OAAO;UACfC,QAAQ,EAAE;QACZ,CAAE;QAAAN,QAAA,EAAC;MAEH;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC;EAEnB;EAEA,oBACE1C,OAAA,CAACC,YAAY;IAAA+B,QAAA,gBACXhC,OAAA,CAACT,MAAM;MAACuB,WAAW,EAAEA;IAAY;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEpC1C,OAAA,CAACI,gBAAgB;MAAA4B,QAAA,gBACfhC,OAAA,CAACR,eAAe;QAAA+C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnB1C,OAAA,CAACP,gBAAgB;QAACiB,aAAa,EAAEA;MAAc;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAClD1C,OAAA,CAACN,aAAa;QAACiD,OAAO,EAAE/B;MAAc;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACzC1C,OAAA,CAACL,iBAAiB;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrB1C,OAAA,CAACJ,WAAW;QAAA2C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEnB,CAAC;AAACnC,EAAA,CA9EID,GAAa;AAAAsC,GAAA,GAAbtC,GAAa;AAgFnB,eAAeA,GAAG;AAAC,IAAAH,EAAA,EAAAE,GAAA,EAAAuC,GAAA;AAAAC,YAAA,CAAA1C,EAAA;AAAA0C,YAAA,CAAAxC,GAAA;AAAAwC,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}