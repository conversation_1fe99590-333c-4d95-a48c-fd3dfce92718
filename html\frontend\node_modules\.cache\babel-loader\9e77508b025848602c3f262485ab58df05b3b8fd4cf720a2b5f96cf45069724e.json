{"ast": null, "code": "var _jsxFileName = \"D:\\\\liu\\\\html\\\\frontend\\\\src\\\\pages\\\\SettingsPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Typography, Spin, message, Descriptions } from 'antd';\nimport { SettingOutlined } from '@ant-design/icons';\nimport { apiService } from '../services/apiService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst SettingsPage = () => {\n  _s();\n  const [loading, setLoading] = useState(true);\n  const [configs, setConfigs] = useState(null);\n  useEffect(() => {\n    loadConfigs();\n  }, []);\n  const loadConfigs = async () => {\n    try {\n      const configData = await apiService.getSystemConfig();\n      setConfigs(configData);\n      setLoading(false);\n    } catch (error) {\n      console.error('加载配置失败:', error);\n      message.error('加载配置失败');\n      setLoading(false);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-container\",\n      children: /*#__PURE__*/_jsxDEV(Spin, {\n        size: \"large\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      children: [/*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this), \" \\u7CFB\\u7EDF\\u8BBE\\u7F6E\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Text, {\n      type: \"secondary\",\n      children: \"\\u7CFB\\u7EDF\\u914D\\u7F6E\\u4FE1\\u606F\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u7CFB\\u7EDF\\u914D\\u7F6E\",\n      style: {\n        marginTop: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Descriptions, {\n        column: 1,\n        children: configs && Object.entries(configs).map(([key, value]) => /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: key,\n          children: typeof value === 'object' ? JSON.stringify(value) : String(value)\n        }, key, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this), !configs && /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        children: \"\\u6682\\u65E0\\u914D\\u7F6E\\u6570\\u636E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 37,\n    columnNumber: 5\n  }, this);\n};\n_s(SettingsPage, \"BKzsXbnphSIi3uJLRtW4hiwGebo=\");\n_c = SettingsPage;\nexport default SettingsPage;\nvar _c;\n$RefreshReg$(_c, \"SettingsPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Typography", "Spin", "message", "Descriptions", "SettingOutlined", "apiService", "jsxDEV", "_jsxDEV", "Title", "Text", "SettingsPage", "_s", "loading", "setLoading", "configs", "setConfigs", "loadConfigs", "configData", "getSystemConfig", "error", "console", "className", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "level", "type", "title", "style", "marginTop", "column", "Object", "entries", "map", "key", "value", "<PERSON><PERSON>", "label", "JSON", "stringify", "String", "_c", "$RefreshReg$"], "sources": ["D:/liu/html/frontend/src/pages/SettingsPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Card, Typography, Spin, message, Descriptions } from 'antd';\nimport { SettingOutlined } from '@ant-design/icons';\nimport { apiService } from '../services/apiService';\n\nconst { Title, Text } = Typography;\n\nconst SettingsPage: React.FC = () => {\n  const [loading, setLoading] = useState(true);\n  const [configs, setConfigs] = useState<any>(null);\n\n  useEffect(() => {\n    loadConfigs();\n  }, []);\n\n  const loadConfigs = async () => {\n    try {\n      const configData = await apiService.getSystemConfig();\n      setConfigs(configData);\n      setLoading(false);\n    } catch (error) {\n      console.error('加载配置失败:', error);\n      message.error('加载配置失败');\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"loading-container\">\n        <Spin size=\"large\" />\n      </div>\n    );\n  }\n\n  return (\n    <div>\n      <Title level={2}>\n        <SettingOutlined /> 系统设置\n      </Title>\n      <Text type=\"secondary\">系统配置信息</Text>\n      \n      <Card title=\"系统配置\" style={{ marginTop: 24 }}>\n        <Descriptions column={1}>\n          {configs && Object.entries(configs).map(([key, value]: [string, any]) => (\n            <Descriptions.Item key={key} label={key}>\n              {typeof value === 'object' ? JSON.stringify(value) : String(value)}\n            </Descriptions.Item>\n          ))}\n        </Descriptions>\n        \n        {!configs && (\n          <Text type=\"secondary\">暂无配置数据</Text>\n        )}\n      </Card>\n    </div>\n  );\n};\n\nexport default SettingsPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,UAAU,EAAEC,IAAI,EAAEC,OAAO,EAAEC,YAAY,QAAQ,MAAM;AACpE,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,UAAU,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGT,UAAU;AAElC,MAAMU,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAM,IAAI,CAAC;EAEjDC,SAAS,CAAC,MAAM;IACdkB,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACF,MAAMC,UAAU,GAAG,MAAMZ,UAAU,CAACa,eAAe,CAAC,CAAC;MACrDH,UAAU,CAACE,UAAU,CAAC;MACtBJ,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BjB,OAAO,CAACiB,KAAK,CAAC,QAAQ,CAAC;MACvBN,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAID,OAAO,EAAE;IACX,oBACEL,OAAA;MAAKc,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChCf,OAAA,CAACN,IAAI;QAACsB,IAAI,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC;EAEV;EAEA,oBACEpB,OAAA;IAAAe,QAAA,gBACEf,OAAA,CAACC,KAAK;MAACoB,KAAK,EAAE,CAAE;MAAAN,QAAA,gBACdf,OAAA,CAACH,eAAe;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,6BACrB;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eACRpB,OAAA,CAACE,IAAI;MAACoB,IAAI,EAAC,WAAW;MAAAP,QAAA,EAAC;IAAM;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAEpCpB,OAAA,CAACR,IAAI;MAAC+B,KAAK,EAAC,0BAAM;MAACC,KAAK,EAAE;QAAEC,SAAS,EAAE;MAAG,CAAE;MAAAV,QAAA,gBAC1Cf,OAAA,CAACJ,YAAY;QAAC8B,MAAM,EAAE,CAAE;QAAAX,QAAA,EACrBR,OAAO,IAAIoB,MAAM,CAACC,OAAO,CAACrB,OAAO,CAAC,CAACsB,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAgB,kBAClE/B,OAAA,CAACJ,YAAY,CAACoC,IAAI;UAAWC,KAAK,EAAEH,GAAI;UAAAf,QAAA,EACrC,OAAOgB,KAAK,KAAK,QAAQ,GAAGG,IAAI,CAACC,SAAS,CAACJ,KAAK,CAAC,GAAGK,MAAM,CAACL,KAAK;QAAC,GAD5CD,GAAG;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAER,CACpB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU,CAAC,EAEd,CAACb,OAAO,iBACPP,OAAA,CAACE,IAAI;QAACoB,IAAI,EAAC,WAAW;QAAAP,QAAA,EAAC;MAAM;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CACpC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAChB,EAAA,CAlDID,YAAsB;AAAAkC,EAAA,GAAtBlC,YAAsB;AAoD5B,eAAeA,YAAY;AAAC,IAAAkC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}