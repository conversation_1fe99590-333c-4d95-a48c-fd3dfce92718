{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { getNumberPrecision, isEmpty, num2str } from \"./numberUtil\";\n\n/**\n * We can remove this when IE not support anymore\n */\nvar NumberDecimal = /*#__PURE__*/function () {\n  function NumberDecimal(value) {\n    _classCallCheck(this, NumberDecimal);\n    _defineProperty(this, \"origin\", '');\n    _defineProperty(this, \"number\", void 0);\n    _defineProperty(this, \"empty\", void 0);\n    if (isEmpty(value)) {\n      this.empty = true;\n      return;\n    }\n    this.origin = String(value);\n    this.number = Number(value);\n  }\n  _createClass(NumberDecimal, [{\n    key: \"negate\",\n    value: function negate() {\n      return new NumberDecimal(-this.toNumber());\n    }\n  }, {\n    key: \"add\",\n    value: function add(value) {\n      if (this.isInvalidate()) {\n        return new NumberDecimal(value);\n      }\n      var target = Number(value);\n      if (Number.isNaN(target)) {\n        return this;\n      }\n      var number = this.number + target;\n\n      // [Legacy] Back to safe integer\n      if (number > Number.MAX_SAFE_INTEGER) {\n        return new NumberDecimal(Number.MAX_SAFE_INTEGER);\n      }\n      if (number < Number.MIN_SAFE_INTEGER) {\n        return new NumberDecimal(Number.MIN_SAFE_INTEGER);\n      }\n      var maxPrecision = Math.max(getNumberPrecision(this.number), getNumberPrecision(target));\n      return new NumberDecimal(number.toFixed(maxPrecision));\n    }\n  }, {\n    key: \"multi\",\n    value: function multi(value) {\n      var target = Number(value);\n      if (this.isInvalidate() || Number.isNaN(target)) {\n        return new NumberDecimal(NaN);\n      }\n      var number = this.number * target;\n\n      // [Legacy] Back to safe integer\n      if (number > Number.MAX_SAFE_INTEGER) {\n        return new NumberDecimal(Number.MAX_SAFE_INTEGER);\n      }\n      if (number < Number.MIN_SAFE_INTEGER) {\n        return new NumberDecimal(Number.MIN_SAFE_INTEGER);\n      }\n      var maxPrecision = Math.max(getNumberPrecision(this.number), getNumberPrecision(target));\n      return new NumberDecimal(number.toFixed(maxPrecision));\n    }\n  }, {\n    key: \"isEmpty\",\n    value: function isEmpty() {\n      return this.empty;\n    }\n  }, {\n    key: \"isNaN\",\n    value: function isNaN() {\n      return Number.isNaN(this.number);\n    }\n  }, {\n    key: \"isInvalidate\",\n    value: function isInvalidate() {\n      return this.isEmpty() || this.isNaN();\n    }\n  }, {\n    key: \"equals\",\n    value: function equals(target) {\n      return this.toNumber() === (target === null || target === void 0 ? void 0 : target.toNumber());\n    }\n  }, {\n    key: \"lessEquals\",\n    value: function lessEquals(target) {\n      return this.add(target.negate().toString()).toNumber() <= 0;\n    }\n  }, {\n    key: \"toNumber\",\n    value: function toNumber() {\n      return this.number;\n    }\n  }, {\n    key: \"toString\",\n    value: function toString() {\n      var safe = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n      if (!safe) {\n        return this.origin;\n      }\n      if (this.isInvalidate()) {\n        return '';\n      }\n      return num2str(this.number);\n    }\n  }]);\n  return NumberDecimal;\n}();\nexport { NumberDecimal as default };", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "_defineProperty", "getNumberPrecision", "isEmpty", "num2str", "NumberDecimal", "value", "empty", "origin", "String", "number", "Number", "key", "negate", "toNumber", "add", "isInvalidate", "target", "isNaN", "MAX_SAFE_INTEGER", "MIN_SAFE_INTEGER", "maxPrecision", "Math", "max", "toFixed", "multi", "NaN", "equals", "lessEquals", "toString", "safe", "arguments", "length", "undefined", "default"], "sources": ["D:/liu/html/frontend/node_modules/@rc-component/mini-decimal/es/NumberDecimal.js"], "sourcesContent": ["import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { getNumberPrecision, isEmpty, num2str } from \"./numberUtil\";\n\n/**\n * We can remove this when IE not support anymore\n */\nvar NumberDecimal = /*#__PURE__*/function () {\n  function NumberDecimal(value) {\n    _classCallCheck(this, NumberDecimal);\n    _defineProperty(this, \"origin\", '');\n    _defineProperty(this, \"number\", void 0);\n    _defineProperty(this, \"empty\", void 0);\n    if (isEmpty(value)) {\n      this.empty = true;\n      return;\n    }\n    this.origin = String(value);\n    this.number = Number(value);\n  }\n  _createClass(NumberDecimal, [{\n    key: \"negate\",\n    value: function negate() {\n      return new NumberDecimal(-this.toNumber());\n    }\n  }, {\n    key: \"add\",\n    value: function add(value) {\n      if (this.isInvalidate()) {\n        return new NumberDecimal(value);\n      }\n      var target = Number(value);\n      if (Number.isNaN(target)) {\n        return this;\n      }\n      var number = this.number + target;\n\n      // [Legacy] Back to safe integer\n      if (number > Number.MAX_SAFE_INTEGER) {\n        return new NumberDecimal(Number.MAX_SAFE_INTEGER);\n      }\n      if (number < Number.MIN_SAFE_INTEGER) {\n        return new NumberDecimal(Number.MIN_SAFE_INTEGER);\n      }\n      var maxPrecision = Math.max(getNumberPrecision(this.number), getNumberPrecision(target));\n      return new NumberDecimal(number.toFixed(maxPrecision));\n    }\n  }, {\n    key: \"multi\",\n    value: function multi(value) {\n      var target = Number(value);\n      if (this.isInvalidate() || Number.isNaN(target)) {\n        return new NumberDecimal(NaN);\n      }\n      var number = this.number * target;\n\n      // [Legacy] Back to safe integer\n      if (number > Number.MAX_SAFE_INTEGER) {\n        return new NumberDecimal(Number.MAX_SAFE_INTEGER);\n      }\n      if (number < Number.MIN_SAFE_INTEGER) {\n        return new NumberDecimal(Number.MIN_SAFE_INTEGER);\n      }\n      var maxPrecision = Math.max(getNumberPrecision(this.number), getNumberPrecision(target));\n      return new NumberDecimal(number.toFixed(maxPrecision));\n    }\n  }, {\n    key: \"isEmpty\",\n    value: function isEmpty() {\n      return this.empty;\n    }\n  }, {\n    key: \"isNaN\",\n    value: function isNaN() {\n      return Number.isNaN(this.number);\n    }\n  }, {\n    key: \"isInvalidate\",\n    value: function isInvalidate() {\n      return this.isEmpty() || this.isNaN();\n    }\n  }, {\n    key: \"equals\",\n    value: function equals(target) {\n      return this.toNumber() === (target === null || target === void 0 ? void 0 : target.toNumber());\n    }\n  }, {\n    key: \"lessEquals\",\n    value: function lessEquals(target) {\n      return this.add(target.negate().toString()).toNumber() <= 0;\n    }\n  }, {\n    key: \"toNumber\",\n    value: function toNumber() {\n      return this.number;\n    }\n  }, {\n    key: \"toString\",\n    value: function toString() {\n      var safe = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n      if (!safe) {\n        return this.origin;\n      }\n      if (this.isInvalidate()) {\n        return '';\n      }\n      return num2str(this.number);\n    }\n  }]);\n  return NumberDecimal;\n}();\nexport { NumberDecimal as default };"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,SAASC,kBAAkB,EAAEC,OAAO,EAAEC,OAAO,QAAQ,cAAc;;AAEnE;AACA;AACA;AACA,IAAIC,aAAa,GAAG,aAAa,YAAY;EAC3C,SAASA,aAAaA,CAACC,KAAK,EAAE;IAC5BP,eAAe,CAAC,IAAI,EAAEM,aAAa,CAAC;IACpCJ,eAAe,CAAC,IAAI,EAAE,QAAQ,EAAE,EAAE,CAAC;IACnCA,eAAe,CAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;IACvCA,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;IACtC,IAAIE,OAAO,CAACG,KAAK,CAAC,EAAE;MAClB,IAAI,CAACC,KAAK,GAAG,IAAI;MACjB;IACF;IACA,IAAI,CAACC,MAAM,GAAGC,MAAM,CAACH,KAAK,CAAC;IAC3B,IAAI,CAACI,MAAM,GAAGC,MAAM,CAACL,KAAK,CAAC;EAC7B;EACAN,YAAY,CAACK,aAAa,EAAE,CAAC;IAC3BO,GAAG,EAAE,QAAQ;IACbN,KAAK,EAAE,SAASO,MAAMA,CAAA,EAAG;MACvB,OAAO,IAAIR,aAAa,CAAC,CAAC,IAAI,CAACS,QAAQ,CAAC,CAAC,CAAC;IAC5C;EACF,CAAC,EAAE;IACDF,GAAG,EAAE,KAAK;IACVN,KAAK,EAAE,SAASS,GAAGA,CAACT,KAAK,EAAE;MACzB,IAAI,IAAI,CAACU,YAAY,CAAC,CAAC,EAAE;QACvB,OAAO,IAAIX,aAAa,CAACC,KAAK,CAAC;MACjC;MACA,IAAIW,MAAM,GAAGN,MAAM,CAACL,KAAK,CAAC;MAC1B,IAAIK,MAAM,CAACO,KAAK,CAACD,MAAM,CAAC,EAAE;QACxB,OAAO,IAAI;MACb;MACA,IAAIP,MAAM,GAAG,IAAI,CAACA,MAAM,GAAGO,MAAM;;MAEjC;MACA,IAAIP,MAAM,GAAGC,MAAM,CAACQ,gBAAgB,EAAE;QACpC,OAAO,IAAId,aAAa,CAACM,MAAM,CAACQ,gBAAgB,CAAC;MACnD;MACA,IAAIT,MAAM,GAAGC,MAAM,CAACS,gBAAgB,EAAE;QACpC,OAAO,IAAIf,aAAa,CAACM,MAAM,CAACS,gBAAgB,CAAC;MACnD;MACA,IAAIC,YAAY,GAAGC,IAAI,CAACC,GAAG,CAACrB,kBAAkB,CAAC,IAAI,CAACQ,MAAM,CAAC,EAAER,kBAAkB,CAACe,MAAM,CAAC,CAAC;MACxF,OAAO,IAAIZ,aAAa,CAACK,MAAM,CAACc,OAAO,CAACH,YAAY,CAAC,CAAC;IACxD;EACF,CAAC,EAAE;IACDT,GAAG,EAAE,OAAO;IACZN,KAAK,EAAE,SAASmB,KAAKA,CAACnB,KAAK,EAAE;MAC3B,IAAIW,MAAM,GAAGN,MAAM,CAACL,KAAK,CAAC;MAC1B,IAAI,IAAI,CAACU,YAAY,CAAC,CAAC,IAAIL,MAAM,CAACO,KAAK,CAACD,MAAM,CAAC,EAAE;QAC/C,OAAO,IAAIZ,aAAa,CAACqB,GAAG,CAAC;MAC/B;MACA,IAAIhB,MAAM,GAAG,IAAI,CAACA,MAAM,GAAGO,MAAM;;MAEjC;MACA,IAAIP,MAAM,GAAGC,MAAM,CAACQ,gBAAgB,EAAE;QACpC,OAAO,IAAId,aAAa,CAACM,MAAM,CAACQ,gBAAgB,CAAC;MACnD;MACA,IAAIT,MAAM,GAAGC,MAAM,CAACS,gBAAgB,EAAE;QACpC,OAAO,IAAIf,aAAa,CAACM,MAAM,CAACS,gBAAgB,CAAC;MACnD;MACA,IAAIC,YAAY,GAAGC,IAAI,CAACC,GAAG,CAACrB,kBAAkB,CAAC,IAAI,CAACQ,MAAM,CAAC,EAAER,kBAAkB,CAACe,MAAM,CAAC,CAAC;MACxF,OAAO,IAAIZ,aAAa,CAACK,MAAM,CAACc,OAAO,CAACH,YAAY,CAAC,CAAC;IACxD;EACF,CAAC,EAAE;IACDT,GAAG,EAAE,SAAS;IACdN,KAAK,EAAE,SAASH,OAAOA,CAAA,EAAG;MACxB,OAAO,IAAI,CAACI,KAAK;IACnB;EACF,CAAC,EAAE;IACDK,GAAG,EAAE,OAAO;IACZN,KAAK,EAAE,SAASY,KAAKA,CAAA,EAAG;MACtB,OAAOP,MAAM,CAACO,KAAK,CAAC,IAAI,CAACR,MAAM,CAAC;IAClC;EACF,CAAC,EAAE;IACDE,GAAG,EAAE,cAAc;IACnBN,KAAK,EAAE,SAASU,YAAYA,CAAA,EAAG;MAC7B,OAAO,IAAI,CAACb,OAAO,CAAC,CAAC,IAAI,IAAI,CAACe,KAAK,CAAC,CAAC;IACvC;EACF,CAAC,EAAE;IACDN,GAAG,EAAE,QAAQ;IACbN,KAAK,EAAE,SAASqB,MAAMA,CAACV,MAAM,EAAE;MAC7B,OAAO,IAAI,CAACH,QAAQ,CAAC,CAAC,MAAMG,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACH,QAAQ,CAAC,CAAC,CAAC;IAChG;EACF,CAAC,EAAE;IACDF,GAAG,EAAE,YAAY;IACjBN,KAAK,EAAE,SAASsB,UAAUA,CAACX,MAAM,EAAE;MACjC,OAAO,IAAI,CAACF,GAAG,CAACE,MAAM,CAACJ,MAAM,CAAC,CAAC,CAACgB,QAAQ,CAAC,CAAC,CAAC,CAACf,QAAQ,CAAC,CAAC,IAAI,CAAC;IAC7D;EACF,CAAC,EAAE;IACDF,GAAG,EAAE,UAAU;IACfN,KAAK,EAAE,SAASQ,QAAQA,CAAA,EAAG;MACzB,OAAO,IAAI,CAACJ,MAAM;IACpB;EACF,CAAC,EAAE;IACDE,GAAG,EAAE,UAAU;IACfN,KAAK,EAAE,SAASuB,QAAQA,CAAA,EAAG;MACzB,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;MACnF,IAAI,CAACD,IAAI,EAAE;QACT,OAAO,IAAI,CAACtB,MAAM;MACpB;MACA,IAAI,IAAI,CAACQ,YAAY,CAAC,CAAC,EAAE;QACvB,OAAO,EAAE;MACX;MACA,OAAOZ,OAAO,CAAC,IAAI,CAACM,MAAM,CAAC;IAC7B;EACF,CAAC,CAAC,CAAC;EACH,OAAOL,aAAa;AACtB,CAAC,CAAC,CAAC;AACH,SAASA,aAAa,IAAI6B,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}