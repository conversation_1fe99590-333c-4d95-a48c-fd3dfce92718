{"ast": null, "code": "var _jsxFileName = \"D:\\\\liu\\\\html\\\\frontend\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { Layout, Menu, Spin, message } from 'antd';\nimport { HomeOutlined, BarChartOutlined, HistoryOutlined, SettingOutlined, TrophyOutlined, ThunderboltOutlined } from '@ant-design/icons';\nimport styled from 'styled-components';\nimport { BrowserRouter as Router, Routes, Route, Link, useLocation } from 'react-router-dom';\n\n// 导入页面组件\nimport HomePage from './pages/HomePage';\nimport PredictionPage from './pages/PredictionPage';\nimport HistoryPage from './pages/HistoryPage';\nimport StatisticsPage from './pages/StatisticsPage';\nimport SettingsPage from './pages/SettingsPage';\n\n// 导入服务\nimport { socketService } from './services/socketService';\nimport { apiService } from './services/apiService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Header,\n  Content,\n  Footer,\n  Sider\n} = Layout;\n\n// 样式组件\nconst StyledLayout = styled(Layout)`\n  min-height: 100vh;\n`;\n_c = StyledLayout;\nconst StyledHeader = styled(Header)`\n  display: flex;\n  align-items: center;\n  padding: 0 24px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n`;\n_c2 = StyledHeader;\nconst Logo = styled.div`\n  color: white;\n  font-size: 20px;\n  font-weight: bold;\n  margin-right: 24px;\n  display: flex;\n  align-items: center;\n  \n  .logo-icon {\n    margin-right: 8px;\n    font-size: 24px;\n  }\n`;\n_c3 = Logo;\nconst StyledContent = styled(Content)`\n  margin: 24px;\n  padding: 24px;\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  min-height: calc(100vh - 200px);\n`;\n_c4 = StyledContent;\nconst StyledFooter = styled(Footer)`\n  text-align: center;\n  background: #f0f2f5;\n  color: #666;\n`;\n_c5 = StyledFooter;\nconst OnlineStatus = styled.div`\n  margin-left: auto;\n  color: white;\n  display: flex;\n  align-items: center;\n  \n  .status-dot {\n    width: 8px;\n    height: 8px;\n    border-radius: 50%;\n    background: #52c41a;\n    margin-right: 8px;\n    animation: pulse 2s infinite;\n  }\n`;\n\n// 菜单项配置\n_c6 = OnlineStatus;\nconst menuItems = [{\n  key: '/',\n  icon: /*#__PURE__*/_jsxDEV(HomeOutlined, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 89,\n    columnNumber: 11\n  }, this),\n  label: /*#__PURE__*/_jsxDEV(Link, {\n    to: \"/\",\n    children: \"\\u9996\\u9875\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 90,\n    columnNumber: 12\n  }, this)\n}, {\n  key: '/predictions',\n  icon: /*#__PURE__*/_jsxDEV(ThunderboltOutlined, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 94,\n    columnNumber: 11\n  }, this),\n  label: /*#__PURE__*/_jsxDEV(Link, {\n    to: \"/predictions\",\n    children: \"\\u9884\\u6D4B\\u4E2D\\u5FC3\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 95,\n    columnNumber: 12\n  }, this)\n}, {\n  key: '/history',\n  icon: /*#__PURE__*/_jsxDEV(HistoryOutlined, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 99,\n    columnNumber: 11\n  }, this),\n  label: /*#__PURE__*/_jsxDEV(Link, {\n    to: \"/history\",\n    children: \"\\u5386\\u53F2\\u5F00\\u5956\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 100,\n    columnNumber: 12\n  }, this)\n}, {\n  key: '/statistics',\n  icon: /*#__PURE__*/_jsxDEV(BarChartOutlined, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 104,\n    columnNumber: 11\n  }, this),\n  label: /*#__PURE__*/_jsxDEV(Link, {\n    to: \"/statistics\",\n    children: \"\\u7EDF\\u8BA1\\u5206\\u6790\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 105,\n    columnNumber: 12\n  }, this)\n}, {\n  key: '/settings',\n  icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 109,\n    columnNumber: 11\n  }, this),\n  label: /*#__PURE__*/_jsxDEV(Link, {\n    to: \"/settings\",\n    children: \"\\u7CFB\\u7EDF\\u8BBE\\u7F6E\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 110,\n    columnNumber: 12\n  }, this)\n}];\n\n// 主应用组件\nconst App = () => {\n  _s();\n  const [loading, setLoading] = useState(true);\n  const [collapsed, setCollapsed] = useState(false);\n  const [onlineUsers, setOnlineUsers] = useState(0);\n  useEffect(() => {\n    // 初始化应用\n    initializeApp();\n\n    // 清理函数\n    return () => {\n      socketService.disconnect();\n    };\n  }, []);\n  const initializeApp = async () => {\n    try {\n      // 连接Socket.IO\n      socketService.connect();\n\n      // 监听在线用户数更新\n      socketService.on('online_users_update', count => {\n        setOnlineUsers(count);\n      });\n\n      // 监听系统消息\n      socketService.on('system_message', data => {\n        message.info(data.message);\n      });\n\n      // 监听连接状态\n      socketService.on('connect', () => {\n        message.success('连接成功');\n      });\n      socketService.on('disconnect', () => {\n        message.warning('连接断开');\n      });\n\n      // 加载初始配置数据\n      await apiService.loadConfigs();\n      setLoading(false);\n    } catch (error) {\n      console.error('应用初始化失败:', error);\n      message.error('应用初始化失败');\n      setLoading(false);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        height: '100vh',\n        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'\n      },\n      children: /*#__PURE__*/_jsxDEV(Spin, {\n        size: \"large\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(StyledLayout, {\n      children: [/*#__PURE__*/_jsxDEV(StyledHeader, {\n        children: [/*#__PURE__*/_jsxDEV(Logo, {\n          children: [/*#__PURE__*/_jsxDEV(TrophyOutlined, {\n            className: \"logo-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this), \"\\u6FB3\\u95E8\\u8461\\u4EAC\\u65B0\\u5F69\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(OnlineStatus, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"status-dot\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this), \"\\u5728\\u7EBF\\u7528\\u6237: \", onlineUsers]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Layout, {\n        children: [/*#__PURE__*/_jsxDEV(Sider, {\n          collapsible: true,\n          collapsed: collapsed,\n          onCollapse: setCollapsed,\n          theme: \"dark\",\n          width: 200,\n          children: /*#__PURE__*/_jsxDEV(MenuWithLocation, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Layout, {\n          children: /*#__PURE__*/_jsxDEV(StyledContent, {\n            children: /*#__PURE__*/_jsxDEV(Routes, {\n              children: [/*#__PURE__*/_jsxDEV(Route, {\n                path: \"/\",\n                element: /*#__PURE__*/_jsxDEV(HomePage, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 42\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/predictions\",\n                element: /*#__PURE__*/_jsxDEV(PredictionPage, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 53\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/history\",\n                element: /*#__PURE__*/_jsxDEV(HistoryPage, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 49\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/statistics\",\n                element: /*#__PURE__*/_jsxDEV(StatisticsPage, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 52\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/settings\",\n                element: /*#__PURE__*/_jsxDEV(SettingsPage, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 50\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StyledFooter, {\n        children: \"\\u6FB3\\u95E8\\u8461\\u4EAC\\u65B0\\u5F69 \\xA92024 \\u5B9E\\u529B\\u6253\\u9020\\uFF0C\\u706B\\u7206\\u5168\\u7F51\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 180,\n    columnNumber: 5\n  }, this);\n};\n\n// 带路由位置的菜单组件\n_s(App, \"KCYDCpPVYafNivWPhQbHPCdFlGo=\");\n_c7 = App;\nconst MenuWithLocation = () => {\n  _s2();\n  const location = useLocation();\n  return /*#__PURE__*/_jsxDEV(Menu, {\n    theme: \"dark\",\n    mode: \"inline\",\n    selectedKeys: [location.pathname],\n    items: menuItems\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 230,\n    columnNumber: 5\n  }, this);\n};\n_s2(MenuWithLocation, \"pkHmaVRPskBaU4tMJuJJpV42k1I=\", false, function () {\n  return [useLocation];\n});\n_c8 = MenuWithLocation;\nexport default App;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8;\n$RefreshReg$(_c, \"StyledLayout\");\n$RefreshReg$(_c2, \"StyledHeader\");\n$RefreshReg$(_c3, \"Logo\");\n$RefreshReg$(_c4, \"StyledContent\");\n$RefreshReg$(_c5, \"StyledFooter\");\n$RefreshReg$(_c6, \"OnlineStatus\");\n$RefreshReg$(_c7, \"App\");\n$RefreshReg$(_c8, \"MenuWithLocation\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Layout", "<PERSON><PERSON>", "Spin", "message", "HomeOutlined", "BarChartOutlined", "HistoryOutlined", "SettingOutlined", "TrophyOutlined", "ThunderboltOutlined", "styled", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Link", "useLocation", "HomePage", "PredictionPage", "HistoryPage", "StatisticsPage", "SettingsPage", "socketService", "apiService", "jsxDEV", "_jsxDEV", "Header", "Content", "Footer", "<PERSON><PERSON>", "StyledLayout", "_c", "StyledHeader", "_c2", "Logo", "div", "_c3", "Styled<PERSON>ontent", "_c4", "StyledFooter", "_c5", "OnlineStatus", "_c6", "menuItems", "key", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "to", "children", "App", "_s", "loading", "setLoading", "collapsed", "setCollapsed", "onlineUsers", "setOnlineUsers", "initializeApp", "disconnect", "connect", "on", "count", "data", "info", "success", "warning", "loadConfigs", "error", "console", "style", "display", "justifyContent", "alignItems", "height", "background", "size", "className", "collapsible", "onCollapse", "theme", "width", "MenuWithLocation", "path", "element", "_c7", "_s2", "location", "mode", "<PERSON><PERSON><PERSON><PERSON>", "pathname", "items", "_c8", "$RefreshReg$"], "sources": ["D:/liu/html/frontend/src/App.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { Layout, Menu, Spin, message } from 'antd';\nimport {\n  HomeOutlined,\n  BarChartOutlined,\n  HistoryOutlined,\n  SettingOutlined,\n  TrophyOutlined,\n  ThunderboltOutlined\n} from '@ant-design/icons';\nimport styled from 'styled-components';\nimport { BrowserRouter as Router, Routes, Route, Link, useLocation } from 'react-router-dom';\n\n// 导入页面组件\nimport HomePage from './pages/HomePage';\nimport PredictionPage from './pages/PredictionPage';\nimport HistoryPage from './pages/HistoryPage';\nimport StatisticsPage from './pages/StatisticsPage';\nimport SettingsPage from './pages/SettingsPage';\n\n// 导入服务\nimport { socketService } from './services/socketService';\nimport { apiService } from './services/apiService';\n\nconst { Header, Content, Footer, Sider } = Layout;\n\n// 样式组件\nconst StyledLayout = styled(Layout)`\n  min-height: 100vh;\n`;\n\nconst StyledHeader = styled(Header)`\n  display: flex;\n  align-items: center;\n  padding: 0 24px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n`;\n\nconst Logo = styled.div`\n  color: white;\n  font-size: 20px;\n  font-weight: bold;\n  margin-right: 24px;\n  display: flex;\n  align-items: center;\n  \n  .logo-icon {\n    margin-right: 8px;\n    font-size: 24px;\n  }\n`;\n\nconst StyledContent = styled(Content)`\n  margin: 24px;\n  padding: 24px;\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  min-height: calc(100vh - 200px);\n`;\n\nconst StyledFooter = styled(Footer)`\n  text-align: center;\n  background: #f0f2f5;\n  color: #666;\n`;\n\nconst OnlineStatus = styled.div`\n  margin-left: auto;\n  color: white;\n  display: flex;\n  align-items: center;\n  \n  .status-dot {\n    width: 8px;\n    height: 8px;\n    border-radius: 50%;\n    background: #52c41a;\n    margin-right: 8px;\n    animation: pulse 2s infinite;\n  }\n`;\n\n// 菜单项配置\nconst menuItems = [\n  {\n    key: '/',\n    icon: <HomeOutlined />,\n    label: <Link to=\"/\">首页</Link>,\n  },\n  {\n    key: '/predictions',\n    icon: <ThunderboltOutlined />,\n    label: <Link to=\"/predictions\">预测中心</Link>,\n  },\n  {\n    key: '/history',\n    icon: <HistoryOutlined />,\n    label: <Link to=\"/history\">历史开奖</Link>,\n  },\n  {\n    key: '/statistics',\n    icon: <BarChartOutlined />,\n    label: <Link to=\"/statistics\">统计分析</Link>,\n  },\n  {\n    key: '/settings',\n    icon: <SettingOutlined />,\n    label: <Link to=\"/settings\">系统设置</Link>,\n  },\n];\n\n// 主应用组件\nconst App: React.FC = () => {\n  const [loading, setLoading] = useState(true);\n  const [collapsed, setCollapsed] = useState(false);\n  const [onlineUsers, setOnlineUsers] = useState(0);\n\n  useEffect(() => {\n    // 初始化应用\n    initializeApp();\n    \n    // 清理函数\n    return () => {\n      socketService.disconnect();\n    };\n  }, []);\n\n  const initializeApp = async () => {\n    try {\n      // 连接Socket.IO\n      socketService.connect();\n      \n      // 监听在线用户数更新\n      socketService.on('online_users_update', (count: number) => {\n        setOnlineUsers(count);\n      });\n      \n      // 监听系统消息\n      socketService.on('system_message', (data: any) => {\n        message.info(data.message);\n      });\n      \n      // 监听连接状态\n      socketService.on('connect', () => {\n        message.success('连接成功');\n      });\n      \n      socketService.on('disconnect', () => {\n        message.warning('连接断开');\n      });\n      \n      // 加载初始配置数据\n      await apiService.loadConfigs();\n      \n      setLoading(false);\n    } catch (error) {\n      console.error('应用初始化失败:', error);\n      message.error('应用初始化失败');\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div style={{ \n        display: 'flex', \n        justifyContent: 'center', \n        alignItems: 'center', \n        height: '100vh',\n        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'\n      }}>\n        <Spin size=\"large\" />\n      </div>\n    );\n  }\n\n  return (\n    <Router>\n      <StyledLayout>\n        <StyledHeader>\n          <Logo>\n            <TrophyOutlined className=\"logo-icon\" />\n            澳门葡京新彩\n          </Logo>\n          <OnlineStatus>\n            <div className=\"status-dot\" />\n            在线用户: {onlineUsers}\n          </OnlineStatus>\n        </StyledHeader>\n        \n        <Layout>\n          <Sider \n            collapsible \n            collapsed={collapsed} \n            onCollapse={setCollapsed}\n            theme=\"dark\"\n            width={200}\n          >\n            <MenuWithLocation />\n          </Sider>\n          \n          <Layout>\n            <StyledContent>\n              <Routes>\n                <Route path=\"/\" element={<HomePage />} />\n                <Route path=\"/predictions\" element={<PredictionPage />} />\n                <Route path=\"/history\" element={<HistoryPage />} />\n                <Route path=\"/statistics\" element={<StatisticsPage />} />\n                <Route path=\"/settings\" element={<SettingsPage />} />\n              </Routes>\n            </StyledContent>\n          </Layout>\n        </Layout>\n        \n        <StyledFooter>\n          澳门葡京新彩 ©2024 实力打造，火爆全网\n        </StyledFooter>\n      </StyledLayout>\n    </Router>\n  );\n};\n\n// 带路由位置的菜单组件\nconst MenuWithLocation: React.FC = () => {\n  const location = useLocation();\n  \n  return (\n    <Menu\n      theme=\"dark\"\n      mode=\"inline\"\n      selectedKeys={[location.pathname]}\n      items={menuItems}\n    />\n  );\n};\n\nexport default App;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,IAAI,EAAEC,IAAI,EAAEC,OAAO,QAAQ,MAAM;AAClD,SACEC,YAAY,EACZC,gBAAgB,EAChBC,eAAe,EACfC,eAAe,EACfC,cAAc,EACdC,mBAAmB,QACd,mBAAmB;AAC1B,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;;AAE5F;AACA,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,YAAY,MAAM,sBAAsB;;AAE/C;AACA,SAASC,aAAa,QAAQ,0BAA0B;AACxD,SAASC,UAAU,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAM;EAAEC,MAAM;EAAEC,OAAO;EAAEC,MAAM;EAAEC;AAAM,CAAC,GAAG7B,MAAM;;AAEjD;AACA,MAAM8B,YAAY,GAAGpB,MAAM,CAACV,MAAM,CAAC;AACnC;AACA,CAAC;AAAC+B,EAAA,GAFID,YAAY;AAIlB,MAAME,YAAY,GAAGtB,MAAM,CAACgB,MAAM,CAAC;AACnC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACO,GAAA,GANID,YAAY;AAQlB,MAAME,IAAI,GAAGxB,MAAM,CAACyB,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAZIF,IAAI;AAcV,MAAMG,aAAa,GAAG3B,MAAM,CAACiB,OAAO,CAAC;AACrC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACW,GAAA,GAPID,aAAa;AASnB,MAAME,YAAY,GAAG7B,MAAM,CAACkB,MAAM,CAAC;AACnC;AACA;AACA;AACA,CAAC;AAACY,GAAA,GAJID,YAAY;AAMlB,MAAME,YAAY,GAAG/B,MAAM,CAACyB,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAO,GAAA,GAhBMD,YAAY;AAiBlB,MAAME,SAAS,GAAG,CAChB;EACEC,GAAG,EAAE,GAAG;EACRC,IAAI,eAAEpB,OAAA,CAACrB,YAAY;IAAA0C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACtBC,KAAK,eAAEzB,OAAA,CAACV,IAAI;IAACoC,EAAE,EAAC,GAAG;IAAAC,QAAA,EAAC;EAAE;IAAAN,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM;AAC9B,CAAC,EACD;EACEL,GAAG,EAAE,cAAc;EACnBC,IAAI,eAAEpB,OAAA,CAAChB,mBAAmB;IAAAqC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAC7BC,KAAK,eAAEzB,OAAA,CAACV,IAAI;IAACoC,EAAE,EAAC,cAAc;IAAAC,QAAA,EAAC;EAAI;IAAAN,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM;AAC3C,CAAC,EACD;EACEL,GAAG,EAAE,UAAU;EACfC,IAAI,eAAEpB,OAAA,CAACnB,eAAe;IAAAwC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACzBC,KAAK,eAAEzB,OAAA,CAACV,IAAI;IAACoC,EAAE,EAAC,UAAU;IAAAC,QAAA,EAAC;EAAI;IAAAN,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM;AACvC,CAAC,EACD;EACEL,GAAG,EAAE,aAAa;EAClBC,IAAI,eAAEpB,OAAA,CAACpB,gBAAgB;IAAAyC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAC1BC,KAAK,eAAEzB,OAAA,CAACV,IAAI;IAACoC,EAAE,EAAC,aAAa;IAAAC,QAAA,EAAC;EAAI;IAAAN,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM;AAC1C,CAAC,EACD;EACEL,GAAG,EAAE,WAAW;EAChBC,IAAI,eAAEpB,OAAA,CAAClB,eAAe;IAAAuC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACzBC,KAAK,eAAEzB,OAAA,CAACV,IAAI;IAACoC,EAAE,EAAC,WAAW;IAAAC,QAAA,EAAC;EAAI;IAAAN,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM;AACxC,CAAC,CACF;;AAED;AACA,MAAMI,GAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGzD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0D,SAAS,EAAEC,YAAY,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC4D,WAAW,EAAEC,cAAc,CAAC,GAAG7D,QAAQ,CAAC,CAAC,CAAC;EAEjDD,SAAS,CAAC,MAAM;IACd;IACA+D,aAAa,CAAC,CAAC;;IAEf;IACA,OAAO,MAAM;MACXvC,aAAa,CAACwC,UAAU,CAAC,CAAC;IAC5B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF;MACAvC,aAAa,CAACyC,OAAO,CAAC,CAAC;;MAEvB;MACAzC,aAAa,CAAC0C,EAAE,CAAC,qBAAqB,EAAGC,KAAa,IAAK;QACzDL,cAAc,CAACK,KAAK,CAAC;MACvB,CAAC,CAAC;;MAEF;MACA3C,aAAa,CAAC0C,EAAE,CAAC,gBAAgB,EAAGE,IAAS,IAAK;QAChD/D,OAAO,CAACgE,IAAI,CAACD,IAAI,CAAC/D,OAAO,CAAC;MAC5B,CAAC,CAAC;;MAEF;MACAmB,aAAa,CAAC0C,EAAE,CAAC,SAAS,EAAE,MAAM;QAChC7D,OAAO,CAACiE,OAAO,CAAC,MAAM,CAAC;MACzB,CAAC,CAAC;MAEF9C,aAAa,CAAC0C,EAAE,CAAC,YAAY,EAAE,MAAM;QACnC7D,OAAO,CAACkE,OAAO,CAAC,MAAM,CAAC;MACzB,CAAC,CAAC;;MAEF;MACA,MAAM9C,UAAU,CAAC+C,WAAW,CAAC,CAAC;MAE9Bd,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,OAAOe,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAChCpE,OAAO,CAACoE,KAAK,CAAC,SAAS,CAAC;MACxBf,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAID,OAAO,EAAE;IACX,oBACE9B,OAAA;MAAKgD,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,QAAQ;QACxBC,UAAU,EAAE,QAAQ;QACpBC,MAAM,EAAE,OAAO;QACfC,UAAU,EAAE;MACd,CAAE;MAAA1B,QAAA,eACA3B,OAAA,CAACvB,IAAI;QAAC6E,IAAI,EAAC;MAAO;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC;EAEV;EAEA,oBACExB,OAAA,CAACb,MAAM;IAAAwC,QAAA,eACL3B,OAAA,CAACK,YAAY;MAAAsB,QAAA,gBACX3B,OAAA,CAACO,YAAY;QAAAoB,QAAA,gBACX3B,OAAA,CAACS,IAAI;UAAAkB,QAAA,gBACH3B,OAAA,CAACjB,cAAc;YAACwE,SAAS,EAAC;UAAW;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,wCAE1C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPxB,OAAA,CAACgB,YAAY;UAAAW,QAAA,gBACX3B,OAAA;YAAKuD,SAAS,EAAC;UAAY;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,8BACxB,EAACU,WAAW;QAAA;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEfxB,OAAA,CAACzB,MAAM;QAAAoD,QAAA,gBACL3B,OAAA,CAACI,KAAK;UACJoD,WAAW;UACXxB,SAAS,EAAEA,SAAU;UACrByB,UAAU,EAAExB,YAAa;UACzByB,KAAK,EAAC,MAAM;UACZC,KAAK,EAAE,GAAI;UAAAhC,QAAA,eAEX3B,OAAA,CAAC4D,gBAAgB;YAAAvC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eAERxB,OAAA,CAACzB,MAAM;UAAAoD,QAAA,eACL3B,OAAA,CAACY,aAAa;YAAAe,QAAA,eACZ3B,OAAA,CAACZ,MAAM;cAAAuC,QAAA,gBACL3B,OAAA,CAACX,KAAK;gBAACwE,IAAI,EAAC,GAAG;gBAACC,OAAO,eAAE9D,OAAA,CAACR,QAAQ;kBAAA6B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzCxB,OAAA,CAACX,KAAK;gBAACwE,IAAI,EAAC,cAAc;gBAACC,OAAO,eAAE9D,OAAA,CAACP,cAAc;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC1DxB,OAAA,CAACX,KAAK;gBAACwE,IAAI,EAAC,UAAU;gBAACC,OAAO,eAAE9D,OAAA,CAACN,WAAW;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnDxB,OAAA,CAACX,KAAK;gBAACwE,IAAI,EAAC,aAAa;gBAACC,OAAO,eAAE9D,OAAA,CAACL,cAAc;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzDxB,OAAA,CAACX,KAAK;gBAACwE,IAAI,EAAC,WAAW;gBAACC,OAAO,eAAE9D,OAAA,CAACJ,YAAY;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAETxB,OAAA,CAACc,YAAY;QAAAa,QAAA,EAAC;MAEd;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEb,CAAC;;AAED;AAAAK,EAAA,CA9GMD,GAAa;AAAAmC,GAAA,GAAbnC,GAAa;AA+GnB,MAAMgC,gBAA0B,GAAGA,CAAA,KAAM;EAAAI,GAAA;EACvC,MAAMC,QAAQ,GAAG1E,WAAW,CAAC,CAAC;EAE9B,oBACES,OAAA,CAACxB,IAAI;IACHkF,KAAK,EAAC,MAAM;IACZQ,IAAI,EAAC,QAAQ;IACbC,YAAY,EAAE,CAACF,QAAQ,CAACG,QAAQ,CAAE;IAClCC,KAAK,EAAEnD;EAAU;IAAAG,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAClB,CAAC;AAEN,CAAC;AAACwC,GAAA,CAXIJ,gBAA0B;EAAA,QACbrE,WAAW;AAAA;AAAA+E,GAAA,GADxBV,gBAA0B;AAahC,eAAehC,GAAG;AAAC,IAAAtB,EAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAA8C,GAAA,EAAAO,GAAA;AAAAC,YAAA,CAAAjE,EAAA;AAAAiE,YAAA,CAAA/D,GAAA;AAAA+D,YAAA,CAAA5D,GAAA;AAAA4D,YAAA,CAAA1D,GAAA;AAAA0D,YAAA,CAAAxD,GAAA;AAAAwD,YAAA,CAAAtD,GAAA;AAAAsD,YAAA,CAAAR,GAAA;AAAAQ,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}