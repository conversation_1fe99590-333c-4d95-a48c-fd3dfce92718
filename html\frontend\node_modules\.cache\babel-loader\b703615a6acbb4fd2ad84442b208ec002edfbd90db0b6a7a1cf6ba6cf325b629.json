{"ast": null, "code": "var _jsxFileName = \"D:\\\\liu\\\\html\\\\frontend\\\\src\\\\components\\\\ZodiacTable.tsx\";\nimport React from 'react';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ZodiacContainer = styled.div`\n  background: #fff;\n  border-radius: 8px;\n  padding: 20px;\n  margin: 20px 0;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n`;\n_c = ZodiacContainer;\nconst SectionTitle = styled.h2`\n  color: #1890ff;\n  text-align: center;\n  margin-bottom: 20px;\n  font-size: 24px;\n`;\n_c2 = SectionTitle;\nconst ZodiacGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 15px;\n  margin-bottom: 30px;\n`;\n_c3 = ZodiacGrid;\nconst ZodiacItem = styled.div`\n  display: flex;\n  align-items: center;\n  padding: 10px;\n  background: #f8f9fa;\n  border-radius: 6px;\n  border-left: 4px solid #1890ff;\n  \n  .zodiac-name {\n    font-weight: bold;\n    color: #1890ff;\n    margin-right: 10px;\n    min-width: 30px;\n  }\n  \n  .zodiac-numbers {\n    color: #333;\n    font-size: 14px;\n  }\n`;\n_c4 = ZodiacItem;\nconst ColorSection = styled.div`\n  margin: 20px 0;\n`;\n_c5 = ColorSection;\nconst ColorTitle = styled.h3`\n  color: #333;\n  margin-bottom: 10px;\n`;\n_c6 = ColorTitle;\nconst ColorRow = styled.div`\n  display: flex;\n  align-items: center;\n  margin: 8px 0;\n  \n  .color-label {\n    font-weight: bold;\n    margin-right: 10px;\n    min-width: 60px;\n  }\n  \n  .red { color: #ff4d4f; }\n  .blue { color: #1890ff; }\n  .green { color: #52c41a; }\n`;\n_c7 = ColorRow;\nconst AttributeTable = styled.table`\n  width: 100%;\n  border-collapse: collapse;\n  margin-top: 20px;\n  \n  th, td {\n    border: 1px solid #d9d9d9;\n    padding: 8px 12px;\n    text-align: left;\n  }\n  \n  th {\n    background: #f0f2f5;\n    font-weight: bold;\n    color: #333;\n  }\n  \n  td {\n    font-size: 14px;\n  }\n  \n  .attribute-label {\n    font-weight: bold;\n    color: #1890ff;\n  }\n`;\n_c8 = AttributeTable;\nconst ZodiacTable = () => {\n  const zodiacData = [{\n    name: '蛇',\n    numbers: '[冲猪] 01 13 25 37 49'\n  }, {\n    name: '龙',\n    numbers: '[冲狗] 02 14 26 38'\n  }, {\n    name: '兔',\n    numbers: '[冲鸡] 03 15 27 39'\n  }, {\n    name: '虎',\n    numbers: '[冲猴] 04 16 28 40'\n  }, {\n    name: '牛',\n    numbers: '[冲羊] 05 17 29 41'\n  }, {\n    name: '鼠',\n    numbers: '[冲马] 06 18 30 42'\n  }, {\n    name: '猪',\n    numbers: '[冲蛇] 07 19 31 43'\n  }, {\n    name: '狗',\n    numbers: '[冲龙] 08 20 32 44'\n  }, {\n    name: '鸡',\n    numbers: '[冲兔] 09 21 33 45'\n  }, {\n    name: '猴',\n    numbers: '[冲虎] 10 22 34 46'\n  }, {\n    name: '羊',\n    numbers: '[冲牛] 11 23 35 47'\n  }, {\n    name: '马',\n    numbers: '[冲鼠] 12 24 36 48'\n  }];\n  return /*#__PURE__*/_jsxDEV(ZodiacContainer, {\n    children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n      children: \"2025\\u86C7\\u5E74\\uFF08\\u5341\\u4E8C\\u751F\\u8096\\u53F7\\u7801\\u5BF9\\u7167\\uFF09\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ZodiacGrid, {\n      children: zodiacData.map(zodiac => /*#__PURE__*/_jsxDEV(ZodiacItem, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"zodiac-name\",\n          children: zodiac.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"zodiac-numbers\",\n          children: zodiac.numbers\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 13\n        }, this)]\n      }, zodiac.name, true, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ColorSection, {\n      children: [/*#__PURE__*/_jsxDEV(ColorTitle, {\n        children: \"\\u6CE2\\u8272\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ColorRow, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"color-label red\",\n          children: \"\\u7EA2\\u6CE2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"01 02 07 08 12 13 18 19 23 24 29 30 34 35 40 45 46\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ColorRow, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"color-label blue\",\n          children: \"\\u84DD\\u6CE2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"03 04 09 10 14 15 20 25 26 31 36 37 41 42 47 48\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ColorRow, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"color-label green\",\n          children: \"\\u7EFF\\u6CE2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"05 06 11 16 17 21 22 27 28 32 33 38 39 43 44 49\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AttributeTable, {\n      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"\\u5206\\u7C7B\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"\\u751F\\u8096\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"\\u5206\\u7C7B\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"\\u751F\\u8096\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"\\u5206\\u7C7B\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"\\u751F\\u8096\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n        children: [/*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n            className: \"attribute-label\",\n            children: \"\\u5BB6\\u79BD:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: \"\\u725B\\u3001\\u9A6C\\u3001\\u7F8A\\u3001\\u9E21\\u3001\\u72D7\\u3001\\u732A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            className: \"attribute-label\",\n            children: \"\\u5409\\u7F8E:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: \"\\u5154\\u3001\\u9F99\\u3001\\u86C7\\u3001\\u9A6C\\u3001\\u7F8A\\u3001\\u9E21\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            className: \"attribute-label\",\n            children: \"\\u9634\\u6027:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: \"\\u9F20\\u3001\\u9F99\\u3001\\u86C7\\u3001\\u9A6C\\u3001\\u72D7\\u3001\\u732A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n            className: \"attribute-label\",\n            children: \"\\u91CE\\u517D:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: \"\\u9F20\\u3001\\u864E\\u3001\\u5154\\u3001\\u9F99\\u3001\\u86C7\\u3001\\u7334\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            className: \"attribute-label\",\n            children: \"\\u51F6\\u4E11:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: \"\\u9F20\\u3001\\u725B\\u3001\\u864E\\u3001\\u7334\\u3001\\u72D7\\u3001\\u732A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            className: \"attribute-label\",\n            children: \"\\u9633\\u6027:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: \"\\u725B\\u3001\\u864E\\u3001\\u5154\\u3001\\u7F8A\\u3001\\u7334\\u3001\\u9E21\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n            className: \"attribute-label\",\n            children: \"\\u5355\\u7B14:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: \"\\u9F20\\u3001\\u9F99\\u3001\\u9A6C\\u3001\\u86C7\\u3001\\u9E21\\u3001\\u732A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            className: \"attribute-label\",\n            children: \"\\u5929\\u8096:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: \"\\u5154\\u3001\\u9A6C\\u3001\\u7334\\u3001\\u732A\\u3001\\u725B\\u3001\\u9F99\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            className: \"attribute-label\",\n            children: \"\\u767D\\u8FB9:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: \"\\u9F20\\u3001\\u725B\\u3001\\u864E\\u3001\\u9E21\\u3001\\u72D7\\u3001\\u732A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n            className: \"attribute-label\",\n            children: \"\\u53CC\\u7B14:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: \"\\u864E\\u3001\\u7334\\u3001\\u72D7\\u3001\\u5154\\u3001\\u7F8A\\u3001\\u725B\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            className: \"attribute-label\",\n            children: \"\\u5730\\u8096:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: \"\\u86C7\\u3001\\u7F8A\\u3001\\u9E21\\u3001\\u72D7\\u3001\\u9F20\\u3001\\u864E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            className: \"attribute-label\",\n            children: \"\\u9ED1\\u4E2D:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: \"\\u5154\\u3001\\u9F99\\u3001\\u86C7\\u3001\\u9A6C\\u3001\\u7F8A\\u3001\\u7334\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n            className: \"attribute-label\",\n            children: \"\\u5973\\u8096:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: \"\\u5154\\u3001\\u86C7\\u3001\\u7F8A\\u3001\\u9E21\\u3001\\u732A\\uFF08\\u4E94\\u5BAB\\u8096\\uFF09\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            className: \"attribute-label\",\n            children: \"\\u4E09\\u5408:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: \"\\u9F20\\u9F99\\u7334\\u3001\\u725B\\u86C7\\u9E21\\u3001\\u864E\\u9A6C\\u72D7\\u3001\\u5154\\u7F8A\\u732A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            className: \"attribute-label\",\n            children: \"\\u7EA2\\u8096:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: \"\\u9A6C\\u3001\\u5154\\u3001\\u9F20\\u3001\\u9E21\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n            className: \"attribute-label\",\n            children: \"\\u7537\\u8096:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: \"\\u9F20\\u3001\\u725B\\u3001\\u864E\\u3001\\u9F99\\u3001\\u9A6C\\u3001\\u7334\\u3001\\u72D7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            className: \"attribute-label\",\n            children: \"\\u516D\\u5408:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: \"\\u9F20\\u725B\\u3001\\u9F99\\u9E21\\u3001\\u864E\\u732A\\u3001\\u86C7\\u7334\\u3001\\u5154\\u72D7\\u3001\\u9A6C\\u7F8A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            className: \"attribute-label\",\n            children: \"\\u84DD\\u8096:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: \"\\u86C7\\u3001\\u864E\\u3001\\u732A\\u3001\\u7334\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 116,\n    columnNumber: 5\n  }, this);\n};\n_c9 = ZodiacTable;\nexport default ZodiacTable;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9;\n$RefreshReg$(_c, \"ZodiacContainer\");\n$RefreshReg$(_c2, \"SectionTitle\");\n$RefreshReg$(_c3, \"ZodiacGrid\");\n$RefreshReg$(_c4, \"ZodiacItem\");\n$RefreshReg$(_c5, \"ColorSection\");\n$RefreshReg$(_c6, \"ColorTitle\");\n$RefreshReg$(_c7, \"ColorRow\");\n$RefreshReg$(_c8, \"AttributeTable\");\n$RefreshReg$(_c9, \"ZodiacTable\");", "map": {"version": 3, "names": ["React", "styled", "jsxDEV", "_jsxDEV", "ZodiacContainer", "div", "_c", "SectionTitle", "h2", "_c2", "ZodiacGrid", "_c3", "ZodiacItem", "_c4", "ColorSection", "_c5", "ColorTitle", "h3", "_c6", "ColorRow", "_c7", "AttributeTable", "table", "_c8", "ZodiacTable", "zodiacData", "name", "numbers", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "zodiac", "className", "_c9", "$RefreshReg$"], "sources": ["D:/liu/html/frontend/src/components/ZodiacTable.tsx"], "sourcesContent": ["import React from 'react';\nimport styled from 'styled-components';\n\nconst ZodiacContainer = styled.div`\n  background: #fff;\n  border-radius: 8px;\n  padding: 20px;\n  margin: 20px 0;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n`;\n\nconst SectionTitle = styled.h2`\n  color: #1890ff;\n  text-align: center;\n  margin-bottom: 20px;\n  font-size: 24px;\n`;\n\nconst ZodiacGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 15px;\n  margin-bottom: 30px;\n`;\n\nconst ZodiacItem = styled.div`\n  display: flex;\n  align-items: center;\n  padding: 10px;\n  background: #f8f9fa;\n  border-radius: 6px;\n  border-left: 4px solid #1890ff;\n  \n  .zodiac-name {\n    font-weight: bold;\n    color: #1890ff;\n    margin-right: 10px;\n    min-width: 30px;\n  }\n  \n  .zodiac-numbers {\n    color: #333;\n    font-size: 14px;\n  }\n`;\n\nconst ColorSection = styled.div`\n  margin: 20px 0;\n`;\n\nconst ColorTitle = styled.h3`\n  color: #333;\n  margin-bottom: 10px;\n`;\n\nconst ColorRow = styled.div`\n  display: flex;\n  align-items: center;\n  margin: 8px 0;\n  \n  .color-label {\n    font-weight: bold;\n    margin-right: 10px;\n    min-width: 60px;\n  }\n  \n  .red { color: #ff4d4f; }\n  .blue { color: #1890ff; }\n  .green { color: #52c41a; }\n`;\n\nconst AttributeTable = styled.table`\n  width: 100%;\n  border-collapse: collapse;\n  margin-top: 20px;\n  \n  th, td {\n    border: 1px solid #d9d9d9;\n    padding: 8px 12px;\n    text-align: left;\n  }\n  \n  th {\n    background: #f0f2f5;\n    font-weight: bold;\n    color: #333;\n  }\n  \n  td {\n    font-size: 14px;\n  }\n  \n  .attribute-label {\n    font-weight: bold;\n    color: #1890ff;\n  }\n`;\n\nconst ZodiacTable: React.FC = () => {\n  const zodiacData = [\n    { name: '蛇', numbers: '[冲猪] 01 13 25 37 49' },\n    { name: '龙', numbers: '[冲狗] 02 14 26 38' },\n    { name: '兔', numbers: '[冲鸡] 03 15 27 39' },\n    { name: '虎', numbers: '[冲猴] 04 16 28 40' },\n    { name: '牛', numbers: '[冲羊] 05 17 29 41' },\n    { name: '鼠', numbers: '[冲马] 06 18 30 42' },\n    { name: '猪', numbers: '[冲蛇] 07 19 31 43' },\n    { name: '狗', numbers: '[冲龙] 08 20 32 44' },\n    { name: '鸡', numbers: '[冲兔] 09 21 33 45' },\n    { name: '猴', numbers: '[冲虎] 10 22 34 46' },\n    { name: '羊', numbers: '[冲牛] 11 23 35 47' },\n    { name: '马', numbers: '[冲鼠] 12 24 36 48' }\n  ];\n\n  return (\n    <ZodiacContainer>\n      <SectionTitle>2025蛇年（十二生肖号码对照）</SectionTitle>\n      \n      <ZodiacGrid>\n        {zodiacData.map((zodiac) => (\n          <ZodiacItem key={zodiac.name}>\n            <div className=\"zodiac-name\">{zodiac.name}</div>\n            <div className=\"zodiac-numbers\">{zodiac.numbers}</div>\n          </ZodiacItem>\n        ))}\n      </ZodiacGrid>\n\n      <ColorSection>\n        <ColorTitle>波色</ColorTitle>\n        <ColorRow>\n          <span className=\"color-label red\">红波</span>\n          <span>01 02 07 08 12 13 18 19 23 24 29 30 34 35 40 45 46</span>\n        </ColorRow>\n        <ColorRow>\n          <span className=\"color-label blue\">蓝波</span>\n          <span>03 04 09 10 14 15 20 25 26 31 36 37 41 42 47 48</span>\n        </ColorRow>\n        <ColorRow>\n          <span className=\"color-label green\">绿波</span>\n          <span>05 06 11 16 17 21 22 27 28 32 33 38 39 43 44 49</span>\n        </ColorRow>\n      </ColorSection>\n\n      <AttributeTable>\n        <thead>\n          <tr>\n            <th>分类</th>\n            <th>生肖</th>\n            <th>分类</th>\n            <th>生肖</th>\n            <th>分类</th>\n            <th>生肖</th>\n          </tr>\n        </thead>\n        <tbody>\n          <tr>\n            <td className=\"attribute-label\">家禽:</td>\n            <td>牛、马、羊、鸡、狗、猪</td>\n            <td className=\"attribute-label\">吉美:</td>\n            <td>兔、龙、蛇、马、羊、鸡</td>\n            <td className=\"attribute-label\">阴性:</td>\n            <td>鼠、龙、蛇、马、狗、猪</td>\n          </tr>\n          <tr>\n            <td className=\"attribute-label\">野兽:</td>\n            <td>鼠、虎、兔、龙、蛇、猴</td>\n            <td className=\"attribute-label\">凶丑:</td>\n            <td>鼠、牛、虎、猴、狗、猪</td>\n            <td className=\"attribute-label\">阳性:</td>\n            <td>牛、虎、兔、羊、猴、鸡</td>\n          </tr>\n          <tr>\n            <td className=\"attribute-label\">单笔:</td>\n            <td>鼠、龙、马、蛇、鸡、猪</td>\n            <td className=\"attribute-label\">天肖:</td>\n            <td>兔、马、猴、猪、牛、龙</td>\n            <td className=\"attribute-label\">白边:</td>\n            <td>鼠、牛、虎、鸡、狗、猪</td>\n          </tr>\n          <tr>\n            <td className=\"attribute-label\">双笔:</td>\n            <td>虎、猴、狗、兔、羊、牛</td>\n            <td className=\"attribute-label\">地肖:</td>\n            <td>蛇、羊、鸡、狗、鼠、虎</td>\n            <td className=\"attribute-label\">黑中:</td>\n            <td>兔、龙、蛇、马、羊、猴</td>\n          </tr>\n          <tr>\n            <td className=\"attribute-label\">女肖:</td>\n            <td>兔、蛇、羊、鸡、猪（五宫肖）</td>\n            <td className=\"attribute-label\">三合:</td>\n            <td>鼠龙猴、牛蛇鸡、虎马狗、兔羊猪</td>\n            <td className=\"attribute-label\">红肖:</td>\n            <td>马、兔、鼠、鸡</td>\n          </tr>\n          <tr>\n            <td className=\"attribute-label\">男肖:</td>\n            <td>鼠、牛、虎、龙、马、猴、狗</td>\n            <td className=\"attribute-label\">六合:</td>\n            <td>鼠牛、龙鸡、虎猪、蛇猴、兔狗、马羊</td>\n            <td className=\"attribute-label\">蓝肖:</td>\n            <td>蛇、虎、猪、猴</td>\n          </tr>\n        </tbody>\n      </AttributeTable>\n    </ZodiacContainer>\n  );\n};\n\nexport default ZodiacTable;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,eAAe,GAAGH,MAAM,CAACI,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GANIF,eAAe;AAQrB,MAAMG,YAAY,GAAGN,MAAM,CAACO,EAAE;AAC9B;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,YAAY;AAOlB,MAAMG,UAAU,GAAGT,MAAM,CAACI,GAAG;AAC7B;AACA;AACA;AACA;AACA,CAAC;AAACM,GAAA,GALID,UAAU;AAOhB,MAAME,UAAU,GAAGX,MAAM,CAACI,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACQ,GAAA,GAnBID,UAAU;AAqBhB,MAAME,YAAY,GAAGb,MAAM,CAACI,GAAG;AAC/B;AACA,CAAC;AAACU,GAAA,GAFID,YAAY;AAIlB,MAAME,UAAU,GAAGf,MAAM,CAACgB,EAAE;AAC5B;AACA;AACA,CAAC;AAACC,GAAA,GAHIF,UAAU;AAKhB,MAAMG,QAAQ,GAAGlB,MAAM,CAACI,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACe,GAAA,GAdID,QAAQ;AAgBd,MAAME,cAAc,GAAGpB,MAAM,CAACqB,KAAK;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAzBIF,cAAc;AA2BpB,MAAMG,WAAqB,GAAGA,CAAA,KAAM;EAClC,MAAMC,UAAU,GAAG,CACjB;IAAEC,IAAI,EAAE,GAAG;IAAEC,OAAO,EAAE;EAAsB,CAAC,EAC7C;IAAED,IAAI,EAAE,GAAG;IAAEC,OAAO,EAAE;EAAmB,CAAC,EAC1C;IAAED,IAAI,EAAE,GAAG;IAAEC,OAAO,EAAE;EAAmB,CAAC,EAC1C;IAAED,IAAI,EAAE,GAAG;IAAEC,OAAO,EAAE;EAAmB,CAAC,EAC1C;IAAED,IAAI,EAAE,GAAG;IAAEC,OAAO,EAAE;EAAmB,CAAC,EAC1C;IAAED,IAAI,EAAE,GAAG;IAAEC,OAAO,EAAE;EAAmB,CAAC,EAC1C;IAAED,IAAI,EAAE,GAAG;IAAEC,OAAO,EAAE;EAAmB,CAAC,EAC1C;IAAED,IAAI,EAAE,GAAG;IAAEC,OAAO,EAAE;EAAmB,CAAC,EAC1C;IAAED,IAAI,EAAE,GAAG;IAAEC,OAAO,EAAE;EAAmB,CAAC,EAC1C;IAAED,IAAI,EAAE,GAAG;IAAEC,OAAO,EAAE;EAAmB,CAAC,EAC1C;IAAED,IAAI,EAAE,GAAG;IAAEC,OAAO,EAAE;EAAmB,CAAC,EAC1C;IAAED,IAAI,EAAE,GAAG;IAAEC,OAAO,EAAE;EAAmB,CAAC,CAC3C;EAED,oBACExB,OAAA,CAACC,eAAe;IAAAwB,QAAA,gBACdzB,OAAA,CAACI,YAAY;MAAAqB,QAAA,EAAC;IAAgB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAc,CAAC,eAE7C7B,OAAA,CAACO,UAAU;MAAAkB,QAAA,EACRH,UAAU,CAACQ,GAAG,CAAEC,MAAM,iBACrB/B,OAAA,CAACS,UAAU;QAAAgB,QAAA,gBACTzB,OAAA;UAAKgC,SAAS,EAAC,aAAa;UAAAP,QAAA,EAAEM,MAAM,CAACR;QAAI;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAChD7B,OAAA;UAAKgC,SAAS,EAAC,gBAAgB;UAAAP,QAAA,EAAEM,MAAM,CAACP;QAAO;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA,GAFvCE,MAAM,CAACR,IAAI;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGhB,CACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC,eAEb7B,OAAA,CAACW,YAAY;MAAAc,QAAA,gBACXzB,OAAA,CAACa,UAAU;QAAAY,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC3B7B,OAAA,CAACgB,QAAQ;QAAAS,QAAA,gBACPzB,OAAA;UAAMgC,SAAS,EAAC,iBAAiB;UAAAP,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC3C7B,OAAA;UAAAyB,QAAA,EAAM;QAAkD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC,eACX7B,OAAA,CAACgB,QAAQ;QAAAS,QAAA,gBACPzB,OAAA;UAAMgC,SAAS,EAAC,kBAAkB;UAAAP,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC5C7B,OAAA;UAAAyB,QAAA,EAAM;QAA+C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC,eACX7B,OAAA,CAACgB,QAAQ;QAAAS,QAAA,gBACPzB,OAAA;UAAMgC,SAAS,EAAC,mBAAmB;UAAAP,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC7C7B,OAAA;UAAAyB,QAAA,EAAM;QAA+C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEf7B,OAAA,CAACkB,cAAc;MAAAO,QAAA,gBACbzB,OAAA;QAAAyB,QAAA,eACEzB,OAAA;UAAAyB,QAAA,gBACEzB,OAAA;YAAAyB,QAAA,EAAI;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACX7B,OAAA;YAAAyB,QAAA,EAAI;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACX7B,OAAA;YAAAyB,QAAA,EAAI;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACX7B,OAAA;YAAAyB,QAAA,EAAI;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACX7B,OAAA;YAAAyB,QAAA,EAAI;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACX7B,OAAA;YAAAyB,QAAA,EAAI;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACR7B,OAAA;QAAAyB,QAAA,gBACEzB,OAAA;UAAAyB,QAAA,gBACEzB,OAAA;YAAIgC,SAAS,EAAC,iBAAiB;YAAAP,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxC7B,OAAA;YAAAyB,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpB7B,OAAA;YAAIgC,SAAS,EAAC,iBAAiB;YAAAP,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxC7B,OAAA;YAAAyB,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpB7B,OAAA;YAAIgC,SAAS,EAAC,iBAAiB;YAAAP,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxC7B,OAAA;YAAAyB,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eACL7B,OAAA;UAAAyB,QAAA,gBACEzB,OAAA;YAAIgC,SAAS,EAAC,iBAAiB;YAAAP,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxC7B,OAAA;YAAAyB,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpB7B,OAAA;YAAIgC,SAAS,EAAC,iBAAiB;YAAAP,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxC7B,OAAA;YAAAyB,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpB7B,OAAA;YAAIgC,SAAS,EAAC,iBAAiB;YAAAP,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxC7B,OAAA;YAAAyB,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eACL7B,OAAA;UAAAyB,QAAA,gBACEzB,OAAA;YAAIgC,SAAS,EAAC,iBAAiB;YAAAP,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxC7B,OAAA;YAAAyB,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpB7B,OAAA;YAAIgC,SAAS,EAAC,iBAAiB;YAAAP,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxC7B,OAAA;YAAAyB,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpB7B,OAAA;YAAIgC,SAAS,EAAC,iBAAiB;YAAAP,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxC7B,OAAA;YAAAyB,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eACL7B,OAAA;UAAAyB,QAAA,gBACEzB,OAAA;YAAIgC,SAAS,EAAC,iBAAiB;YAAAP,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxC7B,OAAA;YAAAyB,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpB7B,OAAA;YAAIgC,SAAS,EAAC,iBAAiB;YAAAP,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxC7B,OAAA;YAAAyB,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpB7B,OAAA;YAAIgC,SAAS,EAAC,iBAAiB;YAAAP,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxC7B,OAAA;YAAAyB,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eACL7B,OAAA;UAAAyB,QAAA,gBACEzB,OAAA;YAAIgC,SAAS,EAAC,iBAAiB;YAAAP,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxC7B,OAAA;YAAAyB,QAAA,EAAI;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvB7B,OAAA;YAAIgC,SAAS,EAAC,iBAAiB;YAAAP,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxC7B,OAAA;YAAAyB,QAAA,EAAI;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxB7B,OAAA;YAAIgC,SAAS,EAAC,iBAAiB;YAAAP,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxC7B,OAAA;YAAAyB,QAAA,EAAI;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,eACL7B,OAAA;UAAAyB,QAAA,gBACEzB,OAAA;YAAIgC,SAAS,EAAC,iBAAiB;YAAAP,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxC7B,OAAA;YAAAyB,QAAA,EAAI;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtB7B,OAAA;YAAIgC,SAAS,EAAC,iBAAiB;YAAAP,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxC7B,OAAA;YAAAyB,QAAA,EAAI;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1B7B,OAAA;YAAIgC,SAAS,EAAC,iBAAiB;YAAAP,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxC7B,OAAA;YAAAyB,QAAA,EAAI;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEtB,CAAC;AAACI,GAAA,GA7GIZ,WAAqB;AA+G3B,eAAeA,WAAW;AAAC,IAAAlB,EAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAa,GAAA;AAAAC,YAAA,CAAA/B,EAAA;AAAA+B,YAAA,CAAA5B,GAAA;AAAA4B,YAAA,CAAA1B,GAAA;AAAA0B,YAAA,CAAAxB,GAAA;AAAAwB,YAAA,CAAAtB,GAAA;AAAAsB,YAAA,CAAAnB,GAAA;AAAAmB,YAAA,CAAAjB,GAAA;AAAAiB,YAAA,CAAAd,GAAA;AAAAc,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}