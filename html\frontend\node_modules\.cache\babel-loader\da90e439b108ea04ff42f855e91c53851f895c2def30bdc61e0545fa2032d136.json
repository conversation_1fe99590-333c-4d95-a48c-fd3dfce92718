{"ast": null, "code": "var _jsxFileName = \"D:\\\\liu\\\\html\\\\frontend\\\\src\\\\pages\\\\HomePage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Row, Col, Card, Statistic, Typography, Divider, Spin, message } from 'antd';\nimport { ClockCircleOutlined, TrophyOutlined, FireOutlined, ThunderboltOutlined } from '@ant-design/icons';\nimport styled from 'styled-components';\nimport { socketService } from '../services/socketService';\nimport { apiService } from '../services/apiService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\n\n// 样式组件\nconst StyledCard = styled(Card)`\n  margin-bottom: 16px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  border-radius: 8px;\n  \n  &:hover {\n    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\n    transform: translateY(-2px);\n    transition: all 0.3s ease;\n  }\n`;\n_c = StyledCard;\nconst CountdownCard = styled(StyledCard)`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  \n  .ant-card-body {\n    text-align: center;\n    padding: 24px;\n  }\n`;\n_c2 = CountdownCard;\nconst LotteryNumber = styled.div`\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  color: white;\n  font-weight: bold;\n  font-size: 16px;\n  margin: 0 6px;\n  background: ${props => {\n  switch (props.color) {\n    case '红':\n      return 'linear-gradient(135deg, #ff4757, #ff3742)';\n    case '蓝':\n      return 'linear-gradient(135deg, #3742fa, #2f3542)';\n    case '绿':\n      return 'linear-gradient(135deg, #2ed573, #1e90ff)';\n    default:\n      return 'linear-gradient(135deg, #747d8c, #57606f)';\n  }\n}};\n  \n  &.special {\n    width: 48px;\n    height: 48px;\n    font-size: 18px;\n    border: 2px solid #ffd700;\n    box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);\n  }\n`;\n_c3 = LotteryNumber;\nconst CountdownDisplay = styled.div`\n  font-size: 32px;\n  font-weight: bold;\n  font-family: 'Courier New', monospace;\n  margin: 16px 0;\n  color: #ffd700;\n  text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);\n`;\n_c4 = CountdownDisplay;\nconst PeriodInfo = styled.div`\n  font-size: 18px;\n  margin-bottom: 16px;\n`;\n_c5 = PeriodInfo;\nconst ResultsContainer = styled.div`\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  flex-wrap: wrap;\n  margin: 16px 0;\n`;\n_c6 = ResultsContainer;\nconst ZodiacDisplay = styled.div`\n  display: flex;\n  justify-content: center;\n  flex-wrap: wrap;\n  margin-top: 8px;\n  \n  .zodiac-item {\n    background: rgba(255, 255, 255, 0.2);\n    padding: 4px 8px;\n    margin: 2px;\n    border-radius: 4px;\n    font-size: 12px;\n  }\n`;\n_c7 = ZodiacDisplay;\nconst HomePage = () => {\n  _s();\n  var _currentData$currentP;\n  const [loading, setLoading] = useState(true);\n  const [currentData, setCurrentData] = useState(null);\n  const [countdown, setCountdown] = useState(0);\n  const [stats, setStats] = useState(null);\n  useEffect(() => {\n    loadInitialData();\n    setupSocketListeners();\n    return () => {\n      // 清理Socket监听器\n      socketService.off('current_period');\n      socketService.off('countdown_update');\n      socketService.off('new_result');\n    };\n  }, []);\n  useEffect(() => {\n    // 倒计时定时器\n    const timer = setInterval(() => {\n      if (countdown > 0) {\n        setCountdown(prev => Math.max(0, prev - 1000));\n      }\n    }, 1000);\n    return () => clearInterval(timer);\n  }, [countdown]);\n  const loadInitialData = async () => {\n    try {\n      const [currentPeriod, statsData] = await Promise.all([apiService.getCurrentPeriod(), apiService.getLotteryStats()]);\n      setCurrentData(currentPeriod);\n      setStats(statsData);\n      if (currentPeriod.timeUntilDraw) {\n        setCountdown(currentPeriod.timeUntilDraw);\n      }\n      setLoading(false);\n    } catch (error) {\n      console.error('加载数据失败:', error);\n      message.error('加载数据失败');\n      setLoading(false);\n    }\n  };\n  const setupSocketListeners = () => {\n    // 监听当前期数更新\n    socketService.onCurrentPeriod(data => {\n      setCurrentData(data);\n      if (data.timeUntilDraw) {\n        setCountdown(data.timeUntilDraw);\n      }\n    });\n\n    // 监听倒计时更新\n    socketService.onCountdownUpdate(data => {\n      setCountdown(data.timeUntilDraw);\n    });\n\n    // 监听新开奖结果\n    socketService.onNewResult(result => {\n      message.success(`第${result.period.period_number}期开奖结果已公布！`);\n      loadInitialData(); // 重新加载数据\n    });\n  };\n  const formatCountdown = milliseconds => {\n    const totalSeconds = Math.floor(milliseconds / 1000);\n    const hours = Math.floor(totalSeconds / 3600);\n    const minutes = Math.floor(totalSeconds % 3600 / 60);\n    const seconds = totalSeconds % 60;\n    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n  };\n  const renderLotteryNumbers = result => {\n    if (!result) return null;\n    const numbers = [result.number_1, result.number_2, result.number_3, result.number_4, result.number_5, result.number_6];\n    const zodiacs = [result.zodiac_1, result.zodiac_2, result.zodiac_3, result.zodiac_4, result.zodiac_5, result.zodiac_6];\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(ResultsContainer, {\n        children: [numbers.map((num, index) => {\n          const color = apiService.getColorByNumberFromCache(num) || '绿';\n          return /*#__PURE__*/_jsxDEV(LotteryNumber, {\n            color: color,\n            children: num.toString().padStart(2, '0')\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 15\n          }, this);\n        }), /*#__PURE__*/_jsxDEV(LotteryNumber, {\n          color: apiService.getColorByNumberFromCache(result.special_number) || '绿',\n          className: \"special\",\n          children: result.special_number.toString().padStart(2, '0')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ZodiacDisplay, {\n        children: [zodiacs.map((zodiac, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"zodiac-item\",\n          children: zodiac\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 13\n        }, this)), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"zodiac-item special\",\n          children: result.special_zodiac\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 7\n    }, this);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-container\",\n      children: /*#__PURE__*/_jsxDEV(Spin, {\n        size: \"large\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 227,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      children: [/*#__PURE__*/_jsxDEV(TrophyOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 9\n      }, this), \" \\u6FB3\\u95E8\\u8461\\u4EAC\\u65B0\\u5F69\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 235,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Text, {\n      type: \"secondary\",\n      children: \"\\u5B9E\\u529B\\u6253\\u9020\\uFF0C\\u706B\\u7206\\u5168\\u7F51\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 240,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 12,\n        children: /*#__PURE__*/_jsxDEV(CountdownCard, {\n          children: [/*#__PURE__*/_jsxDEV(ClockCircleOutlined, {\n            style: {\n              fontSize: 24,\n              marginBottom: 16\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(PeriodInfo, {\n            children: [\"\\u7B2C \", (currentData === null || currentData === void 0 ? void 0 : (_currentData$currentP = currentData.currentPeriod) === null || _currentData$currentP === void 0 ? void 0 : _currentData$currentP.period_number) || '---', \" \\u671F\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CountdownDisplay, {\n            children: countdown > 0 ? formatCountdown(countdown) : '开奖中...'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            style: {\n              color: 'rgba(255, 255, 255, 0.8)'\n            },\n            children: \"\\u8DDD\\u79BB\\u5F00\\u5956\\u8FD8\\u6709\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 12,\n        children: /*#__PURE__*/_jsxDEV(StyledCard, {\n          title: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [/*#__PURE__*/_jsxDEV(FireOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 15\n            }, this), \" \\u6700\\u65B0\\u5F00\\u5956\\u7ED3\\u679C\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 13\n          }, this),\n          children: currentData !== null && currentData !== void 0 && currentData.latestResult ? /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: [\"\\u7B2C \", currentData.latestResult.period.period_number, \" \\u671F\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 17\n            }, this), renderLotteryNumbers(currentData.latestResult)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            children: \"\\u6682\\u65E0\\u5F00\\u5956\\u7ED3\\u679C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        children: /*#__PURE__*/_jsxDEV(StyledCard, {\n          title: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [/*#__PURE__*/_jsxDEV(ThunderboltOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 15\n            }, this), \" \\u7EDF\\u8BA1\\u4FE1\\u606F\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 13\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(Statistic, {\n                title: \"\\u603B\\u671F\\u6570\",\n                value: (stats === null || stats === void 0 ? void 0 : stats.totalPeriods) || 0,\n                valueStyle: {\n                  color: '#1890ff'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(Statistic, {\n                title: \"\\u5DF2\\u5F00\\u5956\",\n                value: (stats === null || stats === void 0 ? void 0 : stats.drawnPeriods) || 0,\n                valueStyle: {\n                  color: '#52c41a'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(Statistic, {\n                title: \"\\u5F85\\u5F00\\u5956\",\n                value: (stats === null || stats === void 0 ? void 0 : stats.pendingPeriods) || 0,\n                valueStyle: {\n                  color: '#faad14'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(Statistic, {\n                title: \"\\u5728\\u7EBF\\u7528\\u6237\",\n                value: 0,\n                valueStyle: {\n                  color: '#722ed1'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 242,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 234,\n    columnNumber: 5\n  }, this);\n};\n_s(HomePage, \"MoBmcn3E+DzLaP+1JgYBlO09S4g=\");\n_c8 = HomePage;\nexport default HomePage;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8;\n$RefreshReg$(_c, \"StyledCard\");\n$RefreshReg$(_c2, \"CountdownCard\");\n$RefreshReg$(_c3, \"LotteryNumber\");\n$RefreshReg$(_c4, \"CountdownDisplay\");\n$RefreshReg$(_c5, \"PeriodInfo\");\n$RefreshReg$(_c6, \"ResultsContainer\");\n$RefreshReg$(_c7, \"ZodiacDisplay\");\n$RefreshReg$(_c8, \"HomePage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Row", "Col", "Card", "Statistic", "Typography", "Divider", "Spin", "message", "ClockCircleOutlined", "TrophyOutlined", "FireOutlined", "ThunderboltOutlined", "styled", "socketService", "apiService", "jsxDEV", "_jsxDEV", "Title", "Text", "StyledCard", "_c", "CountdownCard", "_c2", "LotteryNumber", "div", "props", "color", "_c3", "CountdownDisplay", "_c4", "PeriodInfo", "_c5", "ResultsContainer", "_c6", "ZodiacDisplay", "_c7", "HomePage", "_s", "_currentData$currentP", "loading", "setLoading", "currentData", "setCurrentData", "countdown", "setCountdown", "stats", "setStats", "loadInitialData", "setupSocketListeners", "off", "timer", "setInterval", "prev", "Math", "max", "clearInterval", "currentPeriod", "statsData", "Promise", "all", "getCurrentPeriod", "getLotteryStats", "timeUntilDraw", "error", "console", "onCurrentPeriod", "data", "onCountdownUpdate", "onNewResult", "result", "success", "period", "period_number", "formatCountdown", "milliseconds", "totalSeconds", "floor", "hours", "minutes", "seconds", "toString", "padStart", "renderLotteryNumbers", "numbers", "number_1", "number_2", "number_3", "number_4", "number_5", "number_6", "zodiacs", "zodiac_1", "zodiac_2", "zodiac_3", "zodiac_4", "zodiac_5", "zodiac_6", "children", "map", "num", "index", "getColorByNumberFromCache", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "special_number", "className", "zodiac", "special_zodiac", "size", "level", "type", "gutter", "xs", "lg", "style", "fontSize", "marginBottom", "title", "latestResult", "strong", "sm", "value", "totalPeriods", "valueStyle", "drawnPeriods", "pendingPeriods", "_c8", "$RefreshReg$"], "sources": ["D:/liu/html/frontend/src/pages/HomePage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Row, Col, Card, Statistic, Typography, Divider, Spin, message } from 'antd';\nimport { ClockCircleOutlined, TrophyOutlined, FireOutlined, ThunderboltOutlined } from '@ant-design/icons';\nimport styled from 'styled-components';\nimport { socketService } from '../services/socketService';\nimport { apiService } from '../services/apiService';\n\nconst { Title, Text } = Typography;\n\n// 样式组件\nconst StyledCard = styled(Card)`\n  margin-bottom: 16px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  border-radius: 8px;\n  \n  &:hover {\n    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\n    transform: translateY(-2px);\n    transition: all 0.3s ease;\n  }\n`;\n\nconst CountdownCard = styled(StyledCard)`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  \n  .ant-card-body {\n    text-align: center;\n    padding: 24px;\n  }\n`;\n\nconst LotteryNumber = styled.div<{ color: string }>`\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  color: white;\n  font-weight: bold;\n  font-size: 16px;\n  margin: 0 6px;\n  background: ${props => {\n    switch (props.color) {\n      case '红': return 'linear-gradient(135deg, #ff4757, #ff3742)';\n      case '蓝': return 'linear-gradient(135deg, #3742fa, #2f3542)';\n      case '绿': return 'linear-gradient(135deg, #2ed573, #1e90ff)';\n      default: return 'linear-gradient(135deg, #747d8c, #57606f)';\n    }\n  }};\n  \n  &.special {\n    width: 48px;\n    height: 48px;\n    font-size: 18px;\n    border: 2px solid #ffd700;\n    box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);\n  }\n`;\n\nconst CountdownDisplay = styled.div`\n  font-size: 32px;\n  font-weight: bold;\n  font-family: 'Courier New', monospace;\n  margin: 16px 0;\n  color: #ffd700;\n  text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);\n`;\n\nconst PeriodInfo = styled.div`\n  font-size: 18px;\n  margin-bottom: 16px;\n`;\n\nconst ResultsContainer = styled.div`\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  flex-wrap: wrap;\n  margin: 16px 0;\n`;\n\nconst ZodiacDisplay = styled.div`\n  display: flex;\n  justify-content: center;\n  flex-wrap: wrap;\n  margin-top: 8px;\n  \n  .zodiac-item {\n    background: rgba(255, 255, 255, 0.2);\n    padding: 4px 8px;\n    margin: 2px;\n    border-radius: 4px;\n    font-size: 12px;\n  }\n`;\n\ninterface CurrentPeriodData {\n  currentPeriod: any;\n  timeUntilDraw: number;\n  latestResult: any;\n}\n\nconst HomePage: React.FC = () => {\n  const [loading, setLoading] = useState(true);\n  const [currentData, setCurrentData] = useState<CurrentPeriodData | null>(null);\n  const [countdown, setCountdown] = useState(0);\n  const [stats, setStats] = useState<any>(null);\n\n  useEffect(() => {\n    loadInitialData();\n    setupSocketListeners();\n    \n    return () => {\n      // 清理Socket监听器\n      socketService.off('current_period');\n      socketService.off('countdown_update');\n      socketService.off('new_result');\n    };\n  }, []);\n\n  useEffect(() => {\n    // 倒计时定时器\n    const timer = setInterval(() => {\n      if (countdown > 0) {\n        setCountdown(prev => Math.max(0, prev - 1000));\n      }\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, [countdown]);\n\n  const loadInitialData = async () => {\n    try {\n      const [currentPeriod, statsData] = await Promise.all([\n        apiService.getCurrentPeriod(),\n        apiService.getLotteryStats()\n      ]);\n      \n      setCurrentData(currentPeriod);\n      setStats(statsData);\n      \n      if (currentPeriod.timeUntilDraw) {\n        setCountdown(currentPeriod.timeUntilDraw);\n      }\n      \n      setLoading(false);\n    } catch (error) {\n      console.error('加载数据失败:', error);\n      message.error('加载数据失败');\n      setLoading(false);\n    }\n  };\n\n  const setupSocketListeners = () => {\n    // 监听当前期数更新\n    socketService.onCurrentPeriod((data: CurrentPeriodData) => {\n      setCurrentData(data);\n      if (data.timeUntilDraw) {\n        setCountdown(data.timeUntilDraw);\n      }\n    });\n\n    // 监听倒计时更新\n    socketService.onCountdownUpdate((data: any) => {\n      setCountdown(data.timeUntilDraw);\n    });\n\n    // 监听新开奖结果\n    socketService.onNewResult((result: any) => {\n      message.success(`第${result.period.period_number}期开奖结果已公布！`);\n      loadInitialData(); // 重新加载数据\n    });\n  };\n\n  const formatCountdown = (milliseconds: number) => {\n    const totalSeconds = Math.floor(milliseconds / 1000);\n    const hours = Math.floor(totalSeconds / 3600);\n    const minutes = Math.floor((totalSeconds % 3600) / 60);\n    const seconds = totalSeconds % 60;\n    \n    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n  };\n\n  const renderLotteryNumbers = (result: any) => {\n    if (!result) return null;\n\n    const numbers = [\n      result.number_1, result.number_2, result.number_3,\n      result.number_4, result.number_5, result.number_6\n    ];\n    \n    const zodiacs = [\n      result.zodiac_1, result.zodiac_2, result.zodiac_3,\n      result.zodiac_4, result.zodiac_5, result.zodiac_6\n    ];\n\n    return (\n      <div>\n        <ResultsContainer>\n          {numbers.map((num: number, index: number) => {\n            const color = apiService.getColorByNumberFromCache(num) || '绿';\n            return (\n              <LotteryNumber key={index} color={color}>\n                {num.toString().padStart(2, '0')}\n              </LotteryNumber>\n            );\n          })}\n          <LotteryNumber color={apiService.getColorByNumberFromCache(result.special_number) || '绿'} className=\"special\">\n            {result.special_number.toString().padStart(2, '0')}\n          </LotteryNumber>\n        </ResultsContainer>\n        \n        <ZodiacDisplay>\n          {zodiacs.map((zodiac: string, index: number) => (\n            <span key={index} className=\"zodiac-item\">{zodiac}</span>\n          ))}\n          <span className=\"zodiac-item special\">{result.special_zodiac}</span>\n        </ZodiacDisplay>\n      </div>\n    );\n  };\n\n  if (loading) {\n    return (\n      <div className=\"loading-container\">\n        <Spin size=\"large\" />\n      </div>\n    );\n  }\n\n  return (\n    <div>\n      <Title level={2}>\n        <TrophyOutlined /> 澳门葡京新彩\n      </Title>\n      <Text type=\"secondary\">实力打造，火爆全网</Text>\n      \n      <Divider />\n\n      <Row gutter={[16, 16]}>\n        {/* 倒计时卡片 */}\n        <Col xs={24} lg={12}>\n          <CountdownCard>\n            <ClockCircleOutlined style={{ fontSize: 24, marginBottom: 16 }} />\n            <PeriodInfo>\n              第 {currentData?.currentPeriod?.period_number || '---'} 期\n            </PeriodInfo>\n            <CountdownDisplay>\n              {countdown > 0 ? formatCountdown(countdown) : '开奖中...'}\n            </CountdownDisplay>\n            <Text style={{ color: 'rgba(255, 255, 255, 0.8)' }}>\n              距离开奖还有\n            </Text>\n          </CountdownCard>\n        </Col>\n\n        {/* 最新开奖结果 */}\n        <Col xs={24} lg={12}>\n          <StyledCard title={\n            <span>\n              <FireOutlined /> 最新开奖结果\n            </span>\n          }>\n            {currentData?.latestResult ? (\n              <div>\n                <Text strong>\n                  第 {currentData.latestResult.period.period_number} 期\n                </Text>\n                {renderLotteryNumbers(currentData.latestResult)}\n              </div>\n            ) : (\n              <Text type=\"secondary\">暂无开奖结果</Text>\n            )}\n          </StyledCard>\n        </Col>\n\n        {/* 统计信息 */}\n        <Col xs={24}>\n          <StyledCard title={\n            <span>\n              <ThunderboltOutlined /> 统计信息\n            </span>\n          }>\n            <Row gutter={16}>\n              <Col xs={12} sm={6}>\n                <Statistic\n                  title=\"总期数\"\n                  value={stats?.totalPeriods || 0}\n                  valueStyle={{ color: '#1890ff' }}\n                />\n              </Col>\n              <Col xs={12} sm={6}>\n                <Statistic\n                  title=\"已开奖\"\n                  value={stats?.drawnPeriods || 0}\n                  valueStyle={{ color: '#52c41a' }}\n                />\n              </Col>\n              <Col xs={12} sm={6}>\n                <Statistic\n                  title=\"待开奖\"\n                  value={stats?.pendingPeriods || 0}\n                  valueStyle={{ color: '#faad14' }}\n                />\n              </Col>\n              <Col xs={12} sm={6}>\n                <Statistic\n                  title=\"在线用户\"\n                  value={0}\n                  valueStyle={{ color: '#722ed1' }}\n                />\n              </Col>\n            </Row>\n          </StyledCard>\n        </Col>\n      </Row>\n    </div>\n  );\n};\n\nexport default HomePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,SAAS,EAAEC,UAAU,EAAEC,OAAO,EAAEC,IAAI,EAAEC,OAAO,QAAQ,MAAM;AACpF,SAASC,mBAAmB,EAAEC,cAAc,EAAEC,YAAY,EAAEC,mBAAmB,QAAQ,mBAAmB;AAC1G,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,UAAU,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGd,UAAU;;AAElC;AACA,MAAMe,UAAU,GAAGP,MAAM,CAACV,IAAI,CAAC;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACkB,EAAA,GAVID,UAAU;AAYhB,MAAME,aAAa,GAAGT,MAAM,CAACO,UAAU,CAAC;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GARID,aAAa;AAUnB,MAAME,aAAa,GAAGX,MAAM,CAACY,GAAsB;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgBC,KAAK,IAAI;EACrB,QAAQA,KAAK,CAACC,KAAK;IACjB,KAAK,GAAG;MAAE,OAAO,2CAA2C;IAC5D,KAAK,GAAG;MAAE,OAAO,2CAA2C;IAC5D,KAAK,GAAG;MAAE,OAAO,2CAA2C;IAC5D;MAAS,OAAO,2CAA2C;EAC7D;AACF,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GA3BIJ,aAAa;AA6BnB,MAAMK,gBAAgB,GAAGhB,MAAM,CAACY,GAAG;AACnC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACK,GAAA,GAPID,gBAAgB;AAStB,MAAME,UAAU,GAAGlB,MAAM,CAACY,GAAG;AAC7B;AACA;AACA,CAAC;AAACO,GAAA,GAHID,UAAU;AAKhB,MAAME,gBAAgB,GAAGpB,MAAM,CAACY,GAAG;AACnC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACS,GAAA,GANID,gBAAgB;AAQtB,MAAME,aAAa,GAAGtB,MAAM,CAACY,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACW,GAAA,GAbID,aAAa;AAqBnB,MAAME,QAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EAC/B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC2C,WAAW,EAAEC,cAAc,CAAC,GAAG5C,QAAQ,CAA2B,IAAI,CAAC;EAC9E,MAAM,CAAC6C,SAAS,EAAEC,YAAY,CAAC,GAAG9C,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAAC+C,KAAK,EAAEC,QAAQ,CAAC,GAAGhD,QAAQ,CAAM,IAAI,CAAC;EAE7CC,SAAS,CAAC,MAAM;IACdgD,eAAe,CAAC,CAAC;IACjBC,oBAAoB,CAAC,CAAC;IAEtB,OAAO,MAAM;MACX;MACAnC,aAAa,CAACoC,GAAG,CAAC,gBAAgB,CAAC;MACnCpC,aAAa,CAACoC,GAAG,CAAC,kBAAkB,CAAC;MACrCpC,aAAa,CAACoC,GAAG,CAAC,YAAY,CAAC;IACjC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAENlD,SAAS,CAAC,MAAM;IACd;IACA,MAAMmD,KAAK,GAAGC,WAAW,CAAC,MAAM;MAC9B,IAAIR,SAAS,GAAG,CAAC,EAAE;QACjBC,YAAY,CAACQ,IAAI,IAAIC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEF,IAAI,GAAG,IAAI,CAAC,CAAC;MAChD;IACF,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMG,aAAa,CAACL,KAAK,CAAC;EACnC,CAAC,EAAE,CAACP,SAAS,CAAC,CAAC;EAEf,MAAMI,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAM,CAACS,aAAa,EAAEC,SAAS,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACnD7C,UAAU,CAAC8C,gBAAgB,CAAC,CAAC,EAC7B9C,UAAU,CAAC+C,eAAe,CAAC,CAAC,CAC7B,CAAC;MAEFnB,cAAc,CAACc,aAAa,CAAC;MAC7BV,QAAQ,CAACW,SAAS,CAAC;MAEnB,IAAID,aAAa,CAACM,aAAa,EAAE;QAC/BlB,YAAY,CAACY,aAAa,CAACM,aAAa,CAAC;MAC3C;MAEAtB,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,OAAOuB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BxD,OAAO,CAACwD,KAAK,CAAC,QAAQ,CAAC;MACvBvB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMQ,oBAAoB,GAAGA,CAAA,KAAM;IACjC;IACAnC,aAAa,CAACoD,eAAe,CAAEC,IAAuB,IAAK;MACzDxB,cAAc,CAACwB,IAAI,CAAC;MACpB,IAAIA,IAAI,CAACJ,aAAa,EAAE;QACtBlB,YAAY,CAACsB,IAAI,CAACJ,aAAa,CAAC;MAClC;IACF,CAAC,CAAC;;IAEF;IACAjD,aAAa,CAACsD,iBAAiB,CAAED,IAAS,IAAK;MAC7CtB,YAAY,CAACsB,IAAI,CAACJ,aAAa,CAAC;IAClC,CAAC,CAAC;;IAEF;IACAjD,aAAa,CAACuD,WAAW,CAAEC,MAAW,IAAK;MACzC9D,OAAO,CAAC+D,OAAO,CAAC,IAAID,MAAM,CAACE,MAAM,CAACC,aAAa,WAAW,CAAC;MAC3DzB,eAAe,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC;EACJ,CAAC;EAED,MAAM0B,eAAe,GAAIC,YAAoB,IAAK;IAChD,MAAMC,YAAY,GAAGtB,IAAI,CAACuB,KAAK,CAACF,YAAY,GAAG,IAAI,CAAC;IACpD,MAAMG,KAAK,GAAGxB,IAAI,CAACuB,KAAK,CAACD,YAAY,GAAG,IAAI,CAAC;IAC7C,MAAMG,OAAO,GAAGzB,IAAI,CAACuB,KAAK,CAAED,YAAY,GAAG,IAAI,GAAI,EAAE,CAAC;IACtD,MAAMI,OAAO,GAAGJ,YAAY,GAAG,EAAE;IAEjC,OAAO,GAAGE,KAAK,CAACG,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIH,OAAO,CAACE,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,OAAO,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAC7H,CAAC;EAED,MAAMC,oBAAoB,GAAIb,MAAW,IAAK;IAC5C,IAAI,CAACA,MAAM,EAAE,OAAO,IAAI;IAExB,MAAMc,OAAO,GAAG,CACdd,MAAM,CAACe,QAAQ,EAAEf,MAAM,CAACgB,QAAQ,EAAEhB,MAAM,CAACiB,QAAQ,EACjDjB,MAAM,CAACkB,QAAQ,EAAElB,MAAM,CAACmB,QAAQ,EAAEnB,MAAM,CAACoB,QAAQ,CAClD;IAED,MAAMC,OAAO,GAAG,CACdrB,MAAM,CAACsB,QAAQ,EAAEtB,MAAM,CAACuB,QAAQ,EAAEvB,MAAM,CAACwB,QAAQ,EACjDxB,MAAM,CAACyB,QAAQ,EAAEzB,MAAM,CAAC0B,QAAQ,EAAE1B,MAAM,CAAC2B,QAAQ,CAClD;IAED,oBACEhF,OAAA;MAAAiF,QAAA,gBACEjF,OAAA,CAACgB,gBAAgB;QAAAiE,QAAA,GACdd,OAAO,CAACe,GAAG,CAAC,CAACC,GAAW,EAAEC,KAAa,KAAK;UAC3C,MAAM1E,KAAK,GAAGZ,UAAU,CAACuF,yBAAyB,CAACF,GAAG,CAAC,IAAI,GAAG;UAC9D,oBACEnF,OAAA,CAACO,aAAa;YAAaG,KAAK,EAAEA,KAAM;YAAAuE,QAAA,EACrCE,GAAG,CAACnB,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG;UAAC,GADdmB,KAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEV,CAAC;QAEpB,CAAC,CAAC,eACFzF,OAAA,CAACO,aAAa;UAACG,KAAK,EAAEZ,UAAU,CAACuF,yBAAyB,CAAChC,MAAM,CAACqC,cAAc,CAAC,IAAI,GAAI;UAACC,SAAS,EAAC,SAAS;UAAAV,QAAA,EAC1G5B,MAAM,CAACqC,cAAc,CAAC1B,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG;QAAC;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAEnBzF,OAAA,CAACkB,aAAa;QAAA+D,QAAA,GACXP,OAAO,CAACQ,GAAG,CAAC,CAACU,MAAc,EAAER,KAAa,kBACzCpF,OAAA;UAAkB2F,SAAS,EAAC,aAAa;UAAAV,QAAA,EAAEW;QAAM,GAAtCR,KAAK;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAwC,CACzD,CAAC,eACFzF,OAAA;UAAM2F,SAAS,EAAC,qBAAqB;UAAAV,QAAA,EAAE5B,MAAM,CAACwC;QAAc;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CAAC;EAEV,CAAC;EAED,IAAIlE,OAAO,EAAE;IACX,oBACEvB,OAAA;MAAK2F,SAAS,EAAC,mBAAmB;MAAAV,QAAA,eAChCjF,OAAA,CAACV,IAAI;QAACwG,IAAI,EAAC;MAAO;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC;EAEV;EAEA,oBACEzF,OAAA;IAAAiF,QAAA,gBACEjF,OAAA,CAACC,KAAK;MAAC8F,KAAK,EAAE,CAAE;MAAAd,QAAA,gBACdjF,OAAA,CAACP,cAAc;QAAA6F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,yCACpB;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eACRzF,OAAA,CAACE,IAAI;MAAC8F,IAAI,EAAC,WAAW;MAAAf,QAAA,EAAC;IAAS;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAEvCzF,OAAA,CAACX,OAAO;MAAAiG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEXzF,OAAA,CAAChB,GAAG;MAACiH,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAAAhB,QAAA,gBAEpBjF,OAAA,CAACf,GAAG;QAACiH,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAAAlB,QAAA,eAClBjF,OAAA,CAACK,aAAa;UAAA4E,QAAA,gBACZjF,OAAA,CAACR,mBAAmB;YAAC4G,KAAK,EAAE;cAAEC,QAAQ,EAAE,EAAE;cAAEC,YAAY,EAAE;YAAG;UAAE;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClEzF,OAAA,CAACc,UAAU;YAAAmE,QAAA,GAAC,SACR,EAAC,CAAAxD,WAAW,aAAXA,WAAW,wBAAAH,qBAAA,GAAXG,WAAW,CAAEe,aAAa,cAAAlB,qBAAA,uBAA1BA,qBAAA,CAA4BkC,aAAa,KAAI,KAAK,EAAC,SACxD;UAAA;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbzF,OAAA,CAACY,gBAAgB;YAAAqE,QAAA,EACdtD,SAAS,GAAG,CAAC,GAAG8B,eAAe,CAAC9B,SAAS,CAAC,GAAG;UAAQ;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eACnBzF,OAAA,CAACE,IAAI;YAACkG,KAAK,EAAE;cAAE1F,KAAK,EAAE;YAA2B,CAAE;YAAAuE,QAAA,EAAC;UAEpD;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,eAGNzF,OAAA,CAACf,GAAG;QAACiH,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAAAlB,QAAA,eAClBjF,OAAA,CAACG,UAAU;UAACoG,KAAK,eACfvG,OAAA;YAAAiF,QAAA,gBACEjF,OAAA,CAACN,YAAY;cAAA4F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,yCAClB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP;UAAAR,QAAA,EACExD,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAE+E,YAAY,gBACxBxG,OAAA;YAAAiF,QAAA,gBACEjF,OAAA,CAACE,IAAI;cAACuG,MAAM;cAAAxB,QAAA,GAAC,SACT,EAACxD,WAAW,CAAC+E,YAAY,CAACjD,MAAM,CAACC,aAAa,EAAC,SACnD;YAAA;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EACNvB,oBAAoB,CAACzC,WAAW,CAAC+E,YAAY,CAAC;UAAA;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,gBAENzF,OAAA,CAACE,IAAI;YAAC8F,IAAI,EAAC,WAAW;YAAAf,QAAA,EAAC;UAAM;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QACpC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGNzF,OAAA,CAACf,GAAG;QAACiH,EAAE,EAAE,EAAG;QAAAjB,QAAA,eACVjF,OAAA,CAACG,UAAU;UAACoG,KAAK,eACfvG,OAAA;YAAAiF,QAAA,gBACEjF,OAAA,CAACL,mBAAmB;cAAA2F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,6BACzB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP;UAAAR,QAAA,eACCjF,OAAA,CAAChB,GAAG;YAACiH,MAAM,EAAE,EAAG;YAAAhB,QAAA,gBACdjF,OAAA,CAACf,GAAG;cAACiH,EAAE,EAAE,EAAG;cAACQ,EAAE,EAAE,CAAE;cAAAzB,QAAA,eACjBjF,OAAA,CAACb,SAAS;gBACRoH,KAAK,EAAC,oBAAK;gBACXI,KAAK,EAAE,CAAA9E,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE+E,YAAY,KAAI,CAAE;gBAChCC,UAAU,EAAE;kBAAEnG,KAAK,EAAE;gBAAU;cAAE;gBAAA4E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNzF,OAAA,CAACf,GAAG;cAACiH,EAAE,EAAE,EAAG;cAACQ,EAAE,EAAE,CAAE;cAAAzB,QAAA,eACjBjF,OAAA,CAACb,SAAS;gBACRoH,KAAK,EAAC,oBAAK;gBACXI,KAAK,EAAE,CAAA9E,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEiF,YAAY,KAAI,CAAE;gBAChCD,UAAU,EAAE;kBAAEnG,KAAK,EAAE;gBAAU;cAAE;gBAAA4E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNzF,OAAA,CAACf,GAAG;cAACiH,EAAE,EAAE,EAAG;cAACQ,EAAE,EAAE,CAAE;cAAAzB,QAAA,eACjBjF,OAAA,CAACb,SAAS;gBACRoH,KAAK,EAAC,oBAAK;gBACXI,KAAK,EAAE,CAAA9E,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEkF,cAAc,KAAI,CAAE;gBAClCF,UAAU,EAAE;kBAAEnG,KAAK,EAAE;gBAAU;cAAE;gBAAA4E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNzF,OAAA,CAACf,GAAG;cAACiH,EAAE,EAAE,EAAG;cAACQ,EAAE,EAAE,CAAE;cAAAzB,QAAA,eACjBjF,OAAA,CAACb,SAAS;gBACRoH,KAAK,EAAC,0BAAM;gBACZI,KAAK,EAAE,CAAE;gBACTE,UAAU,EAAE;kBAAEnG,KAAK,EAAE;gBAAU;cAAE;gBAAA4E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpE,EAAA,CAxNID,QAAkB;AAAA4F,GAAA,GAAlB5F,QAAkB;AA0NxB,eAAeA,QAAQ;AAAC,IAAAhB,EAAA,EAAAE,GAAA,EAAAK,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAA6F,GAAA;AAAAC,YAAA,CAAA7G,EAAA;AAAA6G,YAAA,CAAA3G,GAAA;AAAA2G,YAAA,CAAAtG,GAAA;AAAAsG,YAAA,CAAApG,GAAA;AAAAoG,YAAA,CAAAlG,GAAA;AAAAkG,YAAA,CAAAhG,GAAA;AAAAgG,YAAA,CAAA9F,GAAA;AAAA8F,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}