{"ast": null, "code": "var _jsxFileName = \"D:\\\\liu\\\\html\\\\frontend\\\\src\\\\components\\\\LotterySelector.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SelectorContainer = styled.div`\n  background: #fff;\n  margin: 20px 0;\n  padding: 20px;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n`;\n_c = SelectorContainer;\nconst ButtonGroup = styled.div`\n  display: flex;\n  gap: 10px;\n  justify-content: center;\n  flex-wrap: wrap;\n`;\n_c2 = ButtonGroup;\nconst LotteryButton = styled.button`\n  padding: 12px 24px;\n  border: 2px solid ${props => props.active ? '#1890ff' : '#d9d9d9'};\n  background: ${props => props.active ? '#1890ff' : '#fff'};\n  color: ${props => props.active ? '#fff' : '#333'};\n  border-radius: 6px;\n  font-size: 16px;\n  font-weight: bold;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    border-color: #1890ff;\n    color: ${props => props.active ? '#fff' : '#1890ff'};\n  }\n  \n  &:active {\n    transform: translateY(1px);\n  }\n`;\n_c3 = LotteryButton;\nconst LotterySelector = () => {\n  _s();\n  const [selectedLottery, setSelectedLottery] = useState('澳门葡京新彩');\n  const lotteries = ['澳门葡京新彩', '新澳彩', '㊣澳门', '香港彩'];\n  return /*#__PURE__*/_jsxDEV(SelectorContainer, {\n    children: /*#__PURE__*/_jsxDEV(ButtonGroup, {\n      children: lotteries.map(lottery => /*#__PURE__*/_jsxDEV(LotteryButton, {\n        active: selectedLottery === lottery,\n        onClick: () => setSelectedLottery(lottery),\n        children: lottery\n      }, lottery, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 51,\n    columnNumber: 5\n  }, this);\n};\n_s(LotterySelector, \"APHNPaopvNshF94ztyFcZRAuy/E=\");\n_c4 = LotterySelector;\nexport default LotterySelector;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"SelectorContainer\");\n$RefreshReg$(_c2, \"ButtonGroup\");\n$RefreshReg$(_c3, \"LotteryButton\");\n$RefreshReg$(_c4, \"LotterySelector\");", "map": {"version": 3, "names": ["React", "useState", "styled", "jsxDEV", "_jsxDEV", "SelectorContainer", "div", "_c", "ButtonGroup", "_c2", "LotteryButton", "button", "props", "active", "_c3", "LotterySelector", "_s", "selectedLottery", "setSelectedLottery", "lotteries", "children", "map", "lottery", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c4", "$RefreshReg$"], "sources": ["D:/liu/html/frontend/src/components/LotterySelector.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport styled from 'styled-components';\n\nconst SelectorContainer = styled.div`\n  background: #fff;\n  margin: 20px 0;\n  padding: 20px;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n`;\n\nconst ButtonGroup = styled.div`\n  display: flex;\n  gap: 10px;\n  justify-content: center;\n  flex-wrap: wrap;\n`;\n\nconst LotteryButton = styled.button<{ active?: boolean }>`\n  padding: 12px 24px;\n  border: 2px solid ${props => props.active ? '#1890ff' : '#d9d9d9'};\n  background: ${props => props.active ? '#1890ff' : '#fff'};\n  color: ${props => props.active ? '#fff' : '#333'};\n  border-radius: 6px;\n  font-size: 16px;\n  font-weight: bold;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    border-color: #1890ff;\n    color: ${props => props.active ? '#fff' : '#1890ff'};\n  }\n  \n  &:active {\n    transform: translateY(1px);\n  }\n`;\n\nconst LotterySelector: React.FC = () => {\n  const [selectedLottery, setSelectedLottery] = useState('澳门葡京新彩');\n  \n  const lotteries = [\n    '澳门葡京新彩',\n    '新澳彩',\n    '㊣澳门',\n    '香港彩'\n  ];\n\n  return (\n    <SelectorContainer>\n      <ButtonGroup>\n        {lotteries.map((lottery) => (\n          <LotteryButton\n            key={lottery}\n            active={selectedLottery === lottery}\n            onClick={() => setSelectedLottery(lottery)}\n          >\n            {lottery}\n          </LotteryButton>\n        ))}\n      </ButtonGroup>\n    </SelectorContainer>\n  );\n};\n\nexport default LotterySelector;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,iBAAiB,GAAGH,MAAM,CAACI,GAAG;AACpC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GANIF,iBAAiB;AAQvB,MAAMG,WAAW,GAAGN,MAAM,CAACI,GAAG;AAC9B;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GALID,WAAW;AAOjB,MAAME,aAAa,GAAGR,MAAM,CAACS,MAA4B;AACzD;AACA,sBAAsBC,KAAK,IAAIA,KAAK,CAACC,MAAM,GAAG,SAAS,GAAG,SAAS;AACnE,gBAAgBD,KAAK,IAAIA,KAAK,CAACC,MAAM,GAAG,SAAS,GAAG,MAAM;AAC1D,WAAWD,KAAK,IAAIA,KAAK,CAACC,MAAM,GAAG,MAAM,GAAG,MAAM;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAaD,KAAK,IAAIA,KAAK,CAACC,MAAM,GAAG,MAAM,GAAG,SAAS;AACvD;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAnBIJ,aAAa;AAqBnB,MAAMK,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGjB,QAAQ,CAAC,QAAQ,CAAC;EAEhE,MAAMkB,SAAS,GAAG,CAChB,QAAQ,EACR,KAAK,EACL,KAAK,EACL,KAAK,CACN;EAED,oBACEf,OAAA,CAACC,iBAAiB;IAAAe,QAAA,eAChBhB,OAAA,CAACI,WAAW;MAAAY,QAAA,EACTD,SAAS,CAACE,GAAG,CAAEC,OAAO,iBACrBlB,OAAA,CAACM,aAAa;QAEZG,MAAM,EAAEI,eAAe,KAAKK,OAAQ;QACpCC,OAAO,EAAEA,CAAA,KAAML,kBAAkB,CAACI,OAAO,CAAE;QAAAF,QAAA,EAE1CE;MAAO,GAJHA,OAAO;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAKC,CAChB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACS;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAExB,CAAC;AAACX,EAAA,CAzBID,eAAyB;AAAAa,GAAA,GAAzBb,eAAyB;AA2B/B,eAAeA,eAAe;AAAC,IAAAR,EAAA,EAAAE,GAAA,EAAAK,GAAA,EAAAc,GAAA;AAAAC,YAAA,CAAAtB,EAAA;AAAAsB,YAAA,CAAApB,GAAA;AAAAoB,YAAA,CAAAf,GAAA;AAAAe,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}