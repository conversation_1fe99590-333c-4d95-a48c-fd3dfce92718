{"ast": null, "code": "// imported from https://github.com/galkn/parseuri\n/**\n * Parses a URI\n *\n * Note: we could also have used the built-in URL object, but it isn't supported on all platforms.\n *\n * See:\n * - https://developer.mozilla.org/en-US/docs/Web/API/URL\n * - https://caniuse.com/url\n * - https://www.rfc-editor.org/rfc/rfc3986#appendix-B\n *\n * History of the parse() method:\n * - first commit: https://github.com/socketio/socket.io-client/commit/4ee1d5d94b3906a9c052b459f1a818b15f38f91c\n * - export into its own module: https://github.com/socketio/engine.io-client/commit/de2c561e4564efeb78f1bdb1ba39ef81b2822cb3\n * - reimport: https://github.com/socketio/engine.io-client/commit/df32277c3f6d622eec5ed09f493cae3f3391d242\n *\n * <AUTHOR> <stevenlevithan.com> (MIT license)\n * @api private\n */\nconst re = /^(?:(?![^:@\\/?#]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@\\/?#]*)(?::([^:@\\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\nconst parts = ['source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'];\nexport function parse(str) {\n  if (str.length > 8000) {\n    throw \"URI too long\";\n  }\n  const src = str,\n    b = str.indexOf('['),\n    e = str.indexOf(']');\n  if (b != -1 && e != -1) {\n    str = str.substring(0, b) + str.substring(b, e).replace(/:/g, ';') + str.substring(e, str.length);\n  }\n  let m = re.exec(str || ''),\n    uri = {},\n    i = 14;\n  while (i--) {\n    uri[parts[i]] = m[i] || '';\n  }\n  if (b != -1 && e != -1) {\n    uri.source = src;\n    uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, ':');\n    uri.authority = uri.authority.replace('[', '').replace(']', '').replace(/;/g, ':');\n    uri.ipv6uri = true;\n  }\n  uri.pathNames = pathNames(uri, uri['path']);\n  uri.queryKey = queryKey(uri, uri['query']);\n  return uri;\n}\nfunction pathNames(obj, path) {\n  const regx = /\\/{2,9}/g,\n    names = path.replace(regx, \"/\").split(\"/\");\n  if (path.slice(0, 1) == '/' || path.length === 0) {\n    names.splice(0, 1);\n  }\n  if (path.slice(-1) == '/') {\n    names.splice(names.length - 1, 1);\n  }\n  return names;\n}\nfunction queryKey(uri, query) {\n  const data = {};\n  query.replace(/(?:^|&)([^&=]*)=?([^&]*)/g, function ($0, $1, $2) {\n    if ($1) {\n      data[$1] = $2;\n    }\n  });\n  return data;\n}", "map": {"version": 3, "names": ["re", "parts", "parse", "str", "length", "src", "b", "indexOf", "e", "substring", "replace", "m", "exec", "uri", "i", "source", "host", "authority", "ipv6uri", "pathNames", "query<PERSON><PERSON>", "obj", "path", "regx", "names", "split", "slice", "splice", "query", "data", "$0", "$1", "$2"], "sources": ["D:/liu/html/frontend/node_modules/engine.io-client/build/esm/contrib/parseuri.js"], "sourcesContent": ["// imported from https://github.com/galkn/parseuri\n/**\n * Parses a URI\n *\n * Note: we could also have used the built-in URL object, but it isn't supported on all platforms.\n *\n * See:\n * - https://developer.mozilla.org/en-US/docs/Web/API/URL\n * - https://caniuse.com/url\n * - https://www.rfc-editor.org/rfc/rfc3986#appendix-B\n *\n * History of the parse() method:\n * - first commit: https://github.com/socketio/socket.io-client/commit/4ee1d5d94b3906a9c052b459f1a818b15f38f91c\n * - export into its own module: https://github.com/socketio/engine.io-client/commit/de2c561e4564efeb78f1bdb1ba39ef81b2822cb3\n * - reimport: https://github.com/socketio/engine.io-client/commit/df32277c3f6d622eec5ed09f493cae3f3391d242\n *\n * <AUTHOR> <stevenlevithan.com> (MIT license)\n * @api private\n */\nconst re = /^(?:(?![^:@\\/?#]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@\\/?#]*)(?::([^:@\\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\nconst parts = [\n    'source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'\n];\nexport function parse(str) {\n    if (str.length > 8000) {\n        throw \"URI too long\";\n    }\n    const src = str, b = str.indexOf('['), e = str.indexOf(']');\n    if (b != -1 && e != -1) {\n        str = str.substring(0, b) + str.substring(b, e).replace(/:/g, ';') + str.substring(e, str.length);\n    }\n    let m = re.exec(str || ''), uri = {}, i = 14;\n    while (i--) {\n        uri[parts[i]] = m[i] || '';\n    }\n    if (b != -1 && e != -1) {\n        uri.source = src;\n        uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, ':');\n        uri.authority = uri.authority.replace('[', '').replace(']', '').replace(/;/g, ':');\n        uri.ipv6uri = true;\n    }\n    uri.pathNames = pathNames(uri, uri['path']);\n    uri.queryKey = queryKey(uri, uri['query']);\n    return uri;\n}\nfunction pathNames(obj, path) {\n    const regx = /\\/{2,9}/g, names = path.replace(regx, \"/\").split(\"/\");\n    if (path.slice(0, 1) == '/' || path.length === 0) {\n        names.splice(0, 1);\n    }\n    if (path.slice(-1) == '/') {\n        names.splice(names.length - 1, 1);\n    }\n    return names;\n}\nfunction queryKey(uri, query) {\n    const data = {};\n    query.replace(/(?:^|&)([^&=]*)=?([^&]*)/g, function ($0, $1, $2) {\n        if ($1) {\n            data[$1] = $2;\n        }\n    });\n    return data;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,EAAE,GAAG,qPAAqP;AAChQ,MAAMC,KAAK,GAAG,CACV,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,CAChJ;AACD,OAAO,SAASC,KAAKA,CAACC,GAAG,EAAE;EACvB,IAAIA,GAAG,CAACC,MAAM,GAAG,IAAI,EAAE;IACnB,MAAM,cAAc;EACxB;EACA,MAAMC,GAAG,GAAGF,GAAG;IAAEG,CAAC,GAAGH,GAAG,CAACI,OAAO,CAAC,GAAG,CAAC;IAAEC,CAAC,GAAGL,GAAG,CAACI,OAAO,CAAC,GAAG,CAAC;EAC3D,IAAID,CAAC,IAAI,CAAC,CAAC,IAAIE,CAAC,IAAI,CAAC,CAAC,EAAE;IACpBL,GAAG,GAAGA,GAAG,CAACM,SAAS,CAAC,CAAC,EAAEH,CAAC,CAAC,GAAGH,GAAG,CAACM,SAAS,CAACH,CAAC,EAAEE,CAAC,CAAC,CAACE,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,GAAGP,GAAG,CAACM,SAAS,CAACD,CAAC,EAAEL,GAAG,CAACC,MAAM,CAAC;EACrG;EACA,IAAIO,CAAC,GAAGX,EAAE,CAACY,IAAI,CAACT,GAAG,IAAI,EAAE,CAAC;IAAEU,GAAG,GAAG,CAAC,CAAC;IAAEC,CAAC,GAAG,EAAE;EAC5C,OAAOA,CAAC,EAAE,EAAE;IACRD,GAAG,CAACZ,KAAK,CAACa,CAAC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC,IAAI,EAAE;EAC9B;EACA,IAAIR,CAAC,IAAI,CAAC,CAAC,IAAIE,CAAC,IAAI,CAAC,CAAC,EAAE;IACpBK,GAAG,CAACE,MAAM,GAAGV,GAAG;IAChBQ,GAAG,CAACG,IAAI,GAAGH,GAAG,CAACG,IAAI,CAACP,SAAS,CAAC,CAAC,EAAEI,GAAG,CAACG,IAAI,CAACZ,MAAM,GAAG,CAAC,CAAC,CAACM,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;IACxEG,GAAG,CAACI,SAAS,GAAGJ,GAAG,CAACI,SAAS,CAACP,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;IAClFG,GAAG,CAACK,OAAO,GAAG,IAAI;EACtB;EACAL,GAAG,CAACM,SAAS,GAAGA,SAAS,CAACN,GAAG,EAAEA,GAAG,CAAC,MAAM,CAAC,CAAC;EAC3CA,GAAG,CAACO,QAAQ,GAAGA,QAAQ,CAACP,GAAG,EAAEA,GAAG,CAAC,OAAO,CAAC,CAAC;EAC1C,OAAOA,GAAG;AACd;AACA,SAASM,SAASA,CAACE,GAAG,EAAEC,IAAI,EAAE;EAC1B,MAAMC,IAAI,GAAG,UAAU;IAAEC,KAAK,GAAGF,IAAI,CAACZ,OAAO,CAACa,IAAI,EAAE,GAAG,CAAC,CAACE,KAAK,CAAC,GAAG,CAAC;EACnE,IAAIH,IAAI,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,GAAG,IAAIJ,IAAI,CAAClB,MAAM,KAAK,CAAC,EAAE;IAC9CoB,KAAK,CAACG,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;EACtB;EACA,IAAIL,IAAI,CAACI,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE;IACvBF,KAAK,CAACG,MAAM,CAACH,KAAK,CAACpB,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC;EACrC;EACA,OAAOoB,KAAK;AAChB;AACA,SAASJ,QAAQA,CAACP,GAAG,EAAEe,KAAK,EAAE;EAC1B,MAAMC,IAAI,GAAG,CAAC,CAAC;EACfD,KAAK,CAAClB,OAAO,CAAC,2BAA2B,EAAE,UAAUoB,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;IAC7D,IAAID,EAAE,EAAE;MACJF,IAAI,CAACE,EAAE,CAAC,GAAGC,EAAE;IACjB;EACJ,CAAC,CAAC;EACF,OAAOH,IAAI;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}