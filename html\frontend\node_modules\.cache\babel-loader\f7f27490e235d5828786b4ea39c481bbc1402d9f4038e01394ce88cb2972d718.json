{"ast": null, "code": "var _jsxFileName = \"D:\\\\liu\\\\html\\\\frontend\\\\src\\\\index.tsx\";\nimport React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport { ConfigProvider } from 'antd';\nimport zhCN from 'antd/locale/zh_CN';\nimport 'antd/dist/reset.css';\nimport './index.css';\nimport App from './App';\n\n// 配置Ant Design主题\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst theme = {\n  token: {\n    colorPrimary: '#1890ff',\n    colorSuccess: '#52c41a',\n    colorWarning: '#faad14',\n    colorError: '#ff4d4f',\n    colorInfo: '#1890ff',\n    borderRadius: 6,\n    wireframe: false\n  },\n  components: {\n    Layout: {\n      headerBg: '#001529',\n      headerHeight: 64\n    },\n    Menu: {\n      darkItemBg: '#001529',\n      darkSubMenuItemBg: '#000c17',\n      darkItemSelectedBg: '#1890ff'\n    }\n  }\n};\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(/*#__PURE__*/_jsxDEV(React.StrictMode, {\n  children: /*#__PURE__*/_jsxDEV(ConfigProvider, {\n    locale: zhCN,\n    theme: theme,\n    children: /*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 38,\n  columnNumber: 3\n}, this));\n\n// Service Worker 暂时禁用\n// if ('serviceWorker' in navigator) {\n//   window.addEventListener('load', () => {\n//     navigator.serviceWorker.register('/sw.js')\n//       .then((registration) => {\n//         console.log('SW registered: ', registration);\n//       })\n//       .catch((registrationError) => {\n//         console.log('SW registration failed: ', registrationError);\n//       });\n//   });\n// }", "map": {"version": 3, "names": ["React", "ReactDOM", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zhCN", "App", "jsxDEV", "_jsxDEV", "theme", "token", "colorPrimary", "colorSuccess", "colorWarning", "colorError", "colorInfo", "borderRadius", "wireframe", "components", "Layout", "headerBg", "headerHeight", "<PERSON><PERSON>", "darkItemBg", "darkSubMenuItemBg", "darkItemSelectedBg", "root", "createRoot", "document", "getElementById", "render", "StrictMode", "children", "locale", "fileName", "_jsxFileName", "lineNumber", "columnNumber"], "sources": ["D:/liu/html/frontend/src/index.tsx"], "sourcesContent": ["import React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport { ConfigProvider } from 'antd';\nimport zhCN from 'antd/locale/zh_CN';\nimport 'antd/dist/reset.css';\nimport './index.css';\nimport App from './App';\n\n// 配置Ant Design主题\nconst theme = {\n  token: {\n    colorPrimary: '#1890ff',\n    colorSuccess: '#52c41a',\n    colorWarning: '#faad14',\n    colorError: '#ff4d4f',\n    colorInfo: '#1890ff',\n    borderRadius: 6,\n    wireframe: false,\n  },\n  components: {\n    Layout: {\n      headerBg: '#001529',\n      headerHeight: 64,\n    },\n    Menu: {\n      darkItemBg: '#001529',\n      darkSubMenuItemBg: '#000c17',\n      darkItemSelectedBg: '#1890ff',\n    },\n  },\n};\n\nconst root = ReactDOM.createRoot(\n  document.getElementById('root') as HTMLElement\n);\n\nroot.render(\n  <React.StrictMode>\n    <ConfigProvider \n      locale={zhCN} \n      theme={theme}\n    >\n      <App />\n    </ConfigProvider>\n  </React.StrictMode>\n);\n\n// Service Worker 暂时禁用\n// if ('serviceWorker' in navigator) {\n//   window.addEventListener('load', () => {\n//     navigator.serviceWorker.register('/sw.js')\n//       .then((registration) => {\n//         console.log('SW registered: ', registration);\n//       })\n//       .catch((registrationError) => {\n//         console.log('SW registration failed: ', registrationError);\n//       });\n//   });\n// }\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,SAASC,cAAc,QAAQ,MAAM;AACrC,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAO,qBAAqB;AAC5B,OAAO,aAAa;AACpB,OAAOC,GAAG,MAAM,OAAO;;AAEvB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,KAAK,GAAG;EACZC,KAAK,EAAE;IACLC,YAAY,EAAE,SAAS;IACvBC,YAAY,EAAE,SAAS;IACvBC,YAAY,EAAE,SAAS;IACvBC,UAAU,EAAE,SAAS;IACrBC,SAAS,EAAE,SAAS;IACpBC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE;EACb,CAAC;EACDC,UAAU,EAAE;IACVC,MAAM,EAAE;MACNC,QAAQ,EAAE,SAAS;MACnBC,YAAY,EAAE;IAChB,CAAC;IACDC,IAAI,EAAE;MACJC,UAAU,EAAE,SAAS;MACrBC,iBAAiB,EAAE,SAAS;MAC5BC,kBAAkB,EAAE;IACtB;EACF;AACF,CAAC;AAED,MAAMC,IAAI,GAAGvB,QAAQ,CAACwB,UAAU,CAC9BC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAChC,CAAC;AAEDH,IAAI,CAACI,MAAM,cACTtB,OAAA,CAACN,KAAK,CAAC6B,UAAU;EAAAC,QAAA,eACfxB,OAAA,CAACJ,cAAc;IACb6B,MAAM,EAAE5B,IAAK;IACbI,KAAK,EAAEA,KAAM;IAAAuB,QAAA,eAEbxB,OAAA,CAACF,GAAG;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACD,CACpB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}