{"ast": null, "code": "// This icon file is generated automatically.\nvar RobotFilled = {\n  \"icon\": {\n    \"tag\": \"svg\",\n    \"attrs\": {\n      \"viewBox\": \"64 64 896 896\",\n      \"focusable\": \"false\"\n    },\n    \"children\": [{\n      \"tag\": \"defs\",\n      \"attrs\": {},\n      \"children\": [{\n        \"tag\": \"style\",\n        \"attrs\": {}\n      }]\n    }, {\n      \"tag\": \"path\",\n      \"attrs\": {\n        \"d\": \"M852 64H172c-17.7 0-32 14.3-32 32v660c0 17.7 14.3 32 32 32h680c17.7 0 32-14.3 32-32V96c0-17.7-14.3-32-32-32zM300 328c0-33.1 26.9-60 60-60s60 26.9 60 60-26.9 60-60 60-60-26.9-60-60zm372 248c0 4.4-3.6 8-8 8H360c-4.4 0-8-3.6-8-8v-60c0-4.4 3.6-8 8-8h304c4.4 0 8 3.6 8 8v60zm-8-188c-33.1 0-60-26.9-60-60s26.9-60 60-60 60 26.9 60 60-26.9 60-60 60zm135 476H225c-13.8 0-25 14.3-25 32v56c0 4.4 2.8 8 6.2 8h611.5c3.4 0 6.2-3.6 6.2-8v-56c.1-17.7-11.1-32-24.9-32z\"\n      }\n    }]\n  },\n  \"name\": \"robot\",\n  \"theme\": \"filled\"\n};\nexport default RobotFilled;", "map": {"version": 3, "names": ["RobotFilled"], "sources": ["D:/liu/html/frontend/node_modules/@ant-design/icons-svg/es/asn/RobotFilled.js"], "sourcesContent": ["// This icon file is generated automatically.\nvar RobotFilled = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"defs\", \"attrs\": {}, \"children\": [{ \"tag\": \"style\", \"attrs\": {} }] }, { \"tag\": \"path\", \"attrs\": { \"d\": \"M852 64H172c-17.7 0-32 14.3-32 32v660c0 17.7 14.3 32 32 32h680c17.7 0 32-14.3 32-32V96c0-17.7-14.3-32-32-32zM300 328c0-33.1 26.9-60 60-60s60 26.9 60 60-26.9 60-60 60-60-26.9-60-60zm372 248c0 4.4-3.6 8-8 8H360c-4.4 0-8-3.6-8-8v-60c0-4.4 3.6-8 8-8h304c4.4 0 8 3.6 8 8v60zm-8-188c-33.1 0-60-26.9-60-60s26.9-60 60-60 60 26.9 60 60-26.9 60-60 60zm135 476H225c-13.8 0-25 14.3-25 32v56c0 4.4 2.8 8 6.2 8h611.5c3.4 0 6.2-3.6 6.2-8v-56c.1-17.7-11.1-32-24.9-32z\" } }] }, \"name\": \"robot\", \"theme\": \"filled\" };\nexport default RobotFilled;\n"], "mappings": "AAAA;AACA,IAAIA,WAAW,GAAG;EAAE,MAAM,EAAE;IAAE,KAAK,EAAE,KAAK;IAAE,OAAO,EAAE;MAAE,SAAS,EAAE,eAAe;MAAE,WAAW,EAAE;IAAQ,CAAC;IAAE,UAAU,EAAE,CAAC;MAAE,KAAK,EAAE,MAAM;MAAE,OAAO,EAAE,CAAC,CAAC;MAAE,UAAU,EAAE,CAAC;QAAE,KAAK,EAAE,OAAO;QAAE,OAAO,EAAE,CAAC;MAAE,CAAC;IAAE,CAAC,EAAE;MAAE,KAAK,EAAE,MAAM;MAAE,OAAO,EAAE;QAAE,GAAG,EAAE;MAAsc;IAAE,CAAC;EAAE,CAAC;EAAE,MAAM,EAAE,OAAO;EAAE,OAAO,EAAE;AAAS,CAAC;AAC1tB,eAAeA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}