{"name": "lottery-frontend", "version": "1.0.0", "description": "彩票网站前端应用", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "@types/jest": "^27.5.2", "@types/node": "^16.18.39", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "axios": "^1.4.0", "socket.io-client": "^4.7.2", "react-router-dom": "^6.14.2", "styled-components": "^6.0.7", "@types/styled-components": "^5.1.26", "moment": "^2.29.4", "react-countdown": "^2.3.5", "react-table": "^7.8.0", "@types/react-table": "^7.7.14", "recharts": "^2.7.2", "antd": "^5.7.3", "@ant-design/icons": "^5.2.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src/ --ext .ts,.tsx", "lint:fix": "eslint src/ --ext .ts,.tsx --fix"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/moment": "^2.13.0"}, "proxy": "http://localhost:3001"}