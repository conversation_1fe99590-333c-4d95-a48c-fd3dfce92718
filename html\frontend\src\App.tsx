import React, { useEffect, useState } from 'react';
import { Layout, Menu, Spin, message } from 'antd';
import {
  HomeOutlined,
  BarChartOutlined,
  HistoryOutlined,
  SettingOutlined,
  TrophyOutlined,
  ThunderboltOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { BrowserRouter as Router, Routes, Route, Link, useLocation } from 'react-router-dom';

// 导入页面组件
import HomePage from './pages/HomePage';
import PredictionPage from './pages/PredictionPage';
import HistoryPage from './pages/HistoryPage';
import StatisticsPage from './pages/StatisticsPage';
import SettingsPage from './pages/SettingsPage';

// 导入服务
import { socketService } from './services/socketService';
import { apiService } from './services/apiService';

const { Header, Content, Footer, Sider } = Layout;

// 样式组件
const StyledLayout = styled(Layout)`
  min-height: 100vh;
`;

const StyledHeader = styled(Header)`
  display: flex;
  align-items: center;
  padding: 0 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
`;

const Logo = styled.div`
  color: white;
  font-size: 20px;
  font-weight: bold;
  margin-right: 24px;
  display: flex;
  align-items: center;
  
  .logo-icon {
    margin-right: 8px;
    font-size: 24px;
  }
`;

const StyledContent = styled(Content)`
  margin: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-height: calc(100vh - 200px);
`;

const StyledFooter = styled(Footer)`
  text-align: center;
  background: #f0f2f5;
  color: #666;
`;

const OnlineStatus = styled.div`
  margin-left: auto;
  color: white;
  display: flex;
  align-items: center;
  
  .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #52c41a;
    margin-right: 8px;
    animation: pulse 2s infinite;
  }
`;

// 菜单项配置
const menuItems = [
  {
    key: '/',
    icon: <HomeOutlined />,
    label: <Link to="/">首页</Link>,
  },
  {
    key: '/predictions',
    icon: <ThunderboltOutlined />,
    label: <Link to="/predictions">预测中心</Link>,
  },
  {
    key: '/history',
    icon: <HistoryOutlined />,
    label: <Link to="/history">历史开奖</Link>,
  },
  {
    key: '/statistics',
    icon: <BarChartOutlined />,
    label: <Link to="/statistics">统计分析</Link>,
  },
  {
    key: '/settings',
    icon: <SettingOutlined />,
    label: <Link to="/settings">系统设置</Link>,
  },
];

// 主应用组件
const App: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [collapsed, setCollapsed] = useState(false);
  const [onlineUsers, setOnlineUsers] = useState(0);

  useEffect(() => {
    // 初始化应用
    initializeApp();
    
    // 清理函数
    return () => {
      socketService.disconnect();
    };
  }, []);

  const initializeApp = async () => {
    try {
      // 连接Socket.IO
      socketService.connect();
      
      // 监听在线用户数更新
      socketService.on('online_users_update', (count: number) => {
        setOnlineUsers(count);
      });
      
      // 监听系统消息
      socketService.on('system_message', (data: any) => {
        message.info(data.message);
      });
      
      // 监听连接状态
      socketService.on('connect', () => {
        message.success('连接成功');
      });
      
      socketService.on('disconnect', () => {
        message.warning('连接断开');
      });
      
      // 加载初始配置数据
      await apiService.loadConfigs();
      
      setLoading(false);
    } catch (error) {
      console.error('应用初始化失败:', error);
      message.error('应用初始化失败');
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
      }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <Router>
      <StyledLayout>
        <StyledHeader>
          <Logo>
            <TrophyOutlined className="logo-icon" />
            澳门葡京新彩
          </Logo>
          <OnlineStatus>
            <div className="status-dot" />
            在线用户: {onlineUsers}
          </OnlineStatus>
        </StyledHeader>
        
        <Layout>
          <Sider 
            collapsible 
            collapsed={collapsed} 
            onCollapse={setCollapsed}
            theme="dark"
            width={200}
          >
            <MenuWithLocation />
          </Sider>
          
          <Layout>
            <StyledContent>
              <Routes>
                <Route path="/" element={<HomePage />} />
                <Route path="/predictions" element={<PredictionPage />} />
                <Route path="/history" element={<HistoryPage />} />
                <Route path="/statistics" element={<StatisticsPage />} />
                <Route path="/settings" element={<SettingsPage />} />
              </Routes>
            </StyledContent>
          </Layout>
        </Layout>
        
        <StyledFooter>
          澳门葡京新彩 ©2024 实力打造，火爆全网
        </StyledFooter>
      </StyledLayout>
    </Router>
  );
};

// 带路由位置的菜单组件
const MenuWithLocation: React.FC = () => {
  const location = useLocation();
  
  return (
    <Menu
      theme="dark"
      mode="inline"
      selectedKeys={[location.pathname]}
      items={menuItems}
    />
  );
};

export default App;
