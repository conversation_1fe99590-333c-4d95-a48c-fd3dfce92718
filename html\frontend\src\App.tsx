import React, { useEffect, useState } from 'react';
import styled from 'styled-components';

// 导入组件
import Header from './components/Header';
import LotterySelector from './components/LotterySelector';
import CountdownDisplay from './components/CountdownDisplay';
import LatestResults from './components/LatestResults';
import PredictionSection from './components/PredictionSection';
import ZodiacTable from './components/ZodiacTable';

// 导入服务
import { socketService } from './services/socketService';
import { apiService } from './services/apiService';

// 主容器样式
const AppContainer = styled.div`
  min-height: 100vh;
  background: #f0f2f5;
  font-family: 'Microsoft YaHei', Arial, sans-serif;
`;

// 内容容器
const ContentContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
`;

// 主应用组件
const App: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [currentPeriod, setCurrentPeriod] = useState<any>(null);
  const [latestResults, setLatestResults] = useState<any>(null);
  const [onlineUsers, setOnlineUsers] = useState(0);

  useEffect(() => {
    initializeApp();
    setupSocketListeners();
    
    return () => {
      socketService.disconnect();
    };
  }, []);

  const initializeApp = async () => {
    try {
      // 连接Socket.io
      socketService.connect();

      // 加载配置数据
      await apiService.loadConfigs();

      // 加载初始数据
      const [currentData, resultsData] = await Promise.all([
        apiService.getCurrentPeriod(),
        apiService.getLatestResults(5)
      ]);

      setCurrentPeriod(currentData);
      setLatestResults(resultsData);
      setLoading(false);
    } catch (error) {
      console.error('初始化应用失败:', error);
      setLoading(false);
    }
  };

  const setupSocketListeners = () => {
    socketService.on('userCount', (count: number) => {
      setOnlineUsers(count);
    });

    socketService.on('newResult', (result: any) => {
      setLatestResults(result);
    });

    socketService.on('periodUpdate', (period: any) => {
      setCurrentPeriod(period);
    });
  };

  if (loading) {
    return (
      <AppContainer>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          height: '100vh',
          fontSize: '18px'
        }}>
          加载中...
        </div>
      </AppContainer>
    );
  }

  return (
    <AppContainer>
      <Header onlineUsers={onlineUsers} />
      
      <ContentContainer>
        <LotterySelector />
        <CountdownDisplay currentPeriod={currentPeriod} />
        <LatestResults results={latestResults} />
        <PredictionSection />
        <ZodiacTable />
      </ContentContainer>
    </AppContainer>
  );
};

export default App;
