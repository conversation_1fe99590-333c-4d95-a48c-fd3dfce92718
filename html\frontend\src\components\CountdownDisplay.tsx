import React, { useState, useEffect } from 'react';
import styled from 'styled-components';

interface CountdownDisplayProps {
  currentPeriod: any;
}

const CountdownContainer = styled.div`
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: white;
  padding: 20px;
  border-radius: 8px;
  text-align: center;
  margin: 20px 0;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
`;

const PeriodInfo = styled.div`
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 10px;
`;

const CountdownText = styled.div`
  font-size: 18px;
  margin-bottom: 15px;
`;

const TimeDisplay = styled.div`
  font-size: 32px;
  font-weight: bold;
  font-family: 'Courier New', monospace;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
  letter-spacing: 2px;
`;

const HistoryButton = styled.button`
  background: rgba(255,255,255,0.2);
  border: 2px solid rgba(255,255,255,0.5);
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  margin-top: 15px;
  cursor: pointer;
  font-size: 14px;
  
  &:hover {
    background: rgba(255,255,255,0.3);
  }
`;

const CountdownDisplay: React.FC<CountdownDisplayProps> = ({ currentPeriod }) => {
  const [timeLeft, setTimeLeft] = useState('00:00:00');
  const [currentPeriodNumber, setCurrentPeriodNumber] = useState(188);

  useEffect(() => {
    // 模拟下一期开奖时间（每5分钟一期）
    const getNextDrawTime = () => {
      const now = new Date();
      const nextDraw = new Date(now);
      nextDraw.setMinutes(Math.ceil(now.getMinutes() / 5) * 5, 0, 0);
      if (nextDraw <= now) {
        nextDraw.setMinutes(nextDraw.getMinutes() + 5);
      }
      return nextDraw;
    };

    const timer = setInterval(() => {
      const now = new Date().getTime();
      const drawTime = getNextDrawTime().getTime();
      const difference = drawTime - now;

      if (difference > 0) {
        const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((difference % (1000 * 60)) / 1000);
        const milliseconds = Math.floor((difference % 1000) / 10);

        setTimeLeft(
          `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}:${milliseconds.toString().padStart(2, '0')}`
        );
      } else {
        setTimeLeft('00:00:00:00');
        // 当倒计时结束时，增加期数
        setCurrentPeriodNumber(prev => prev + 1);
      }
    }, 100); // 更频繁更新以显示毫秒

    return () => clearInterval(timer);
  }, []);

  return (
    <CountdownContainer>
      <PeriodInfo>
        澳门葡京新彩第{currentPeriodNumber}期
      </PeriodInfo>
      <CountdownText>
        开奖倒计时:
      </CountdownText>
      <TimeDisplay>
        {timeLeft}
      </TimeDisplay>
      <HistoryButton>
        历史记录
      </HistoryButton>
    </CountdownContainer>
  );
};

export default CountdownDisplay;
