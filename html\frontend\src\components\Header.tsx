import React from 'react';
import styled from 'styled-components';

interface HeaderProps {
  onlineUsers: number;
}

const HeaderContainer = styled.header`
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  padding: 10px 0;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
`;

const TopNav = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #fff;
  
  .nav-links {
    display: flex;
    gap: 20px;
    
    a {
      color: #fff;
      text-decoration: none;
      
      &:hover {
        color: #ffd700;
      }
    }
  }
  
  .return-home {
    color: #ffd700;
  }
`;

const MainHeader = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const Logo = styled.div`
  display: flex;
  align-items: center;

  .logo-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: bold;
    color: #1e3c72;
    margin-right: 15px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
  }

  .logo-text {
    color: #ffd700;
    font-size: 28px;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
  }
`;

const OnlineStatus = styled.div`
  color: #fff;
  font-size: 14px;
  display: flex;
  align-items: center;
  
  .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #52c41a;
    margin-right: 8px;
    animation: pulse 2s infinite;
  }
  
  @keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
  }
`;

const Header: React.FC<HeaderProps> = ({ onlineUsers }) => {
  return (
    <HeaderContainer>
      <TopNav>
        <div className="nav-links">
          <a href="https://681680.com">金财神</a>
          <a href="https://681680.com">澳门49网</a>
          <a href="https://681680.com">诸葛亮</a>
          <a href="https://681680.com">钱多多</a>
          <a href="https://681680.com">管家婆</a>
          <a href="https://681680.com">财神爷</a>
        </div>
        <div className="return-home">返回首页</div>
      </TopNav>
      
      <MainHeader>
        <Logo>
          <div className="logo-icon">澳</div>
          <div className="logo-text">澳门葡京新彩</div>
        </Logo>
        
        <OnlineStatus>
          <div className="status-dot"></div>
          在线用户: {onlineUsers}
        </OnlineStatus>
      </MainHeader>
    </HeaderContainer>
  );
};

export default Header;
