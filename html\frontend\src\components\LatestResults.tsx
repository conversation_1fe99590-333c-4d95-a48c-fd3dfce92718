import React from 'react';
import styled from 'styled-components';

interface LatestResultsProps {
  results: any;
}

const ResultsContainer = styled.div`
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  margin: 20px 0;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
`;

const ResultsHeader = styled.div`
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #333;
`;

const NumbersDisplay = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
`;

const NumberBall = styled.div<{ color: string }>`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 16px;
  background: ${props => {
    switch (props.color) {
      case '红': return 'linear-gradient(135deg, #ff4757, #ff3742)';
      case '蓝': return 'linear-gradient(135deg, #3742fa, #2f3542)';
      case '绿': return 'linear-gradient(135deg, #2ed573, #1e90ff)';
      default: return 'linear-gradient(135deg, #747d8c, #57606f)';
    }
  }};
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
`;

const ZodiacInfo = styled.div`
  font-size: 14px;
  color: #666;
  margin-left: 5px;
`;

const PlusSign = styled.div`
  font-size: 24px;
  font-weight: bold;
  color: #666;
`;

const DateInfo = styled.div`
  font-size: 14px;
  color: #999;
  margin-top: 10px;
`;

const RefreshButton = styled.button`
  background: #1890ff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 10px;
  
  &:hover {
    background: #40a9ff;
  }
`;

// 获取号码对应的颜色
const getNumberColor = (num: number): string => {
  const redNumbers = [1, 2, 7, 8, 12, 13, 18, 19, 23, 24, 29, 30, 34, 35, 40, 45, 46];
  const blueNumbers = [3, 4, 9, 10, 14, 15, 20, 25, 26, 31, 36, 37, 41, 42, 47, 48];
  
  if (redNumbers.includes(num)) return '红';
  if (blueNumbers.includes(num)) return '蓝';
  return '绿';
};

// 获取号码对应的生肖
const getZodiac = (num: number): string => {
  const zodiacMap: { [key: number]: string } = {
    1: '蛇', 2: '龙', 3: '兔', 4: '虎', 5: '牛', 6: '鼠',
    7: '猪', 8: '狗', 9: '鸡', 10: '猴', 11: '羊', 12: '马',
    13: '蛇', 14: '龙', 15: '兔', 16: '虎', 17: '牛', 18: '鼠',
    19: '猪', 20: '狗', 21: '鸡', 22: '猴', 23: '羊', 24: '马',
    25: '蛇', 26: '龙', 27: '兔', 28: '虎', 29: '牛', 30: '鼠',
    31: '猪', 32: '狗', 33: '鸡', 34: '猴', 35: '羊', 36: '马',
    37: '蛇', 38: '龙', 39: '兔', 40: '虎', 41: '牛', 42: '鼠',
    43: '猪', 44: '狗', 45: '鸡', 46: '猴', 47: '羊', 48: '马',
    49: '蛇'
  };
  return zodiacMap[num] || '';
};

const LatestResults: React.FC<LatestResultsProps> = ({ results }) => {
  // 生成模拟的最新开奖结果
  const generateRandomResults = () => {
    const numbers = [];
    for (let i = 0; i < 6; i++) {
      numbers.push(Math.floor(Math.random() * 49) + 1);
    }
    const specialNumber = Math.floor(Math.random() * 49) + 1;
    return { numbers, specialNumber };
  };

  const { numbers, specialNumber } = generateRandomResults();
  const period = 187; // 上一期的结果
  const now = new Date();
  const date = `${now.getFullYear()}年${(now.getMonth() + 1).toString().padStart(2, '0')}月${now.getDate().toString().padStart(2, '0')}日 星期${['日','一','二','三','四','五','六'][now.getDay()]} ${now.getHours().toString().padStart(2, '0')}点${now.getMinutes().toString().padStart(2, '0')}分`;

  return (
    <ResultsContainer>
      <NumbersDisplay>
        {numbers.map((num, index) => (
          <React.Fragment key={index}>
            <NumberBall color={getNumberColor(num)}>
              {num.toString().padStart(2, '0')}
            </NumberBall>
            <ZodiacInfo>
              {getNumberColor(num)}/{getZodiac(num)}
            </ZodiacInfo>
          </React.Fragment>
        ))}
        <PlusSign>+</PlusSign>
        <NumberBall color={getNumberColor(specialNumber)}>
          {specialNumber.toString().padStart(2, '0')}
        </NumberBall>
        <ZodiacInfo>
          {getNumberColor(specialNumber)}/{getZodiac(specialNumber)}
        </ZodiacInfo>
      </NumbersDisplay>
      
      <DateInfo>
        第{period}期 {date}
      </DateInfo>
      
      <RefreshButton>
        手动刷新
      </RefreshButton>
    </ResultsContainer>
  );
};

export default LatestResults;
