import React, { useState } from 'react';
import styled from 'styled-components';

const SelectorContainer = styled.div`
  background: #fff;
  margin: 20px 0;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 10px;
  justify-content: center;
  flex-wrap: wrap;
`;

const LotteryButton = styled.button<{ active?: boolean }>`
  padding: 12px 24px;
  border: 2px solid ${props => props.active ? '#1890ff' : '#d9d9d9'};
  background: ${props => props.active ? '#1890ff' : '#fff'};
  color: ${props => props.active ? '#fff' : '#333'};
  border-radius: 6px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    border-color: #1890ff;
    color: ${props => props.active ? '#fff' : '#1890ff'};
  }
  
  &:active {
    transform: translateY(1px);
  }
`;

const LotterySelector: React.FC = () => {
  const [selectedLottery, setSelectedLottery] = useState('澳门葡京新彩');
  
  const lotteries = [
    '澳门葡京新彩',
    '新澳彩',
    '㊣澳门',
    '香港彩'
  ];

  return (
    <SelectorContainer>
      <ButtonGroup>
        {lotteries.map((lottery) => (
          <LotteryButton
            key={lottery}
            active={selectedLottery === lottery}
            onClick={() => setSelectedLottery(lottery)}
          >
            {lottery}
          </LotteryButton>
        ))}
      </ButtonGroup>
    </SelectorContainer>
  );
};

export default LotterySelector;
