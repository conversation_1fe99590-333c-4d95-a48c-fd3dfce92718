import React from 'react';
import styled from 'styled-components';

const PredictionContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin: 20px 0;
`;

const PredictionCard = styled.div`
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  border-left: 4px solid #1890ff;
`;

const CardTitle = styled.h3`
  color: #1890ff;
  margin: 0 0 15px 0;
  font-size: 18px;
  font-weight: bold;
  text-align: center;
`;

const PredictionItem = styled.div`
  margin: 8px 0;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 4px;
  font-size: 14px;
  
  .period {
    color: #1890ff;
    font-weight: bold;
  }
  
  .content {
    margin-left: 10px;
  }
  
  .result {
    color: #52c41a;
    font-weight: bold;
  }
`;

const HighlightText = styled.span`
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  padding: 2px 6px;
  border-radius: 3px;
  font-weight: bold;
  color: #333;
`;

const PredictionSection: React.FC = () => {
  return (
    <PredictionContainer>
      {/* 一肖一码 */}
      <PredictionCard>
        <CardTitle>【一肖一码】</CardTitle>
        <PredictionItem>
          <span className="period">🇲🇴188期</span>
          <span className="content">必中七肖 <HighlightText>狗猴虎鸡兔羊马</HighlightText></span>
        </PredictionItem>
        <PredictionItem>
          <span className="period">🇲🇴188期</span>
          <span className="content">必中五肖 <HighlightText>狗猴虎鸡兔</HighlightText></span>
        </PredictionItem>
        <PredictionItem>
          <span className="period">🇲🇴188期</span>
          <span className="content">必中三肖 <HighlightText>狗猴虎</HighlightText></span>
        </PredictionItem>
        <PredictionItem>
          <span className="period">🇲🇴188期</span>
          <span className="content">必中二肖 <HighlightText>狗猴</HighlightText></span>
        </PredictionItem>
        <PredictionItem>
          <span className="period">🇲🇴188期</span>
          <span className="content">必中一肖 <HighlightText>狗</HighlightText></span>
        </PredictionItem>
        <PredictionItem>
          <span className="period">🇲🇴188期</span>
          <span className="content">必中8码 <HighlightText>08.32.46.04.45.27.35.12</HighlightText></span>
        </PredictionItem>
      </PredictionCard>

      {/* 输尽光三肖 */}
      <PredictionCard>
        <CardTitle>【输尽光三肖】</CardTitle>
        <PredictionItem>
          <span className="period">🇲🇴188期</span>
          <span className="content">【今期买<HighlightText>猴马蛇</HighlightText>输尽光】开：<span className="result">？ ？</span></span>
        </PredictionItem>
        <PredictionItem>
          <span className="period">🇲🇴187期</span>
          <span className="content">【今期买<HighlightText>蛇猪马</HighlightText>输尽光】开：<span className="result">蛇37</span></span>
        </PredictionItem>
        <PredictionItem>
          <span className="period">🇲🇴186期</span>
          <span className="content">【今期买<HighlightText>猪狗龙</HighlightText>输尽光】开：<span className="result">虎04</span></span>
        </PredictionItem>
        <PredictionItem>
          <span className="period">🇲🇴185期</span>
          <span className="content">【今期买<HighlightText>猴猪牛</HighlightText>输尽光】开：<span className="result">猪07</span></span>
        </PredictionItem>
      </PredictionCard>

      {/* 惊喜24码 */}
      <PredictionCard>
        <CardTitle>【惊喜24码】</CardTitle>
        <PredictionItem>
          <span className="period">🇲🇴 188</span>
          <span className="content">【惊喜24码】开<span className="result">？？</span></span>
        </PredictionItem>
        <PredictionItem>
          <span className="content"><HighlightText>31.43.06.30.10.34.03.15.02.38.21.33</HighlightText></span>
        </PredictionItem>
        <PredictionItem>
          <span className="content"><HighlightText>20.44.16.40.11.23.24.48.05.41.13.37</HighlightText></span>
        </PredictionItem>
        <PredictionItem>
          <span className="period">🇲🇴 187</span>
          <span className="content">【惊喜24码】开<span className="result">蛇37</span></span>
        </PredictionItem>
      </PredictionCard>

      {/* 波色中特 */}
      <PredictionCard>
        <CardTitle>【波色中特】</CardTitle>
        <PredictionItem>
          <span className="period">🇲🇴188期</span>
          <span className="content">【<HighlightText>绿波 红波</HighlightText>】开: <span className="result">？？</span></span>
        </PredictionItem>
        <PredictionItem>
          <span className="period">🇲🇴187期</span>
          <span className="content">【<HighlightText>红波 绿波</HighlightText>】开: <span className="result">蛇37</span></span>
        </PredictionItem>
        <PredictionItem>
          <span className="period">🇲🇴186期</span>
          <span className="content">【<HighlightText>蓝波 红波</HighlightText>】开: <span className="result">虎04</span></span>
        </PredictionItem>
        <PredictionItem>
          <span className="period">🇲🇴185期</span>
          <span className="content">【<HighlightText>红波 绿波</HighlightText>】开: <span className="result">猪07</span></span>
        </PredictionItem>
      </PredictionCard>

      {/* 三头中特 */}
      <PredictionCard>
        <CardTitle>【三头中特】</CardTitle>
        <PredictionItem>
          <span className="period">🇲🇴188期</span>
          <span className="content">3头中特【<HighlightText>1-2-4</HighlightText>】开：<span className="result">？？</span></span>
        </PredictionItem>
        <PredictionItem>
          <span className="period">🇲🇴187期</span>
          <span className="content">3头中特【<HighlightText>1-3-4</HighlightText>】开：<span className="result">蛇37</span></span>
        </PredictionItem>
        <PredictionItem>
          <span className="period">🇲🇴186期</span>
          <span className="content">3头中特【<HighlightText>1-2-3</HighlightText>】开：<span className="result">虎04</span></span>
        </PredictionItem>
      </PredictionCard>

      {/* 单双中特 */}
      <PredictionCard>
        <CardTitle>【单双中特】</CardTitle>
        <PredictionItem>
          <span className="period">🇲🇴188期</span>
          <span className="content">【单双中特】(<HighlightText>单数</HighlightText>）开：<span className="result">？？</span></span>
        </PredictionItem>
        <PredictionItem>
          <span className="period">🇲🇴187期</span>
          <span className="content">【单双中特】(<HighlightText>双数</HighlightText>）开：<span className="result">蛇37</span></span>
        </PredictionItem>
      </PredictionCard>
    </PredictionContainer>
  );
};

export default PredictionSection;
