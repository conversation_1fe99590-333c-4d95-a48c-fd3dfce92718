import React from 'react';
import styled from 'styled-components';

const ZodiacContainer = styled.div`
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
`;

const SectionTitle = styled.h2`
  color: #1890ff;
  text-align: center;
  margin-bottom: 20px;
  font-size: 24px;
`;

const ZodiacGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 30px;
`;

const ZodiacItem = styled.div`
  display: flex;
  align-items: center;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #1890ff;
  
  .zodiac-name {
    font-weight: bold;
    color: #1890ff;
    margin-right: 10px;
    min-width: 30px;
  }
  
  .zodiac-numbers {
    color: #333;
    font-size: 14px;
  }
`;

const ColorSection = styled.div`
  margin: 20px 0;
`;

const ColorTitle = styled.h3`
  color: #333;
  margin-bottom: 10px;
`;

const ColorRow = styled.div`
  display: flex;
  align-items: center;
  margin: 8px 0;
  
  .color-label {
    font-weight: bold;
    margin-right: 10px;
    min-width: 60px;
  }
  
  .red { color: #ff4d4f; }
  .blue { color: #1890ff; }
  .green { color: #52c41a; }
`;

const AttributeTable = styled.table`
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
  
  th, td {
    border: 1px solid #d9d9d9;
    padding: 8px 12px;
    text-align: left;
  }
  
  th {
    background: #f0f2f5;
    font-weight: bold;
    color: #333;
  }
  
  td {
    font-size: 14px;
  }
  
  .attribute-label {
    font-weight: bold;
    color: #1890ff;
  }
`;

const ZodiacTable: React.FC = () => {
  const zodiacData = [
    { name: '蛇', numbers: '[冲猪] 01 13 25 37 49' },
    { name: '龙', numbers: '[冲狗] 02 14 26 38' },
    { name: '兔', numbers: '[冲鸡] 03 15 27 39' },
    { name: '虎', numbers: '[冲猴] 04 16 28 40' },
    { name: '牛', numbers: '[冲羊] 05 17 29 41' },
    { name: '鼠', numbers: '[冲马] 06 18 30 42' },
    { name: '猪', numbers: '[冲蛇] 07 19 31 43' },
    { name: '狗', numbers: '[冲龙] 08 20 32 44' },
    { name: '鸡', numbers: '[冲兔] 09 21 33 45' },
    { name: '猴', numbers: '[冲虎] 10 22 34 46' },
    { name: '羊', numbers: '[冲牛] 11 23 35 47' },
    { name: '马', numbers: '[冲鼠] 12 24 36 48' }
  ];

  return (
    <ZodiacContainer>
      <SectionTitle>2025蛇年（十二生肖号码对照）</SectionTitle>
      
      <ZodiacGrid>
        {zodiacData.map((zodiac) => (
          <ZodiacItem key={zodiac.name}>
            <div className="zodiac-name">{zodiac.name}</div>
            <div className="zodiac-numbers">{zodiac.numbers}</div>
          </ZodiacItem>
        ))}
      </ZodiacGrid>

      <ColorSection>
        <ColorTitle>波色</ColorTitle>
        <ColorRow>
          <span className="color-label red">红波</span>
          <span>01 02 07 08 12 13 18 19 23 24 29 30 34 35 40 45 46</span>
        </ColorRow>
        <ColorRow>
          <span className="color-label blue">蓝波</span>
          <span>03 04 09 10 14 15 20 25 26 31 36 37 41 42 47 48</span>
        </ColorRow>
        <ColorRow>
          <span className="color-label green">绿波</span>
          <span>05 06 11 16 17 21 22 27 28 32 33 38 39 43 44 49</span>
        </ColorRow>
      </ColorSection>

      <AttributeTable>
        <thead>
          <tr>
            <th>分类</th>
            <th>生肖</th>
            <th>分类</th>
            <th>生肖</th>
            <th>分类</th>
            <th>生肖</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td className="attribute-label">家禽:</td>
            <td>牛、马、羊、鸡、狗、猪</td>
            <td className="attribute-label">吉美:</td>
            <td>兔、龙、蛇、马、羊、鸡</td>
            <td className="attribute-label">阴性:</td>
            <td>鼠、龙、蛇、马、狗、猪</td>
          </tr>
          <tr>
            <td className="attribute-label">野兽:</td>
            <td>鼠、虎、兔、龙、蛇、猴</td>
            <td className="attribute-label">凶丑:</td>
            <td>鼠、牛、虎、猴、狗、猪</td>
            <td className="attribute-label">阳性:</td>
            <td>牛、虎、兔、羊、猴、鸡</td>
          </tr>
          <tr>
            <td className="attribute-label">单笔:</td>
            <td>鼠、龙、马、蛇、鸡、猪</td>
            <td className="attribute-label">天肖:</td>
            <td>兔、马、猴、猪、牛、龙</td>
            <td className="attribute-label">白边:</td>
            <td>鼠、牛、虎、鸡、狗、猪</td>
          </tr>
          <tr>
            <td className="attribute-label">双笔:</td>
            <td>虎、猴、狗、兔、羊、牛</td>
            <td className="attribute-label">地肖:</td>
            <td>蛇、羊、鸡、狗、鼠、虎</td>
            <td className="attribute-label">黑中:</td>
            <td>兔、龙、蛇、马、羊、猴</td>
          </tr>
          <tr>
            <td className="attribute-label">女肖:</td>
            <td>兔、蛇、羊、鸡、猪（五宫肖）</td>
            <td className="attribute-label">三合:</td>
            <td>鼠龙猴、牛蛇鸡、虎马狗、兔羊猪</td>
            <td className="attribute-label">红肖:</td>
            <td>马、兔、鼠、鸡</td>
          </tr>
          <tr>
            <td className="attribute-label">男肖:</td>
            <td>鼠、牛、虎、龙、马、猴、狗</td>
            <td className="attribute-label">六合:</td>
            <td>鼠牛、龙鸡、虎猪、蛇猴、兔狗、马羊</td>
            <td className="attribute-label">蓝肖:</td>
            <td>蛇、虎、猪、猴</td>
          </tr>
        </tbody>
      </AttributeTable>
    </ZodiacContainer>
  );
};

export default ZodiacTable;
