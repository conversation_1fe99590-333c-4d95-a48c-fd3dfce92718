/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    '<PERSON>buntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
  line-height: 1.6;
}

#root {
  height: 100%;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 自定义类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: 8px !important; }
.mb-2 { margin-bottom: 16px !important; }
.mb-3 { margin-bottom: 24px !important; }
.mb-4 { margin-bottom: 32px !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: 8px !important; }
.mt-2 { margin-top: 16px !important; }
.mt-3 { margin-top: 24px !important; }
.mt-4 { margin-top: 32px !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: 8px !important; }
.p-2 { padding: 16px !important; }
.p-3 { padding: 24px !important; }
.p-4 { padding: 32px !important; }

/* 彩票相关样式 */
.lottery-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  color: white;
  font-weight: bold;
  font-size: 14px;
  margin: 0 4px;
}

.lottery-number.red {
  background: linear-gradient(135deg, #ff4757, #ff3742);
}

.lottery-number.blue {
  background: linear-gradient(135deg, #3742fa, #2f3542);
}

.lottery-number.green {
  background: linear-gradient(135deg, #2ed573, #1e90ff);
}

.lottery-number.special {
  width: 36px;
  height: 36px;
  font-size: 16px;
  border: 2px solid #ffd700;
  box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

/* 生肖样式 */
.zodiac-item {
  display: inline-block;
  padding: 4px 8px;
  margin: 2px;
  background: #f0f0f0;
  border-radius: 4px;
  font-size: 12px;
  color: #666;
}

.zodiac-item.active {
  background: #1890ff;
  color: white;
}

/* 倒计时样式 */
.countdown {
  font-family: 'Courier New', monospace;
  font-weight: bold;
  color: #ff4757;
}

.countdown.large {
  font-size: 24px;
}

.countdown.medium {
  font-size: 18px;
}

.countdown.small {
  font-size: 14px;
}

/* 预测卡片样式 */
.prediction-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.prediction-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.prediction-title {
  font-size: 16px;
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 8px;
}

.prediction-content {
  color: #666;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .lottery-number {
    width: 28px;
    height: 28px;
    font-size: 12px;
    margin: 0 2px;
  }
  
  .lottery-number.special {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }
  
  .prediction-card {
    padding: 12px;
    margin-bottom: 12px;
  }
  
  .countdown.large {
    font-size: 20px;
  }
  
  .countdown.medium {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .lottery-number {
    width: 24px;
    height: 24px;
    font-size: 10px;
    margin: 0 1px;
  }
  
  .lottery-number.special {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }
  
  .countdown.large {
    font-size: 18px;
  }
}

/* 动画效果 */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.pulse {
  animation: pulse 2s infinite;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.5s ease-out;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* 错误状态 */
.error-container {
  text-align: center;
  padding: 40px;
  color: #ff4757;
}

/* 空状态 */
.empty-container {
  text-align: center;
  padding: 40px;
  color: #999;
}
