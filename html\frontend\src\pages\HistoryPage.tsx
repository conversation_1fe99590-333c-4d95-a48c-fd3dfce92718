import React, { useState, useEffect } from 'react';
import { Table, Typography, Spin, message, Tag } from 'antd';
import { HistoryOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import { apiService } from '../services/apiService';

const { Title } = Typography;

const LotteryNumber = styled.span<{ color: string }>`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  color: white;
  font-weight: bold;
  font-size: 12px;
  margin: 0 2px;
  background: ${props => {
    switch (props.color) {
      case '红': return 'linear-gradient(135deg, #ff4757, #ff3742)';
      case '蓝': return 'linear-gradient(135deg, #3742fa, #2f3542)';
      case '绿': return 'linear-gradient(135deg, #2ed573, #1e90ff)';
      default: return 'linear-gradient(135deg, #747d8c, #57606f)';
    }
  }};
  
  &.special {
    width: 32px;
    height: 32px;
    font-size: 14px;
    border: 2px solid #ffd700;
  }
`;

const HistoryPage: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [results, setResults] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });

  useEffect(() => {
    loadResults(1, 20);
  }, []);

  const loadResults = async (page: number, limit: number) => {
    try {
      setLoading(true);
      const response = await apiService.getLotteryResults(page, limit);
      setResults(response.results);
      setPagination({
        current: response.pagination.page,
        pageSize: response.pagination.limit,
        total: response.pagination.total,
      });
      setLoading(false);
    } catch (error) {
      console.error('加载历史数据失败:', error);
      message.error('加载历史数据失败');
      setLoading(false);
    }
  };

  const handleTableChange = (paginationInfo: any) => {
    loadResults(paginationInfo.current, paginationInfo.pageSize);
  };

  const renderNumbers = (record: any) => {
    const numbers = [
      record.number_1, record.number_2, record.number_3,
      record.number_4, record.number_5, record.number_6
    ];

    return (
      <div style={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap' }}>
        {numbers.map((num: number, index: number) => {
          const color = apiService.getColorByNumberFromCache(num) || '绿';
          return (
            <LotteryNumber key={index} color={color}>
              {num.toString().padStart(2, '0')}
            </LotteryNumber>
          );
        })}
        <LotteryNumber 
          color={apiService.getColorByNumberFromCache(record.special_number) || '绿'} 
          className="special"
        >
          {record.special_number.toString().padStart(2, '0')}
        </LotteryNumber>
      </div>
    );
  };

  const renderZodiacs = (record: any) => {
    const zodiacs = [
      record.zodiac_1, record.zodiac_2, record.zodiac_3,
      record.zodiac_4, record.zodiac_5, record.zodiac_6
    ];

    return (
      <div>
        {zodiacs.map((zodiac: string, index: number) => (
          <Tag key={index} color="blue" style={{ margin: '2px' }}>
            {zodiac}
          </Tag>
        ))}
        <Tag color="gold" style={{ margin: '2px' }}>
          {record.special_zodiac}
        </Tag>
      </div>
    );
  };

  const columns = [
    {
      title: '期数',
      dataIndex: ['period', 'period_number'],
      key: 'period_number',
      width: 100,
      render: (text: string) => <strong>{text}</strong>,
    },
    {
      title: '开奖号码',
      key: 'numbers',
      width: 300,
      render: (record: any) => renderNumbers(record),
    },
    {
      title: '生肖',
      key: 'zodiacs',
      width: 200,
      render: (record: any) => renderZodiacs(record),
    },
    {
      title: '开奖时间',
      dataIndex: ['period', 'draw_time'],
      key: 'draw_time',
      width: 180,
      render: (text: string) => new Date(text).toLocaleString('zh-CN'),
    },
  ];

  return (
    <div>
      <Title level={2}>
        <HistoryOutlined /> 历史开奖
      </Title>
      
      <Table
        columns={columns}
        dataSource={results}
        loading={loading}
        pagination={{
          ...pagination,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => 
            `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
        }}
        onChange={handleTableChange}
        rowKey={(record: any) => record.id}
        scroll={{ x: 800 }}
      />
    </div>
  );
};

export default HistoryPage;
