import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Statistic, Typography, Divider, Spin, message } from 'antd';
import { ClockCircleOutlined, TrophyOutlined, FireOutlined, ThunderboltOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import { socketService } from '../services/socketService';
import { apiService } from '../services/apiService';

const { Title, Text } = Typography;

// 样式组件
const StyledCard = styled(Card)`
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  
  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
    transition: all 0.3s ease;
  }
`;

const CountdownCard = styled(StyledCard)`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  
  .ant-card-body {
    text-align: center;
    padding: 24px;
  }
`;

const LotteryNumber = styled.div<{ color: string }>`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  color: white;
  font-weight: bold;
  font-size: 16px;
  margin: 0 6px;
  background: ${props => {
    switch (props.color) {
      case '红': return 'linear-gradient(135deg, #ff4757, #ff3742)';
      case '蓝': return 'linear-gradient(135deg, #3742fa, #2f3542)';
      case '绿': return 'linear-gradient(135deg, #2ed573, #1e90ff)';
      default: return 'linear-gradient(135deg, #747d8c, #57606f)';
    }
  }};
  
  &.special {
    width: 48px;
    height: 48px;
    font-size: 18px;
    border: 2px solid #ffd700;
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
  }
`;

const CountdownDisplay = styled.div`
  font-size: 32px;
  font-weight: bold;
  font-family: 'Courier New', monospace;
  margin: 16px 0;
  color: #ffd700;
  text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
`;

const PeriodInfo = styled.div`
  font-size: 18px;
  margin-bottom: 16px;
`;

const ResultsContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  margin: 16px 0;
`;

const ZodiacDisplay = styled.div`
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 8px;
  
  .zodiac-item {
    background: rgba(255, 255, 255, 0.2);
    padding: 4px 8px;
    margin: 2px;
    border-radius: 4px;
    font-size: 12px;
  }
`;

interface CurrentPeriodData {
  currentPeriod: any;
  timeUntilDraw: number;
  latestResult: any;
}

const HomePage: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [currentData, setCurrentData] = useState<CurrentPeriodData | null>(null);
  const [countdown, setCountdown] = useState(0);
  const [stats, setStats] = useState<any>(null);

  useEffect(() => {
    loadInitialData();
    setupSocketListeners();
    
    return () => {
      // 清理Socket监听器
      socketService.off('current_period');
      socketService.off('countdown_update');
      socketService.off('new_result');
    };
  }, []);

  useEffect(() => {
    // 倒计时定时器
    const timer = setInterval(() => {
      if (countdown > 0) {
        setCountdown(prev => Math.max(0, prev - 1000));
      }
    }, 1000);

    return () => clearInterval(timer);
  }, [countdown]);

  const loadInitialData = async () => {
    try {
      const [currentPeriod, statsData] = await Promise.all([
        apiService.getCurrentPeriod(),
        apiService.getLotteryStats()
      ]);

      setCurrentData(currentPeriod as CurrentPeriodData);
      setStats(statsData);

      if ((currentPeriod as CurrentPeriodData)?.timeUntilDraw) {
        setCountdown((currentPeriod as CurrentPeriodData).timeUntilDraw);
      }
      
      setLoading(false);
    } catch (error) {
      console.error('加载数据失败:', error);
      message.error('加载数据失败');
      setLoading(false);
    }
  };

  const setupSocketListeners = () => {
    // 监听当前期数更新
    socketService.onCurrentPeriod((data: CurrentPeriodData) => {
      setCurrentData(data);
      if (data.timeUntilDraw) {
        setCountdown(data.timeUntilDraw);
      }
    });

    // 监听倒计时更新
    socketService.onCountdownUpdate((data: any) => {
      setCountdown(data.timeUntilDraw);
    });

    // 监听新开奖结果
    socketService.onNewResult((result: any) => {
      message.success(`第${result.period.period_number}期开奖结果已公布！`);
      loadInitialData(); // 重新加载数据
    });
  };

  const formatCountdown = (milliseconds: number) => {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  const renderLotteryNumbers = (result: any) => {
    if (!result) return null;

    const numbers = [
      result.number_1, result.number_2, result.number_3,
      result.number_4, result.number_5, result.number_6
    ];
    
    const zodiacs = [
      result.zodiac_1, result.zodiac_2, result.zodiac_3,
      result.zodiac_4, result.zodiac_5, result.zodiac_6
    ];

    return (
      <div>
        <ResultsContainer>
          {numbers.map((num: number, index: number) => {
            const color = apiService.getColorByNumberFromCache(num) || '绿';
            return (
              <LotteryNumber key={index} color={color}>
                {num.toString().padStart(2, '0')}
              </LotteryNumber>
            );
          })}
          <LotteryNumber color={apiService.getColorByNumberFromCache(result.special_number) || '绿'} className="special">
            {result.special_number.toString().padStart(2, '0')}
          </LotteryNumber>
        </ResultsContainer>
        
        <ZodiacDisplay>
          {zodiacs.map((zodiac: string, index: number) => (
            <span key={index} className="zodiac-item">{zodiac}</span>
          ))}
          <span className="zodiac-item special">{result.special_zodiac}</span>
        </ZodiacDisplay>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="loading-container">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div>
      <Title level={2}>
        <TrophyOutlined /> 澳门葡京新彩
      </Title>
      <Text type="secondary">实力打造，火爆全网</Text>
      
      <Divider />

      <Row gutter={[16, 16]}>
        {/* 倒计时卡片 */}
        <Col xs={24} lg={12}>
          <CountdownCard>
            <ClockCircleOutlined style={{ fontSize: 24, marginBottom: 16 }} />
            <PeriodInfo>
              第 {currentData?.currentPeriod?.period_number || '---'} 期
            </PeriodInfo>
            <CountdownDisplay>
              {countdown > 0 ? formatCountdown(countdown) : '开奖中...'}
            </CountdownDisplay>
            <Text style={{ color: 'rgba(255, 255, 255, 0.8)' }}>
              距离开奖还有
            </Text>
          </CountdownCard>
        </Col>

        {/* 最新开奖结果 */}
        <Col xs={24} lg={12}>
          <StyledCard title={
            <span>
              <FireOutlined /> 最新开奖结果
            </span>
          }>
            {currentData?.latestResult ? (
              <div>
                <Text strong>
                  第 {currentData.latestResult.period.period_number} 期
                </Text>
                {renderLotteryNumbers(currentData.latestResult)}
              </div>
            ) : (
              <Text type="secondary">暂无开奖结果</Text>
            )}
          </StyledCard>
        </Col>

        {/* 统计信息 */}
        <Col xs={24}>
          <StyledCard title={
            <span>
              <ThunderboltOutlined /> 统计信息
            </span>
          }>
            <Row gutter={16}>
              <Col xs={12} sm={6}>
                <Statistic
                  title="总期数"
                  value={stats?.totalPeriods || 0}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Col>
              <Col xs={12} sm={6}>
                <Statistic
                  title="已开奖"
                  value={stats?.drawnPeriods || 0}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Col>
              <Col xs={12} sm={6}>
                <Statistic
                  title="待开奖"
                  value={stats?.pendingPeriods || 0}
                  valueStyle={{ color: '#faad14' }}
                />
              </Col>
              <Col xs={12} sm={6}>
                <Statistic
                  title="在线用户"
                  value={0}
                  valueStyle={{ color: '#722ed1' }}
                />
              </Col>
            </Row>
          </StyledCard>
        </Col>
      </Row>
    </div>
  );
};

export default HomePage;
