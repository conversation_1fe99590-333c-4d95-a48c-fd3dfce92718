import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Typography, Spin, message, Tabs, Tag } from 'antd';
import { ThunderboltOutlined, TrophyOutlined, FireOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import { apiService } from '../services/apiService';
import { socketService } from '../services/socketService';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

// 样式组件
const StyledCard = styled(Card)`
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  
  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
    transition: all 0.3s ease;
  }
`;

const PredictionCard = styled(StyledCard)`
  .prediction-title {
    font-size: 16px;
    font-weight: bold;
    color: #1890ff;
    margin-bottom: 8px;
  }
  
  .prediction-content {
    color: #666;
    line-height: 1.6;
  }
  
  .accuracy-rate {
    margin-top: 8px;
    text-align: right;
  }
`;

const PredictionData = styled.div`
  margin: 12px 0;
  
  .data-item {
    margin: 8px 0;
    padding: 8px 12px;
    background: #f5f5f5;
    border-radius: 4px;
    border-left: 4px solid #1890ff;
  }
  
  .data-label {
    font-weight: bold;
    color: #333;
    margin-right: 8px;
  }
  
  .data-value {
    color: #666;
  }
`;

const CodeList = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-top: 8px;
  
  .code-item {
    background: #1890ff;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
  }
`;

const ZodiacList = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-top: 8px;
  
  .zodiac-item {
    background: #52c41a;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 12px;
  }
`;

const PredictionPage: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [predictions, setPredictions] = useState<any[]>([]);
  const [currentPeriod, setCurrentPeriod] = useState<any>(null);

  useEffect(() => {
    loadPredictions();
    setupSocketListeners();
    
    return () => {
      socketService.off('prediction_update');
      socketService.off('current_period');
    };
  }, []);

  const loadPredictions = async () => {
    try {
      const [predictionsData, currentData] = await Promise.all([
        apiService.getCurrentPredictions(),
        apiService.getCurrentPeriod()
      ]);
      
      setPredictions(predictionsData);
      setCurrentPeriod(currentData.currentPeriod);
      setLoading(false);
    } catch (error) {
      console.error('加载预测数据失败:', error);
      message.error('加载预测数据失败');
      setLoading(false);
    }
  };

  const setupSocketListeners = () => {
    socketService.onPredictionUpdate((prediction: any) => {
      message.info('预测数据已更新');
      loadPredictions();
    });

    socketService.onCurrentPeriod((data: any) => {
      setCurrentPeriod(data.currentPeriod);
    });
  };

  const renderPredictionContent = (prediction: any) => {
    const { prediction_type, prediction_data } = prediction;
    
    switch (prediction_type) {
      case 'one_zodiac_one_code':
        return (
          <PredictionData>
            <div className="data-item">
              <span className="data-label">七肖:</span>
              <span className="data-value">{prediction_data.seven_zodiacs}</span>
            </div>
            <div className="data-item">
              <span className="data-label">五肖:</span>
              <span className="data-value">{prediction_data.five_zodiacs}</span>
            </div>
            <div className="data-item">
              <span className="data-label">三肖:</span>
              <span className="data-value">{prediction_data.three_zodiacs}</span>
            </div>
            <div className="data-item">
              <span className="data-label">一肖:</span>
              <span className="data-value">{prediction_data.one_zodiac}</span>
            </div>
            <div className="data-item">
              <span className="data-label">八码:</span>
              <CodeList>
                {prediction_data.eight_codes?.map((code: string, index: number) => (
                  <span key={index} className="code-item">{code}</span>
                ))}
              </CodeList>
            </div>
          </PredictionData>
        );
        
      case 'lose_all_three':
        return (
          <PredictionData>
            <div className="data-item">
              <span className="data-label">输尽光:</span>
              <ZodiacList>
                {prediction_data.lose_zodiacs?.map((zodiac: string, index: number) => (
                  <span key={index} className="zodiac-item">{zodiac}</span>
                ))}
              </ZodiacList>
            </div>
            <div className="data-item">
              <span className="data-value">{prediction_data.description}</span>
            </div>
          </PredictionData>
        );
        
      case 'surprise_24_codes':
        return (
          <PredictionData>
            <div className="data-item">
              <span className="data-label">惊喜24码:</span>
              <CodeList>
                {prediction_data.codes?.map((code: string, index: number) => (
                  <span key={index} className="code-item">{code}</span>
                ))}
              </CodeList>
            </div>
          </PredictionData>
        );
        
      case 'color_special':
        return (
          <PredictionData>
            <div className="data-item">
              <span className="data-label">波色中特:</span>
              <span className="data-value">{prediction_data.colors?.join('、')}</span>
            </div>
          </PredictionData>
        );
        
      case 'single_double':
        return (
          <PredictionData>
            <div className="data-item">
              <span className="data-label">单双中特:</span>
              <span className="data-value">{prediction_data.type}</span>
            </div>
          </PredictionData>
        );
        
      default:
        return (
          <PredictionData>
            <div className="data-item">
              <span className="data-value">
                {JSON.stringify(prediction_data, null, 2)}
              </span>
            </div>
          </PredictionData>
        );
    }
  };

  const getPredictionTitle = (type: string) => {
    const titles: { [key: string]: string } = {
      'one_zodiac_one_code': '一肖一码',
      'lose_all_three': '输尽光三肖',
      'surprise_24_codes': '惊喜24码',
      'color_special': '波色中特',
      'three_head_special': '三头中特',
      'single_double': '单双中特',
      'kill_two_zodiac_one_tail': '杀二肖一尾',
      'seven_tail_special': '七尾中特',
      'home_wild_special': '家野中特',
      'big_small_special': '大小中特',
      'flat_one_tail': '平特一尾',
      'idiom_flat_special': '成语出平特',
      'four_zodiac_four_code': '四肖四码',
      'five_element_special': '五行中特',
      'kill_one_door': '杀一门',
      'male_female_special': '男女特肖',
      'home_wild_vs': '家禽VS野兽',
      'black_white_special': '黑白肖中特'
    };
    return titles[type] || type;
  };

  const getAccuracyColor = (rate: number) => {
    if (rate >= 80) return 'success';
    if (rate >= 60) return 'warning';
    return 'error';
  };

  if (loading) {
    return (
      <div className="loading-container">
        <Spin size="large" />
      </div>
    );
  }

  // 按类型分组预测数据
  const groupedPredictions = predictions.reduce((groups: any, prediction: any) => {
    const type = prediction.prediction_type;
    if (!groups[type]) {
      groups[type] = [];
    }
    groups[type].push(prediction);
    return groups;
  }, {});

  return (
    <div>
      <Title level={2}>
        <ThunderboltOutlined /> 预测中心
      </Title>
      <Text type="secondary">
        第 {currentPeriod?.period_number || '---'} 期预测数据
      </Text>
      
      <Row gutter={[16, 16]} style={{ marginTop: 24 }}>
        {Object.entries(groupedPredictions).map(([type, typePredictions]: [string, any]) => (
          <Col xs={24} sm={12} lg={8} key={type}>
            <PredictionCard
              title={
                <div className="prediction-title">
                  <TrophyOutlined /> {getPredictionTitle(type)}
                </div>
              }
              extra={
                <Tag color={getAccuracyColor(typePredictions[0]?.accuracy_rate || 0)}>
                  {(typePredictions[0]?.accuracy_rate || 0).toFixed(1)}%
                </Tag>
              }
            >
              {renderPredictionContent(typePredictions[0])}
              <div className="accuracy-rate">
                <Text type="secondary" style={{ fontSize: 12 }}>
                  准确率: {(typePredictions[0]?.accuracy_rate || 0).toFixed(1)}%
                </Text>
              </div>
            </PredictionCard>
          </Col>
        ))}
      </Row>
      
      {predictions.length === 0 && (
        <div className="empty-container">
          <FireOutlined style={{ fontSize: 48, color: '#ccc', marginBottom: 16 }} />
          <div>暂无预测数据</div>
        </div>
      )}
    </div>
  );
};

export default PredictionPage;
