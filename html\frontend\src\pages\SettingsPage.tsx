import React, { useState, useEffect } from 'react';
import { Card, Typography, Spin, message, Descriptions } from 'antd';
import { SettingOutlined } from '@ant-design/icons';
import { apiService } from '../services/apiService';

const { Title, Text } = Typography;

const SettingsPage: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [configs, setConfigs] = useState<any>(null);

  useEffect(() => {
    loadConfigs();
  }, []);

  const loadConfigs = async () => {
    try {
      const configData = await apiService.getSystemConfig();
      setConfigs(configData);
      setLoading(false);
    } catch (error) {
      console.error('加载配置失败:', error);
      message.error('加载配置失败');
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="loading-container">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div>
      <Title level={2}>
        <SettingOutlined /> 系统设置
      </Title>
      <Text type="secondary">系统配置信息</Text>
      
      <Card title="系统配置" style={{ marginTop: 24 }}>
        <Descriptions column={1}>
          {configs && Object.entries(configs).map(([key, value]: [string, any]) => (
            <Descriptions.Item key={key} label={key}>
              {typeof value === 'object' ? JSON.stringify(value) : String(value)}
            </Descriptions.Item>
          ))}
        </Descriptions>
        
        {!configs && (
          <Text type="secondary">暂无配置数据</Text>
        )}
      </Card>
    </div>
  );
};

export default SettingsPage;
