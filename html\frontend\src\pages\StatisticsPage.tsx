import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Typography, Spin, message } from 'antd';
import { BarChartOutlined } from '@ant-design/icons';
import { apiService } from '../services/apiService';

const { Title, Text } = Typography;

const StatisticsPage: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<any>(null);

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      const statsData = await apiService.getLotteryStats();
      setStats(statsData);
      setLoading(false);
    } catch (error) {
      console.error('加载统计数据失败:', error);
      message.error('加载统计数据失败');
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="loading-container">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div>
      <Title level={2}>
        <BarChartOutlined /> 统计分析
      </Title>
      <Text type="secondary">彩票数据统计分析</Text>
      
      <Row gutter={[16, 16]} style={{ marginTop: 24 }}>
        <Col span={24}>
          <Card title="统计功能开发中">
            <Text>统计分析功能正在开发中，敬请期待...</Text>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default StatisticsPage;
