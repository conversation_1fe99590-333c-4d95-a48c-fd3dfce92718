import axios, { AxiosInstance, AxiosResponse } from 'axios';

// API响应接口
interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
}

// 分页响应接口
interface PaginatedResponse<T> {
  results: T[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
}

class ApiService {
  private api: AxiosInstance;
  private configs: any = {};

  constructor() {
    this.api = axios.create({
      baseURL: process.env.REACT_APP_API_URL || 'http://localhost:3001/api',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // 请求拦截器
    this.api.interceptors.request.use(
      (config) => {
        // 添加认证token
        const token = localStorage.getItem('auth_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.api.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        return response;
      },
      (error) => {
        if (error.response?.status === 401) {
          // 清除过期token
          localStorage.removeItem('auth_token');
          // 可以在这里触发登录页面跳转
        }
        return Promise.reject(error);
      }
    );
  }

  // 通用请求方法
  private async request<T>(method: string, url: string, data?: any): Promise<T> {
    try {
      const response = await this.api.request<ApiResponse<T>>({
        method,
        url,
        data,
      });
      
      if (response.data.success) {
        return response.data.data as T;
      } else {
        throw new Error(response.data.message || '请求失败');
      }
    } catch (error: any) {
      console.error(`API请求失败 [${method} ${url}]:`, error);
      throw error;
    }
  }

  // GET请求
  async get<T>(url: string): Promise<T> {
    return this.request<T>('GET', url);
  }

  // POST请求
  async post<T>(url: string, data?: any): Promise<T> {
    return this.request<T>('POST', url, data);
  }

  // PUT请求
  async put<T>(url: string, data?: any): Promise<T> {
    return this.request<T>('PUT', url, data);
  }

  // DELETE请求
  async delete<T>(url: string): Promise<T> {
    return this.request<T>('DELETE', url);
  }

  // 彩票相关API
  async getCurrentPeriod() {
    return this.get('/lottery/current');
  }

  async getLotteryResults(page = 1, limit = 10) {
    return this.get<PaginatedResponse<any>>(`/lottery/results?page=${page}&limit=${limit}`);
  }

  async getLotteryResult(periodNumber: number) {
    return this.get(`/lottery/results/${periodNumber}`);
  }

  async getLatestResults(count = 5) {
    return this.get(`/lottery/latest/${count}`);
  }

  async getLotteryPeriods(status?: string, page = 1, limit = 20) {
    const params = new URLSearchParams();
    if (status) params.append('status', status);
    params.append('page', page.toString());
    params.append('limit', limit.toString());
    
    return this.get<PaginatedResponse<any>>(`/lottery/periods?${params}`);
  }

  async getLotteryStats() {
    return this.get('/lottery/stats');
  }

  // 预测相关API
  async getCurrentPredictions() {
    return this.get('/predictions/current');
  }

  async getPredictionsByType(type: string, periodId?: number, limit = 10) {
    const params = new URLSearchParams();
    if (periodId) params.append('periodId', periodId.toString());
    params.append('limit', limit.toString());
    
    return this.get(`/predictions/type/${type}?${params}`);
  }

  async getPredictionsByPeriod(periodNumber: number) {
    return this.get(`/predictions/period/${periodNumber}`);
  }

  async getPredictionTypes() {
    return this.get('/predictions/types');
  }

  async getPredictionAccuracy(type?: string) {
    const params = type ? `?type=${type}` : '';
    return this.get(`/predictions/accuracy${params}`);
  }

  // 配置相关API
  async getZodiacConfig() {
    return this.get('/config/zodiac');
  }

  async getColorConfig() {
    return this.get('/config/color');
  }

  async getElementConfig() {
    return this.get('/config/element');
  }

  async getSystemConfig() {
    return this.get('/config/system');
  }

  async getAllConfigs() {
    return this.get('/config/all');
  }

  async getZodiacByNumber(number: number) {
    return this.get(`/config/zodiac/number/${number}`);
  }

  async getNumbersByZodiac(zodiacName: string) {
    return this.get(`/config/zodiac/${zodiacName}/numbers`);
  }

  // 认证相关API
  async login(username: string, password: string) {
    return this.post('/auth/login', { username, password });
  }

  async register(username: string, email: string, password: string) {
    return this.post('/auth/register', { username, email, password });
  }

  async verifyToken() {
    return this.get('/auth/verify');
  }

  async getUserProfile() {
    return this.get('/auth/profile');
  }

  async updateProfile(data: any) {
    return this.put('/auth/profile', data);
  }

  async changePassword(currentPassword: string, newPassword: string) {
    return this.put('/auth/password', { currentPassword, newPassword });
  }

  // 加载配置数据
  async loadConfigs() {
    try {
      this.configs = await this.getAllConfigs();
      return this.configs;
    } catch (error) {
      console.error('加载配置失败:', error);
      throw error;
    }
  }

  // 获取缓存的配置
  getConfigs() {
    return this.configs;
  }

  // 根据号码获取生肖
  getZodiacByNumberFromCache(number: number): string | null {
    const zodiacs = this.configs.zodiacs || [];
    for (const zodiac of zodiacs) {
      if (zodiac.numbers.includes(number)) {
        return zodiac.name;
      }
    }
    return null;
  }

  // 根据号码获取波色
  getColorByNumberFromCache(number: number): string | null {
    const colors = this.configs.colors || [];
    for (const color of colors) {
      if (color.numbers.includes(number)) {
        return color.name;
      }
    }
    return null;
  }

  // 根据号码获取五行
  getElementByNumberFromCache(number: number): string | null {
    const elements = this.configs.elements || [];
    for (const element of elements) {
      if (element.numbers.includes(number)) {
        return element.name;
      }
    }
    return null;
  }
}

// 导出单例
export const apiService = new ApiService();
