import { io, Socket } from 'socket.io-client';

class SocketService {
  private socket: Socket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;

  connect() {
    if (this.socket?.connected) {
      return;
    }

    const serverUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001';
    
    this.socket = io(serverUrl, {
      transports: ['websocket', 'polling'],
      timeout: 20000,
      forceNew: true,
    });

    this.setupEventListeners();
  }

  private setupEventListeners() {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('Socket连接成功');
      this.reconnectAttempts = 0;
      
      // 请求当前期数信息
      this.socket?.emit('request_current_period');
    });

    this.socket.on('disconnect', (reason) => {
      console.log('Socket连接断开:', reason);
      
      if (reason === 'io server disconnect') {
        // 服务器主动断开，需要重新连接
        this.reconnect();
      }
    });

    this.socket.on('connect_error', (error) => {
      console.error('Socket连接错误:', error);
      this.reconnect();
    });

    this.socket.on('reconnect', (attemptNumber) => {
      console.log('Socket重连成功，尝试次数:', attemptNumber);
    });

    this.socket.on('reconnect_error', (error) => {
      console.error('Socket重连失败:', error);
    });

    this.socket.on('reconnect_failed', () => {
      console.error('Socket重连失败，已达到最大尝试次数');
    });
  }

  private reconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('已达到最大重连次数');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
    
    console.log(`${delay}ms后尝试第${this.reconnectAttempts}次重连`);
    
    setTimeout(() => {
      this.connect();
    }, delay);
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  // 监听事件
  on(event: string, callback: (...args: any[]) => void) {
    this.socket?.on(event, callback);
  }

  // 移除事件监听
  off(event: string, callback?: (...args: any[]) => void) {
    this.socket?.off(event, callback);
  }

  // 发送事件
  emit(event: string, data?: any) {
    this.socket?.emit(event, data);
  }

  // 获取连接状态
  get connected() {
    return this.socket?.connected || false;
  }

  // 请求当前期数信息
  requestCurrentPeriod() {
    this.emit('request_current_period');
  }

  // 请求历史开奖结果
  requestHistoryResults(limit = 10) {
    this.emit('request_history_results', { limit });
  }

  // 监听当前期数更新
  onCurrentPeriod(callback: (data: any) => void) {
    this.on('current_period', callback);
  }

  // 监听倒计时更新
  onCountdownUpdate(callback: (data: any) => void) {
    this.on('countdown_update', callback);
  }

  // 监听新开奖结果
  onNewResult(callback: (data: any) => void) {
    this.on('new_result', callback);
  }

  // 监听历史结果
  onHistoryResults(callback: (data: any) => void) {
    this.on('history_results', callback);
  }

  // 监听预测数据更新
  onPredictionUpdate(callback: (data: any) => void) {
    this.on('prediction_update', callback);
  }

  // 监听系统消息
  onSystemMessage(callback: (data: any) => void) {
    this.on('system_message', callback);
  }

  // 监听在线用户数更新
  onOnlineUsersUpdate(callback: (count: number) => void) {
    this.on('online_users_update', callback);
  }

  // 监听错误
  onError(callback: (error: any) => void) {
    this.on('error', callback);
  }
}

// 导出单例
export const socketService = new SocketService();
