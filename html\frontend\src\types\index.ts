// 彩票期数接口
export interface LotteryPeriod {
  id: number;
  period_number: number;
  draw_time: string;
  status: 'pending' | 'drawn' | 'cancelled';
  created_at: string;
  updated_at: string;
}

// 彩票结果接口
export interface LotteryResult {
  id: number;
  period_id: number;
  number_1: number;
  number_2: number;
  number_3: number;
  number_4: number;
  number_5: number;
  number_6: number;
  special_number: number;
  zodiac_1: string;
  zodiac_2: string;
  zodiac_3: string;
  zodiac_4: string;
  zodiac_5: string;
  zodiac_6: string;
  special_zodiac: string;
  created_at: string;
  period: LotteryPeriod;
}

// 预测数据接口
export interface Prediction {
  id: number;
  period_id: number;
  prediction_type: string;
  prediction_data: any;
  accuracy_rate: number;
  is_active: boolean;
  expires_at: string;
  created_at: string;
  updated_at: string;
  period: LotteryPeriod;
}

// 生肖配置接口
export interface ZodiacConfig {
  id: number;
  zodiac_name: string;
  numbers: string;
  year: number;
  created_at: string;
  updated_at: string;
}

// 波色配置接口
export interface ColorConfig {
  id: number;
  color_name: string;
  numbers: string;
  created_at: string;
  updated_at: string;
}

// 五行配置接口
export interface ElementConfig {
  id: number;
  element_name: string;
  numbers: string;
  created_at: string;
  updated_at: string;
}

// 用户接口
export interface User {
  id: number;
  username: string;
  email?: string;
  role: 'user' | 'admin';
  last_login?: string;
  created_at: string;
  updated_at: string;
}

// 系统配置接口
export interface SystemConfig {
  id: number;
  config_key: string;
  config_value: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

// API响应接口
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
}

// 分页响应接口
export interface PaginatedResponse<T> {
  results: T[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
}

// 当前期数数据接口
export interface CurrentPeriodData {
  currentPeriod: LotteryPeriod;
  timeUntilDraw: number;
  latestResult?: LotteryResult;
}

// 统计数据接口
export interface LotteryStats {
  totalPeriods: number;
  drawnPeriods: number;
  pendingPeriods: number;
  numberFrequency: { [key: number]: number };
  zodiacFrequency: { [key: string]: number };
  colorFrequency: { [key: string]: number };
  elementFrequency: { [key: string]: number };
}

// Socket事件接口
export interface SocketEvents {
  // 客户端发送的事件
  request_current_period: () => void;
  request_history_results: (data: { limit: number }) => void;
  
  // 服务器发送的事件
  current_period: (data: CurrentPeriodData) => void;
  countdown_update: (data: { timeUntilDraw: number }) => void;
  new_result: (result: LotteryResult) => void;
  history_results: (results: LotteryResult[]) => void;
  prediction_update: (prediction: Prediction) => void;
  system_message: (data: { message: string; type: string }) => void;
  online_users_update: (count: number) => void;
  error: (error: any) => void;
}

// 预测类型枚举
export enum PredictionType {
  ONE_ZODIAC_ONE_CODE = 'one_zodiac_one_code',
  LOSE_ALL_THREE = 'lose_all_three',
  SURPRISE_24_CODES = 'surprise_24_codes',
  COLOR_SPECIAL = 'color_special',
  THREE_HEAD_SPECIAL = 'three_head_special',
  SINGLE_DOUBLE = 'single_double',
  KILL_TWO_ZODIAC_ONE_TAIL = 'kill_two_zodiac_one_tail',
  SEVEN_TAIL_SPECIAL = 'seven_tail_special',
  HOME_WILD_SPECIAL = 'home_wild_special',
  BIG_SMALL_SPECIAL = 'big_small_special',
  FLAT_ONE_TAIL = 'flat_one_tail',
  IDIOM_FLAT_SPECIAL = 'idiom_flat_special',
  FOUR_ZODIAC_FOUR_CODE = 'four_zodiac_four_code',
  FIVE_ELEMENT_SPECIAL = 'five_element_special',
  KILL_ONE_DOOR = 'kill_one_door',
  MALE_FEMALE_SPECIAL = 'male_female_special',
  HOME_WILD_VS = 'home_wild_vs',
  BLACK_WHITE_SPECIAL = 'black_white_special'
}

// 预测数据类型
export interface PredictionData {
  [PredictionType.ONE_ZODIAC_ONE_CODE]: {
    seven_zodiacs: string;
    five_zodiacs: string;
    three_zodiacs: string;
    one_zodiac: string;
    eight_codes: string[];
  };
  
  [PredictionType.LOSE_ALL_THREE]: {
    lose_zodiacs: string[];
    description: string;
  };
  
  [PredictionType.SURPRISE_24_CODES]: {
    codes: string[];
  };
  
  [PredictionType.COLOR_SPECIAL]: {
    colors: string[];
  };
  
  [PredictionType.SINGLE_DOUBLE]: {
    type: '单' | '双';
  };
  
  // 其他预测类型的数据结构...
}

// 颜色类型
export type LotteryColor = '红' | '蓝' | '绿';

// 生肖类型
export type Zodiac = '鼠' | '牛' | '虎' | '兔' | '龙' | '蛇' | '马' | '羊' | '猴' | '鸡' | '狗' | '猪';

// 五行类型
export type Element = '金' | '木' | '水' | '火' | '土';
