# 澳门葡京新彩应用启动脚本
Write-Host "=== 澳门葡京新彩应用启动 ===" -ForegroundColor Green

# 检查是否已有进程在运行
$backendProcess = Get-Process -Name "node" -ErrorAction SilentlyContinue | Where-Object { $_.CommandLine -like "*src/app.js*" }
$frontendProcess = Get-Process -Name "node" -ErrorAction SilentlyContinue | Where-Object { $_.CommandLine -like "*react-scripts*" }

if ($backendProcess) {
    Write-Host "后端服务已在运行 (PID: $($backendProcess.Id))" -ForegroundColor Yellow
} else {
    Write-Host "启动后端服务..." -ForegroundColor Cyan
    Start-Process -FilePath "powershell" -ArgumentList "-Command", "cd 'html\backend'; npm start" -WindowStyle Minimized
    Start-Sleep -Seconds 3
}

if ($frontendProcess) {
    Write-Host "前端服务已在运行 (PID: $($frontendProcess.Id))" -ForegroundColor Yellow
} else {
    Write-Host "启动前端服务..." -ForegroundColor Cyan
    Start-Process -FilePath "powershell" -ArgumentList "-Command", "cd 'html\frontend'; npm start" -WindowStyle Minimized
    Start-Sleep -Seconds 5
}

Write-Host "`n等待服务启动..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# 测试服务
Write-Host "测试后端API..." -ForegroundColor Cyan
try {
    $response = Invoke-RestMethod -Uri "http://localhost:3001/api/lottery/current" -Method GET -TimeoutSec 10
    Write-Host "✓ 后端API正常运行" -ForegroundColor Green
} catch {
    Write-Host "✗ 后端API测试失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "测试前端服务..." -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3000" -Method GET -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "✓ 前端服务正常运行" -ForegroundColor Green
    }
} catch {
    Write-Host "✗ 前端服务测试失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 应用启动完成 ===" -ForegroundColor Green
Write-Host "前端地址: http://localhost:3000" -ForegroundColor Cyan
Write-Host "后端API: http://localhost:3001" -ForegroundColor Cyan
Write-Host "`n按任意键打开浏览器..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

# 打开浏览器
Start-Process "http://localhost:3000"
