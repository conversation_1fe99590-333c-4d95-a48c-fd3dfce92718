# 澳门葡京新彩应用状态检查
Write-Host "=== 应用状态检查 ===" -ForegroundColor Green

# 检查后端
Write-Host "检查后端服务..." -ForegroundColor Cyan
try {
    $response = Invoke-RestMethod -Uri "http://localhost:3001/health" -TimeoutSec 5
    Write-Host "后端服务正常运行" -ForegroundColor Green
    Write-Host "状态: $($response.status)" -ForegroundColor Gray
} catch {
    Write-Host "后端服务未运行" -ForegroundColor Red
}

# 检查前端
Write-Host "检查前端服务..." -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3000" -TimeoutSec 5
    if ($response.StatusCode -eq 200) {
        Write-Host "前端服务正常运行" -ForegroundColor Green
    }
} catch {
    Write-Host "前端服务未运行" -ForegroundColor Red
}

# 检查进程
Write-Host "检查Node.js进程..." -ForegroundColor Cyan
$processes = Get-Process -Name "node" -ErrorAction SilentlyContinue
if ($processes) {
    Write-Host "发现 $($processes.Count) 个Node.js进程" -ForegroundColor Green
} else {
    Write-Host "未发现Node.js进程" -ForegroundColor Red
}

Write-Host "=== 检查完成 ===" -ForegroundColor Green
Write-Host "前端: http://localhost:3000" -ForegroundColor Cyan
Write-Host "后端: http://localhost:3001" -ForegroundColor Cyan
