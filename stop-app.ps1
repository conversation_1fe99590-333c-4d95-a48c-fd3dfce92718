# 澳门葡京新彩应用停止脚本
Write-Host "=== 停止澳门葡京新彩应用 ===" -ForegroundColor Red

# 查找并停止后端进程
Write-Host "停止后端服务..." -ForegroundColor Yellow
$backendProcesses = Get-Process -Name "node" -ErrorAction SilentlyContinue | Where-Object { 
    $_.CommandLine -like "*src/app.js*" -or $_.CommandLine -like "*backend*"
}

if ($backendProcesses) {
    foreach ($process in $backendProcesses) {
        Write-Host "停止后端进程 PID: $($process.Id)" -ForegroundColor Cyan
        Stop-Process -Id $process.Id -Force
    }
    Write-Host "✓ 后端服务已停止" -ForegroundColor Green
} else {
    Write-Host "未找到运行中的后端进程" -ForegroundColor Yellow
}

# 查找并停止前端进程
Write-Host "停止前端服务..." -ForegroundColor Yellow
$frontendProcesses = Get-Process -Name "node" -ErrorAction SilentlyContinue | Where-Object { 
    $_.CommandLine -like "*react-scripts*" -or $_.CommandLine -like "*frontend*"
}

if ($frontendProcesses) {
    foreach ($process in $frontendProcesses) {
        Write-Host "停止前端进程 PID: $($process.Id)" -ForegroundColor Cyan
        Stop-Process -Id $process.Id -Force
    }
    Write-Host "✓ 前端服务已停止" -ForegroundColor Green
} else {
    Write-Host "未找到运行中的前端进程" -ForegroundColor Yellow
}

# 停止所有相关的PowerShell进程
$psProcesses = Get-Process -Name "powershell" -ErrorAction SilentlyContinue | Where-Object { 
    $_.CommandLine -like "*npm start*"
}

if ($psProcesses) {
    foreach ($process in $psProcesses) {
        Write-Host "停止PowerShell进程 PID: $($process.Id)" -ForegroundColor Cyan
        Stop-Process -Id $process.Id -Force
    }
}

Write-Host "`n=== 应用已完全停止 ===" -ForegroundColor Green
