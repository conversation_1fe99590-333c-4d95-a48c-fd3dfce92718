# 测试应用程序脚本
Write-Host "=== 澳门葡京新彩应用测试 ===" -ForegroundColor Green

# 检查Node.js
Write-Host "检查Node.js..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version
    Write-Host "Node.js版本: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "错误: 未安装Node.js" -ForegroundColor Red
    exit 1
}

# 检查npm
Write-Host "检查npm..." -ForegroundColor Yellow
try {
    $npmVersion = npm --version
    Write-Host "npm版本: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "错误: 未安装npm" -ForegroundColor Red
    exit 1
}

# 测试后端
Write-Host "`n=== 测试后端 ===" -ForegroundColor Cyan
Set-Location "html\backend"

# 安装后端依赖
Write-Host "安装后端依赖..." -ForegroundColor Yellow
npm install

if ($LASTEXITCODE -ne 0) {
    Write-Host "错误: 后端依赖安装失败" -ForegroundColor Red
    Set-Location "..\..\"
    exit 1
}

# 启动后端服务（后台）
Write-Host "启动后端服务..." -ForegroundColor Yellow
Start-Process -FilePath "npm" -ArgumentList "start" -WindowStyle Hidden
Start-Sleep -Seconds 5

# 测试后端API
Write-Host "测试后端API..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "http://localhost:3001/api/lottery/current" -Method GET -TimeoutSec 10
    Write-Host "后端API测试成功" -ForegroundColor Green
} catch {
    Write-Host "警告: 后端API测试失败 - $($_.Exception.Message)" -ForegroundColor Yellow
}

Set-Location "..\..\"

# 测试前端
Write-Host "`n=== 测试前端 ===" -ForegroundColor Cyan
Set-Location "html\frontend"

# 安装前端依赖
Write-Host "安装前端依赖..." -ForegroundColor Yellow
npm install

if ($LASTEXITCODE -ne 0) {
    Write-Host "错误: 前端依赖安装失败" -ForegroundColor Red
    Set-Location "..\..\"
    exit 1
}

# 构建前端
Write-Host "构建前端应用..." -ForegroundColor Yellow
npm run build

if ($LASTEXITCODE -ne 0) {
    Write-Host "错误: 前端构建失败" -ForegroundColor Red
    Set-Location "..\..\"
    exit 1
}

Write-Host "前端构建成功" -ForegroundColor Green
Set-Location "..\..\"

# 检查Docker
Write-Host "`n=== 检查Docker ===" -ForegroundColor Cyan
try {
    $dockerVersion = docker --version
    Write-Host "Docker版本: $dockerVersion" -ForegroundColor Green
    
    # 检查Docker是否运行
    docker ps | Out-Null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Docker正在运行" -ForegroundColor Green
        
        # 尝试构建Docker镜像
        Write-Host "构建Docker镜像..." -ForegroundColor Yellow
        docker-compose build
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "Docker镜像构建成功" -ForegroundColor Green
        } else {
            Write-Host "警告: Docker镜像构建失败" -ForegroundColor Yellow
        }
    } else {
        Write-Host "警告: Docker未运行，请启动Docker Desktop" -ForegroundColor Yellow
    }
} catch {
    Write-Host "警告: Docker未安装或未配置" -ForegroundColor Yellow
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
Write-Host "应用程序已准备就绪！" -ForegroundColor Green
Write-Host "后端地址: http://localhost:3001" -ForegroundColor Cyan
Write-Host "前端地址: http://localhost:3000" -ForegroundColor Cyan
Write-Host "要启动应用程序，请运行:" -ForegroundColor Yellow
Write-Host "  后端: cd html\backend && npm start" -ForegroundColor White
Write-Host "  前端: cd html\frontend && npm start" -ForegroundColor White
Write-Host "  Docker: docker-compose up" -ForegroundColor White
